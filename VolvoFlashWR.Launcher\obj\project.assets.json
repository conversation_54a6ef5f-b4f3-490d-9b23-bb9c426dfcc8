{"version": 3, "targets": {"net8.0-windows7.0": {"HarfBuzzSharp/*******": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "compile": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libHarfBuzzSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libHarfBuzzSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "HidSharp/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/HidSharp.dll": {"related": ".pdb;.XML"}}, "runtime": {"lib/netstandard2.0/HidSharp.dll": {"related": ".pdb;.XML"}}}, "InTheHand.Net.Bluetooth/4.1.40": {"type": "package", "compile": {"lib/net7.0-windows7.0/InTheHand.Net.Bluetooth.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0-windows7.0/InTheHand.Net.Bluetooth.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "compile": {"lib/net8.0-windows7.0/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "3.116.1", "SkiaSharp.Views.WPF": "3.116.1"}, "compile": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MaterialDesignColors/3.1.0": {"type": "package", "compile": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.1.0": {"type": "package", "dependencies": {"MaterialDesignColors": "3.1.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.NET.ILLink.Tasks/8.0.15": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"assetType": "runtime", "rid": "win"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"type": "package", "compile": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "OpenTK/3.3.1": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLWpfControl/3.3.0": {"type": "package", "dependencies": {"OpenTK": "[3.3.0, 3.4.0)"}, "compile": {"lib/net452/GLWpfControl.dll": {"related": ".pdb"}}, "runtime": {"lib/net452/GLWpfControl.dll": {"related": ".pdb"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Ports/9.0.4": {"type": "package", "dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.4", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.4"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/_._": {"assetType": "native", "rid": "osx-x64"}}}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package", "runtimeTargets": {"runtimes/android-arm/native/_._": {"assetType": "native", "rid": "android-arm"}, "runtimes/android-arm64/native/_._": {"assetType": "native", "rid": "android-arm64"}, "runtimes/android-x64/native/_._": {"assetType": "native", "rid": "android-x64"}, "runtimes/android-x86/native/_._": {"assetType": "native", "rid": "android-x86"}, "runtimes/linux-arm/native/_._": {"assetType": "native", "rid": "linux-arm"}, "runtimes/linux-arm64/native/_._": {"assetType": "native", "rid": "linux-arm64"}, "runtimes/linux-bionic-arm64/native/_._": {"assetType": "native", "rid": "linux-bionic-arm64"}, "runtimes/linux-bionic-x64/native/_._": {"assetType": "native", "rid": "linux-bionic-x64"}, "runtimes/linux-musl-arm/native/_._": {"assetType": "native", "rid": "linux-musl-arm"}, "runtimes/linux-musl-arm64/native/_._": {"assetType": "native", "rid": "linux-musl-arm64"}, "runtimes/linux-musl-x64/native/_._": {"assetType": "native", "rid": "linux-musl-x64"}, "runtimes/linux-x64/native/_._": {"assetType": "native", "rid": "linux-x64"}, "runtimes/maccatalyst-arm64/native/_._": {"assetType": "native", "rid": "maccatalyst-arm64"}, "runtimes/maccatalyst-x64/native/_._": {"assetType": "native", "rid": "maccatalyst-x64"}, "runtimes/osx-arm64/native/_._": {"assetType": "native", "rid": "osx-arm64"}, "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib": {"assetType": "native", "rid": "osx-x64"}}}, "SharpCompress/0.37.2": {"type": "package", "dependencies": {"ZstdSharp.Port": "0.8.0"}, "compile": {"lib/net8.0/SharpCompress.dll": {}}, "runtime": {"lib/net8.0/SharpCompress.dll": {}}}, "SkiaSharp/3.116.1": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.116.1", "SkiaSharp.NativeAssets.macOS": "3.116.1"}, "compile": {"ref/net8.0/SkiaSharp.dll": {}}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.HarfBuzz/3.116.1": {"type": "package", "dependencies": {"HarfBuzzSharp": "*******", "SkiaSharp": "3.116.1"}, "compile": {"lib/net8.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"assetType": "native", "rid": "osx"}}}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-arm64"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x64"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"assetType": "native", "rid": "win-x86"}}}, "SkiaSharp.Views.Desktop.Common/3.116.1": {"type": "package", "dependencies": {"SkiaSharp": "3.116.1"}, "compile": {"lib/net8.0/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb"}}}, "SkiaSharp.Views.WPF/3.116.1": {"type": "package", "dependencies": {"OpenTK": "3.3.1", "OpenTK.GLWpfControl": "3.3.0", "SkiaSharp": "3.116.1", "SkiaSharp.Views.Desktop.Common": "3.116.1"}, "compile": {"lib/net462/SkiaSharp.Views.WPF.dll": {"related": ".pdb"}}, "runtime": {"lib/net462/SkiaSharp.Views.WPF.dll": {"related": ".pdb"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.CodeDom/9.0.5": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Device.Gpio/3.2.0": {"type": "package", "compile": {"lib/net6.0/System.Device.Gpio.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Device.Gpio.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/System.Device.Gpio.targets": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll": {"assetType": "runtime", "rid": "win"}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Ports/9.0.4": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "9.0.4"}, "compile": {"lib/net8.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/unix/lib/net8.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/net8.0/System.IO.Ports.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Management/9.0.5": {"type": "package", "dependencies": {"System.CodeDom": "9.0.5"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}, "runtimeTargets": {"runtimes/win/lib/net8.0/System.Management.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Net.NetworkInformation/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.NetworkInformation.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/linux/lib/netstandard1.3/System.Net.NetworkInformation.dll": {"assetType": "runtime", "rid": "linux"}, "runtimes/osx/lib/netstandard1.3/System.Net.NetworkInformation.dll": {"assetType": "runtime", "rid": "osx"}, "runtimes/win/lib/netstandard1.3/System.Net.NetworkInformation.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Net.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {"related": ".xml"}}}, "System.Net.Sockets/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.0/_._": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/_._": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard2.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}, "runtimeTargets": {"runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll": {"assetType": "runtime", "rid": "browser"}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Overlapped/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtimeTargets": {"runtimes/unix/lib/netstandard1.3/System.Threading.Overlapped.dll": {"assetType": "runtime", "rid": "unix"}, "runtimes/win/lib/netstandard1.3/System.Threading.Overlapped.dll": {"assetType": "runtime", "rid": "win"}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Threading.Thread/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.ThreadPool.dll": {}}}, "ZstdSharp.Port/0.8.0": {"type": "package", "compile": {"lib/net8.0/ZstdSharp.dll": {}}, "runtime": {"lib/net8.0/ZstdSharp.dll": {}}}, "VolvoFlashWR.Communication/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"HidSharp": "2.1.0", "InTheHand.Net.Bluetooth": "4.1.40", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Win32.Registry": "5.0.0", "Newtonsoft.Json": "13.0.3", "SharpCompress": "0.37.2", "System.Buffers": "4.5.1", "System.Device.Gpio": "3.2.0", "System.IO.Compression": "4.3.0", "System.IO.Pipelines": "8.0.0", "System.IO.Ports": "9.0.4", "System.Management": "9.0.5", "System.Memory": "4.5.5", "System.Net.NetworkInformation": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "VolvoFlashWR.Core": "1.0.0"}, "compile": {"bin/placeholder/VolvoFlashWR.Communication.dll": {}}, "runtime": {"bin/placeholder/VolvoFlashWR.Communication.dll": {}}}, "VolvoFlashWR.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.5.1", "System.Collections.Immutable": "8.0.0", "System.Management": "9.0.5", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/VolvoFlashWR.Core.dll": {}}, "runtime": {"bin/placeholder/VolvoFlashWR.Core.dll": {}}}, "VolvoFlashWR.UI/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"LiveChartsCore.SkiaSharpView.WPF": "2.0.0-rc5.4", "MaterialDesignThemes": "5.1.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.77", "Newtonsoft.Json": "13.0.3", "System.Threading.Tasks.Extensions": "4.5.4", "VolvoFlashWR.Communication": "1.0.0", "VolvoFlashWR.Core": "1.0.0"}, "compile": {"bin/placeholder/VolvoFlashWR.UI.dll": {}}, "runtime": {"bin/placeholder/VolvoFlashWR.UI.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}}, "net8.0-windows7.0/win-x64": {"HarfBuzzSharp/*******": {"type": "package", "dependencies": {"HarfBuzzSharp.NativeAssets.Win32": "*******", "HarfBuzzSharp.NativeAssets.macOS": "*******"}, "compile": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/HarfBuzzSharp.dll": {"related": ".pdb"}}}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "native": {"runtimes/win-x64/native/libHarfBuzzSharp.dll": {}}}, "HidSharp/2.1.0": {"type": "package", "compile": {"lib/netstandard2.0/HidSharp.dll": {"related": ".pdb;.XML"}}, "runtime": {"lib/netstandard2.0/HidSharp.dll": {"related": ".pdb;.XML"}}}, "InTheHand.Net.Bluetooth/4.1.40": {"type": "package", "compile": {"lib/net7.0-windows7.0/InTheHand.Net.Bluetooth.dll": {"related": ".xml"}}, "runtime": {"lib/net7.0-windows7.0/InTheHand.Net.Bluetooth.dll": {"related": ".xml"}}}, "LiveChartsCore/2.0.0-rc5.4": {"type": "package", "compile": {"lib/net8.0-windows7.0/LiveChartsCore.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/LiveChartsCore.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore": "2.0.0-rc5.4", "SkiaSharp": "2.88.9", "SkiaSharp.HarfBuzz": "2.88.9"}, "compile": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.dll": {"related": ".xml"}}}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc5.4": {"type": "package", "dependencies": {"LiveChartsCore.SkiaSharpView": "2.0.0-rc5.4", "SkiaSharp.HarfBuzz": "3.116.1", "SkiaSharp.Views.WPF": "3.116.1"}, "compile": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.dll": {"related": ".xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "MaterialDesignColors/3.1.0": {"type": "package", "compile": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/MaterialDesignColors.dll": {"related": ".pdb"}}}, "MaterialDesignThemes/5.1.0": {"type": "package", "dependencies": {"MaterialDesignColors": "3.1.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.39"}, "compile": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net8.0/MaterialDesignThemes.Wpf.dll": {"related": ".pdb;.xml"}}, "build": {"build/_._": {}}}, "Microsoft.Extensions.Configuration/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Configuration.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets": {}}}, "Microsoft.Extensions.Options/8.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}, "compile": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Options.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/Microsoft.Extensions.Options.targets": {}}}, "Microsoft.Extensions.Primitives/8.0.0": {"type": "package", "compile": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/Microsoft.Extensions.Primitives.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "Microsoft.NET.ILLink.Tasks/8.0.15": {"type": "package", "build": {"build/Microsoft.NET.ILLink.Tasks.props": {}}}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.Microsoft.Win32.Primitives": "4.3.0"}, "compile": {"ref/netstandard1.3/Microsoft.Win32.Primitives.dll": {"related": ".xml"}}}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll": {"related": ".xml"}}}, "Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"type": "package", "compile": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll": {"related": ".pdb;.xml"}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}, "Newtonsoft.Json/13.0.3": {"type": "package", "compile": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"related": ".xml"}}}, "OpenTK/3.3.1": {"type": "package", "compile": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}, "runtime": {"lib/net20/OpenTK.dll": {"related": ".pdb;.xml"}}}, "OpenTK.GLWpfControl/3.3.0": {"type": "package", "dependencies": {"OpenTK": "[3.3.0, 3.4.0)"}, "compile": {"lib/net452/GLWpfControl.dll": {"related": ".pdb"}}, "runtime": {"lib/net452/GLWpfControl.dll": {"related": ".pdb"}}}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.any.System.Collections/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Collections.dll": {}}}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.Diagnostics.Tracing.dll": {}}}, "runtime.any.System.Globalization/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Globalization.dll": {}}}, "runtime.any.System.IO/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.IO.dll": {}}}, "runtime.any.System.Reflection/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.Reflection.dll": {}}}, "runtime.any.System.Reflection.Primitives/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Reflection.Primitives.dll": {}}}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Resources.ResourceManager.dll": {}}}, "runtime.any.System.Runtime/4.3.0": {"type": "package", "dependencies": {"System.Private.Uri": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.5/System.Runtime.dll": {}}}, "runtime.any.System.Runtime.Handles/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Runtime.Handles.dll": {}}}, "runtime.any.System.Runtime.InteropServices/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.6/System.Runtime.InteropServices.dll": {}}}, "runtime.any.System.Text.Encoding/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Text.Encoding.dll": {}}}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Text.Encoding.Extensions.dll": {}}}, "runtime.any.System.Threading.Tasks/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"lib/netstandard1.3/System.Threading.Tasks.dll": {}}}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.native.System/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "runtime.native.System.IO.Ports/9.0.4": {"type": "package", "dependencies": {"runtime.android-arm.runtime.native.System.IO.Ports": "9.0.4", "runtime.android-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.android-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.android-x86.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-arm.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-bionic-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-musl-arm.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-musl-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-musl-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.linux-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.maccatalyst-x64.runtime.native.System.IO.Ports": "9.0.4", "runtime.osx-arm64.runtime.native.System.IO.Ports": "9.0.4", "runtime.osx-x64.runtime.native.System.IO.Ports": "9.0.4"}}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.4": {"type": "package"}, "runtime.win.Microsoft.Win32.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/win/lib/netstandard1.3/Microsoft.Win32.Primitives.dll": {}}}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"type": "package", "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.Diagnostics.Debug.dll": {}}}, "runtime.win.System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.IO.FileSystem.dll": {}}}, "runtime.win.System.Net.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.Net.Primitives.dll": {}}}, "runtime.win.System.Net.Sockets/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Net.NameResolution": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.Net.Sockets.dll": {}}}, "runtime.win.System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"System.Private.Uri": "4.3.0"}, "compile": {"ref/netstandard/_._": {}}, "runtime": {"runtimes/win/lib/netstandard1.5/System.Runtime.Extensions.dll": {}}}, "SharpCompress/0.37.2": {"type": "package", "dependencies": {"ZstdSharp.Port": "0.8.0"}, "compile": {"lib/net8.0/SharpCompress.dll": {}}, "runtime": {"lib/net8.0/SharpCompress.dll": {}}}, "SkiaSharp/3.116.1": {"type": "package", "dependencies": {"SkiaSharp.NativeAssets.Win32": "3.116.1", "SkiaSharp.NativeAssets.macOS": "3.116.1"}, "compile": {"ref/net8.0/SkiaSharp.dll": {}}, "runtime": {"lib/net8.0/SkiaSharp.dll": {"related": ".pdb"}}}, "SkiaSharp.HarfBuzz/3.116.1": {"type": "package", "dependencies": {"HarfBuzzSharp": "*******", "SkiaSharp": "3.116.1"}, "compile": {"lib/net8.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.HarfBuzz.dll": {"related": ".pdb"}}}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"type": "package", "compile": {"lib/net8.0/_._": {}}, "runtime": {"lib/net8.0/_._": {}}, "native": {"runtimes/win-x64/native/libSkiaSharp.dll": {}}}, "SkiaSharp.Views.Desktop.Common/3.116.1": {"type": "package", "dependencies": {"SkiaSharp": "3.116.1"}, "compile": {"lib/net8.0/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb"}}, "runtime": {"lib/net8.0/SkiaSharp.Views.Desktop.Common.dll": {"related": ".pdb"}}}, "SkiaSharp.Views.WPF/3.116.1": {"type": "package", "dependencies": {"OpenTK": "3.3.1", "OpenTK.GLWpfControl": "3.3.0", "SkiaSharp": "3.116.1", "SkiaSharp.Views.Desktop.Common": "3.116.1"}, "compile": {"lib/net462/SkiaSharp.Views.WPF.dll": {"related": ".pdb"}}, "runtime": {"lib/net462/SkiaSharp.Views.WPF.dll": {"related": ".pdb"}}}, "System.Buffers/4.5.1": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "System.CodeDom/9.0.5": {"type": "package", "compile": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.CodeDom.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Collections/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Collections": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Collections.dll": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Device.Gpio/3.2.0": {"type": "package", "compile": {"lib/net6.0/System.Device.Gpio.dll": {"related": ".xml"}}, "runtime": {"lib/net6.0/System.Device.Gpio.dll": {"related": ".xml"}}, "build": {"buildTransitive/net5.0/System.Device.Gpio.targets": {}}}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Diagnostics.Debug": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Diagnostics.Tracing": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Diagnostics.Tracing.dll": {"related": ".xml"}}}, "System.Globalization/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Globalization": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Globalization.dll": {"related": ".xml"}}}, "System.IO/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.any.System.IO": "4.3.0"}, "compile": {"ref/netstandard1.5/System.IO.dll": {"related": ".xml"}}}, "System.IO.Compression/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Buffers": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.IO.Compression": "4.3.0"}, "compile": {"ref/netstandard1.3/System.IO.Compression.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.IO.Compression.dll": {}}}, "System.IO.FileSystem/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.win.System.IO.FileSystem": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.IO.FileSystem.Primitives.dll": {}}}, "System.IO.Pipelines/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.IO.Pipelines.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.IO.Ports/9.0.4": {"type": "package", "dependencies": {"runtime.native.System.IO.Ports": "9.0.4"}, "compile": {"lib/net8.0/System.IO.Ports.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.IO.Ports.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Linq/4.3.0": {"type": "package", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.6/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.6/System.Linq.dll": {}}}, "System.Management/9.0.5": {"type": "package", "dependencies": {"System.CodeDom": "9.0.5"}, "compile": {"lib/net8.0/System.Management.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/net8.0/System.Management.dll": {"related": ".xml"}}, "build": {"buildTransitive/net8.0/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Net.NameResolution/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.Net.NameResolution.dll": {}}}, "System.Net.NetworkInformation/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.Win32.Primitives": "4.3.0", "System.Collections": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Linq": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Net.Sockets": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Principal.Windows": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Overlapped": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Thread": "4.3.0", "System.Threading.ThreadPool": "4.3.0", "runtime.native.System": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.NetworkInformation.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.Net.NetworkInformation.dll": {}}}, "System.Net.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.win.System.Net.Primitives": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Net.Primitives.dll": {"related": ".xml"}}}, "System.Net.Sockets/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.win.System.Net.Sockets": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Private.Uri/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}, "compile": {"ref/netstandard/_._": {}}}, "System.Reflection/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Reflection.dll": {"related": ".xml"}}}, "System.Reflection.Primitives/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Reflection.Primitives": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Reflection.Primitives.dll": {"related": ".xml"}}}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0", "runtime.any.System.Resources.ResourceManager": "4.3.0"}, "compile": {"ref/netstandard1.0/System.Resources.ResourceManager.dll": {"related": ".xml"}}}, "System.Runtime/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "runtime.any.System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.dll": {"related": ".xml"}}}, "System.Runtime.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.win.System.Runtime.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.5/System.Runtime.Extensions.dll": {"related": ".xml"}}}, "System.Runtime.Handles/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Runtime.Handles.dll": {"related": ".xml"}}}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "runtime.any.System.Runtime.InteropServices": "4.3.0"}, "compile": {"ref/netcoreapp1.1/System.Runtime.InteropServices.dll": {}}}, "System.Security.AccessControl/5.0.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}, "compile": {"ref/netstandard2.0/System.Security.AccessControl.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll": {"related": ".xml"}}}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "compile": {"ref/netcoreapp3.0/System.Security.Principal.Windows.dll": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll": {"related": ".xml"}}}, "System.Text.Encoding/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Text.Encoding": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Text.Encoding.dll": {"related": ".xml"}}}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.any.System.Text.Encoding.Extensions": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}}, "System.Text.Encodings.Web/8.0.0": {"type": "package", "compile": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Encodings.Web.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/_._": {}}}, "System.Text.Json/8.0.0": {"type": "package", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}, "compile": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "runtime": {"lib/net8.0/System.Text.Json.dll": {"related": ".xml"}}, "build": {"buildTransitive/net6.0/System.Text.Json.targets": {}}}, "System.Threading/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.dll": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.dll": {}}}, "System.Threading.Overlapped/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"runtimes/win/lib/netstandard1.3/System.Threading.Overlapped.dll": {}}}, "System.Threading.Tasks/4.3.0": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "runtime.any.System.Threading.Tasks": "4.3.0"}, "compile": {"ref/netstandard1.3/System.Threading.Tasks.dll": {"related": ".xml"}}}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "compile": {"ref/netcoreapp2.1/_._": {}}, "runtime": {"lib/netcoreapp2.1/_._": {}}}, "System.Threading.Thread/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.Thread.dll": {}}}, "System.Threading.ThreadPool/4.3.0": {"type": "package", "dependencies": {"System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}, "compile": {"ref/netstandard1.3/_._": {"related": ".xml"}}, "runtime": {"lib/netstandard1.3/System.Threading.ThreadPool.dll": {}}}, "ZstdSharp.Port/0.8.0": {"type": "package", "compile": {"lib/net8.0/ZstdSharp.dll": {}}, "runtime": {"lib/net8.0/ZstdSharp.dll": {}}}, "VolvoFlashWR.Communication/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"HidSharp": "2.1.0", "InTheHand.Net.Bluetooth": "4.1.40", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Win32.Registry": "5.0.0", "Newtonsoft.Json": "13.0.3", "SharpCompress": "0.37.2", "System.Buffers": "4.5.1", "System.Device.Gpio": "3.2.0", "System.IO.Compression": "4.3.0", "System.IO.Pipelines": "8.0.0", "System.IO.Ports": "9.0.4", "System.Management": "9.0.5", "System.Memory": "4.5.5", "System.Net.NetworkInformation": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "VolvoFlashWR.Core": "1.0.0"}, "compile": {"bin/placeholder/VolvoFlashWR.Communication.dll": {}}, "runtime": {"bin/placeholder/VolvoFlashWR.Communication.dll": {}}}, "VolvoFlashWR.Core/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Newtonsoft.Json": "13.0.3", "System.Buffers": "4.5.1", "System.Collections.Immutable": "8.0.0", "System.Management": "9.0.5", "System.Memory": "4.5.5", "System.Threading.Tasks.Extensions": "4.5.4"}, "compile": {"bin/placeholder/VolvoFlashWR.Core.dll": {}}, "runtime": {"bin/placeholder/VolvoFlashWR.Core.dll": {}}}, "VolvoFlashWR.UI/1.0.0": {"type": "project", "framework": ".NETCoreApp,Version=v8.0", "dependencies": {"LiveChartsCore.SkiaSharpView.WPF": "2.0.0-rc5.4", "MaterialDesignThemes": "5.1.0", "Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Xaml.Behaviors.Wpf": "1.1.77", "Newtonsoft.Json": "13.0.3", "System.Threading.Tasks.Extensions": "4.5.4", "VolvoFlashWR.Communication": "1.0.0", "VolvoFlashWR.Core": "1.0.0"}, "compile": {"bin/placeholder/VolvoFlashWR.UI.dll": {}}, "runtime": {"bin/placeholder/VolvoFlashWR.UI.dll": {}}, "frameworkReferences": ["Microsoft.WindowsDesktop.App.WPF"]}}}, "libraries": {"HarfBuzzSharp/*******": {"sha512": "rwLpl+W6uqu0DuvzqNhTMuFcXfy1Vc0uq0YXgPEmtTSfeUSAye1FcARrm2YIPOSiCBwBOGu3cLvMX5Fp6OKe2g==", "type": "package", "path": "harfbuzzsharp/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "harfbuzzsharp.*******.nupkg.sha512", "harfbuzzsharp.nuspec", "icon.png", "lib/net462/HarfBuzzSharp.dll", "lib/net462/HarfBuzzSharp.pdb", "lib/net6.0/HarfBuzzSharp.dll", "lib/net6.0/HarfBuzzSharp.pdb", "lib/net8.0-android34.0/HarfBuzzSharp.dll", "lib/net8.0-android34.0/HarfBuzzSharp.pdb", "lib/net8.0-android34.0/HarfBuzzSharp.xml", "lib/net8.0-ios17.0/HarfBuzzSharp.dll", "lib/net8.0-ios17.0/HarfBuzzSharp.pdb", "lib/net8.0-maccatalyst17.0/HarfBuzzSharp.dll", "lib/net8.0-maccatalyst17.0/HarfBuzzSharp.pdb", "lib/net8.0-macos14.0/HarfBuzzSharp.dll", "lib/net8.0-macos14.0/HarfBuzzSharp.pdb", "lib/net8.0-tizen7.0/HarfBuzzSharp.dll", "lib/net8.0-tizen7.0/HarfBuzzSharp.pdb", "lib/net8.0-tvos17.0/HarfBuzzSharp.dll", "lib/net8.0-tvos17.0/HarfBuzzSharp.pdb", "lib/net8.0-windows10.0.19041/HarfBuzzSharp.dll", "lib/net8.0-windows10.0.19041/HarfBuzzSharp.pdb", "lib/net8.0/HarfBuzzSharp.dll", "lib/net8.0/HarfBuzzSharp.pdb", "lib/netstandard2.0/HarfBuzzSharp.dll", "lib/netstandard2.0/HarfBuzzSharp.pdb", "lib/netstandard2.1/HarfBuzzSharp.dll", "lib/netstandard2.1/HarfBuzzSharp.pdb"]}, "HarfBuzzSharp.NativeAssets.macOS/*******": {"sha512": "2o6U05LAmK+rwX7TvmJ2X0anXJG2hSE7kHVmCshhHy0tKfByJ5ykBacvhmmooHchlOwq15KBZeROGafCT8nN+g==", "type": "package", "path": "harfbuzzsharp.nativeassets.macos/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/HarfBuzzSharp.NativeAssets.macOS.targets", "harfbuzzsharp.nativeassets.macos.*******.nupkg.sha512", "harfbuzzsharp.nativeassets.macos.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libHarfBuzzSharp.dylib"]}, "HarfBuzzSharp.NativeAssets.Win32/*******": {"sha512": "ow0DtGEUjo65qhiI22of7qiVbN1xDFsZ5P5xJljRmGZ5WSxNy+1batLNJFGxahqhB1MTHYV8kAXf0GqC8WaevQ==", "type": "package", "path": "harfbuzzsharp.nativeassets.win32/*******", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/HarfBuzzSharp.NativeAssets.Win32.targets", "harfbuzzsharp.nativeassets.win32.*******.nupkg.sha512", "harfbuzzsharp.nativeassets.win32.nuspec", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libHarfBuzzSharp.dll", "runtimes/win-x64/native/libHarfBuzzSharp.dll", "runtimes/win-x86/native/libHarfBuzzSharp.dll"]}, "HidSharp/2.1.0": {"sha512": "UTdxWvbgp2xzT1Ajaa2va+Qi3oNHJPasYmVhbKI2VVdu1VYP6yUG+RikhsHvpD7iM0S8e8UYb5Qm/LTWxx9QAA==", "type": "package", "path": "hidsharp/2.1.0", "files": [".nupkg.metadata", ".signature.p7s", "HidSharp.chm", "hidsharp.2.1.0.nupkg.sha512", "hidsharp.nuspec", "lib/net35/HidSharp.XML", "lib/net35/HidSharp.dll", "lib/net35/HidSharp.pdb", "lib/netstandard2.0/HidSharp.XML", "lib/netstandard2.0/HidSharp.dll", "lib/netstandard2.0/HidSharp.pdb"]}, "InTheHand.Net.Bluetooth/4.1.40": {"sha512": "Hx0NqU4M/r/jMp/TGk5RTfCW9CAUYpI4eMWiNbw18E/ClBeHDW3FU0PV6d9SgT4XAUKQtGjok2ByKu1lp3q7pQ==", "type": "package", "path": "inthehand.net.bluetooth/4.1.40", "files": [".nupkg.metadata", ".signature.p7s", "32feet-package-icon.png", "README.md", "inthehand.net.bluetooth.4.1.40.nupkg.sha512", "inthehand.net.bluetooth.nuspec", "lib/monoandroid10.0/InTheHand.Net.Bluetooth.dll", "lib/monoandroid10.0/InTheHand.Net.Bluetooth.xml", "lib/net461/InTheHand.Net.Bluetooth.dll", "lib/net461/InTheHand.Net.Bluetooth.xml", "lib/net6.0-android31.0/InTheHand.Net.Bluetooth.dll", "lib/net6.0-android31.0/InTheHand.Net.Bluetooth.xml", "lib/net6.0-ios16.1/InTheHand.Net.Bluetooth.dll", "lib/net6.0-ios16.1/InTheHand.Net.Bluetooth.xml", "lib/net6.0-windows10.0.22000/InTheHand.Net.Bluetooth.dll", "lib/net6.0-windows10.0.22000/InTheHand.Net.Bluetooth.xml", "lib/net6.0-windows7.0/InTheHand.Net.Bluetooth.dll", "lib/net6.0-windows7.0/InTheHand.Net.Bluetooth.xml", "lib/net6.0/InTheHand.Net.Bluetooth.dll", "lib/net6.0/InTheHand.Net.Bluetooth.xml", "lib/net7.0-android33.0/InTheHand.Net.Bluetooth.dll", "lib/net7.0-android33.0/InTheHand.Net.Bluetooth.xml", "lib/net7.0-ios16.1/InTheHand.Net.Bluetooth.dll", "lib/net7.0-ios16.1/InTheHand.Net.Bluetooth.xml", "lib/net7.0-windows10.0.22000/InTheHand.Net.Bluetooth.dll", "lib/net7.0-windows10.0.22000/InTheHand.Net.Bluetooth.xml", "lib/net7.0-windows7.0/InTheHand.Net.Bluetooth.dll", "lib/net7.0-windows7.0/InTheHand.Net.Bluetooth.xml", "lib/net7.0/InTheHand.Net.Bluetooth.dll", "lib/net7.0/InTheHand.Net.Bluetooth.xml", "lib/netstandard2.0/InTheHand.Net.Bluetooth.dll", "lib/netstandard2.0/InTheHand.Net.Bluetooth.xml", "lib/netstandard2.1/InTheHand.Net.Bluetooth.dll", "lib/netstandard2.1/InTheHand.Net.Bluetooth.xml", "lib/uap10.0.16299/InTheHand.Net.Bluetooth.dll", "lib/uap10.0.16299/InTheHand.Net.Bluetooth.pri", "lib/uap10.0.16299/InTheHand.Net.Bluetooth.xml", "lib/xamarinios10/InTheHand.Net.Bluetooth.dll", "lib/xamarinios10/InTheHand.Net.Bluetooth.xml"]}, "LiveChartsCore/2.0.0-rc5.4": {"sha512": "1hEEvMndEP5urodx+Epu2lwvJr0ZG246FR7jMYtE9/snYwbUKoItq6a4cTzyeyFPi9fcBsjtgwAWA8uL/2s73g==", "type": "package", "path": "livechartscore/2.0.0-rc5.4", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.dll", "lib/net462/LiveChartsCore.xml", "lib/net6.0-windows10.0.19041/LiveChartsCore.dll", "lib/net6.0-windows10.0.19041/LiveChartsCore.xml", "lib/net6.0-windows7.0/LiveChartsCore.dll", "lib/net6.0-windows7.0/LiveChartsCore.xml", "lib/net8.0-android34.0/LiveChartsCore.dll", "lib/net8.0-android34.0/LiveChartsCore.xml", "lib/net8.0-ios18.0/LiveChartsCore.dll", "lib/net8.0-ios18.0/LiveChartsCore.xml", "lib/net8.0-maccatalyst18.0/LiveChartsCore.dll", "lib/net8.0-maccatalyst18.0/LiveChartsCore.xml", "lib/net8.0-windows10.0.19041/LiveChartsCore.dll", "lib/net8.0-windows10.0.19041/LiveChartsCore.xml", "lib/net8.0-windows7.0/LiveChartsCore.dll", "lib/net8.0-windows7.0/LiveChartsCore.xml", "lib/net8.0/LiveChartsCore.dll", "lib/net8.0/LiveChartsCore.xml", "lib/netstandard2.0/LiveChartsCore.dll", "lib/netstandard2.0/LiveChartsCore.xml", "lib/netstandard2.1/LiveChartsCore.dll", "lib/netstandard2.1/LiveChartsCore.xml", "livechartscore.2.0.0-rc5.4.nupkg.sha512", "livechartscore.nuspec"]}, "LiveChartsCore.SkiaSharpView/2.0.0-rc5.4": {"sha512": "lpylapUJHvAagM4pmcwCvx3ObfL2FlITtI0u8LkKlLEnzhJYg17Tcxcgd6R/mItQlCdICG0PFDVNUDnZEwhFuw==", "type": "package", "path": "livechartscore.skiasharpview/2.0.0-rc5.4", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.SkiaSharpView.dll", "lib/net462/LiveChartsCore.SkiaSharpView.xml", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.dll", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-android34.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-android34.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-ios18.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-ios18.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-maccatalyst18.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-maccatalyst18.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-windows10.0.20348/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-windows10.0.20348/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.xml", "lib/net8.0/LiveChartsCore.SkiaSharpView.dll", "lib/net8.0/LiveChartsCore.SkiaSharpView.xml", "lib/netstandard2.0/LiveChartsCore.SkiaSharpView.dll", "lib/netstandard2.0/LiveChartsCore.SkiaSharpView.xml", "lib/netstandard2.1/LiveChartsCore.SkiaSharpView.dll", "lib/netstandard2.1/LiveChartsCore.SkiaSharpView.xml", "livechartscore.skiasharpview.2.0.0-rc5.4.nupkg.sha512", "livechartscore.skiasharpview.nuspec"]}, "LiveChartsCore.SkiaSharpView.WPF/2.0.0-rc5.4": {"sha512": "TXlnj+wbmt4dXq21uFFPRg+lTjFhmMqo1Gqi+IAkNsy/UFB4cyynCmZzVFAgEzfqQ/dJ4ZKwfsXWMqbtI28NHQ==", "type": "package", "path": "livechartscore.skiasharpview.wpf/2.0.0-rc5.4", "files": [".nupkg.metadata", ".signature.p7s", "icon.png", "lib/net462/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/net462/LiveChartsCore.SkiaSharpView.WPF.xml", "lib/net6.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/net6.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WPF.xml", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/net6.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.xml", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/net8.0-windows10.0.19041/LiveChartsCore.SkiaSharpView.WPF.xml", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.dll", "lib/net8.0-windows7.0/LiveChartsCore.SkiaSharpView.WPF.xml", "livechartscore.skiasharpview.wpf.2.0.0-rc5.4.nupkg.sha512", "livechartscore.skiasharpview.wpf.nuspec"]}, "MaterialDesignColors/3.1.0": {"sha512": "J2mpZBWx0wArrMCK8E0Cqfsy+Wh3iRDVnznp5/84B1KcnTKI9u9Pyt2zN0oSQGsa6NhvwdUErbhE3jJd6iRTxw==", "type": "package", "path": "materialdesigncolors/3.1.0", "files": [".nupkg.metadata", ".signature.p7s", "docs/README.md", "images/MaterialDesignColors.Icon.png", "lib/net462/MaterialDesignColors.dll", "lib/net462/MaterialDesignColors.pdb", "lib/net6.0/MaterialDesignColors.dll", "lib/net6.0/MaterialDesignColors.pdb", "lib/net8.0/MaterialDesignColors.dll", "lib/net8.0/MaterialDesignColors.pdb", "materialdesigncolors.3.1.0.nupkg.sha512", "materialdesigncolors.nuspec"]}, "MaterialDesignThemes/5.1.0": {"sha512": "k5lO0NqhExrCLnFWnapI+a2XvOOm/Mwz92GwUq4CvNrahMMPx9puALE8VJlLNeNxUYjWf2+PAKPGpmr1u1QDfg==", "type": "package", "path": "materialdesignthemes/5.1.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/MaterialDesignThemes.targets", "build/Resources/Roboto/Roboto-Black.ttf", "build/Resources/Roboto/Roboto-BlackItalic.ttf", "build/Resources/Roboto/Roboto-Bold.ttf", "build/Resources/Roboto/Roboto-BoldItalic.ttf", "build/Resources/Roboto/Roboto-Italic.ttf", "build/Resources/Roboto/Roboto-Light.ttf", "build/Resources/Roboto/Roboto-LightItalic.ttf", "build/Resources/Roboto/Roboto-Medium.ttf", "build/Resources/Roboto/Roboto-MediumItalic.ttf", "build/Resources/Roboto/Roboto-Regular.ttf", "build/Resources/Roboto/Roboto-Thin.ttf", "build/Resources/Roboto/Roboto-ThinItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Bold.ttf", "build/Resources/Roboto/RobotoCondensed-BoldItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Italic.ttf", "build/Resources/Roboto/RobotoCondensed-Light.ttf", "build/Resources/Roboto/RobotoCondensed-LightItalic.ttf", "build/Resources/Roboto/RobotoCondensed-Regular.ttf", "docs/README.md", "images/MaterialDesignThemes.Icon.png", "lib/net462/MaterialDesignThemes.Wpf.dll", "lib/net462/MaterialDesignThemes.Wpf.pdb", "lib/net462/MaterialDesignThemes.Wpf.xml", "lib/net6.0/MaterialDesignThemes.Wpf.dll", "lib/net6.0/MaterialDesignThemes.Wpf.pdb", "lib/net6.0/MaterialDesignThemes.Wpf.xml", "lib/net8.0/MaterialDesignThemes.Wpf.dll", "lib/net8.0/MaterialDesignThemes.Wpf.pdb", "lib/net8.0/MaterialDesignThemes.Wpf.xml", "materialdesignthemes.5.1.0.nupkg.sha512", "materialdesignthemes.nuspec", "tools/VisualStudioToolsManifest.xml"]}, "Microsoft.Extensions.Configuration/8.0.0": {"sha512": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "type": "package", "path": "microsoft.extensions.configuration/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.targets", "lib/net462/Microsoft.Extensions.Configuration.dll", "lib/net462/Microsoft.Extensions.Configuration.xml", "lib/net6.0/Microsoft.Extensions.Configuration.dll", "lib/net6.0/Microsoft.Extensions.Configuration.xml", "lib/net7.0/Microsoft.Extensions.Configuration.dll", "lib/net7.0/Microsoft.Extensions.Configuration.xml", "lib/net8.0/Microsoft.Extensions.Configuration.dll", "lib/net8.0/Microsoft.Extensions.Configuration.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.xml", "microsoft.extensions.configuration.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/8.0.0": {"sha512": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Abstractions.targets", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net462/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.FileExtensions/8.0.0": {"sha512": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "type": "package", "path": "microsoft.extensions.configuration.fileextensions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.FileExtensions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.FileExtensions.targets", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net462/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net6.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net7.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/net8.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.FileExtensions.xml", "microsoft.extensions.configuration.fileextensions.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.fileextensions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Configuration.Json/8.0.0": {"sha512": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "type": "package", "path": "microsoft.extensions.configuration.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Configuration.Json.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Configuration.Json.targets", "lib/net462/Microsoft.Extensions.Configuration.Json.dll", "lib/net462/Microsoft.Extensions.Configuration.Json.xml", "lib/net6.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net6.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net7.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net7.0/Microsoft.Extensions.Configuration.Json.xml", "lib/net8.0/Microsoft.Extensions.Configuration.Json.dll", "lib/net8.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Json.xml", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.dll", "lib/netstandard2.1/Microsoft.Extensions.Configuration.Json.xml", "microsoft.extensions.configuration.json.8.0.0.nupkg.sha512", "microsoft.extensions.configuration.json.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection/8.0.0": {"sha512": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "type": "package", "path": "microsoft.extensions.dependencyinjection/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/8.0.0": {"sha512": "cjWrLkJXK0rs4zofsK4bSdg+jhDLTaxrkXu4gS6Y7MAlCvRyNNgwY/lJi5RDlQOnSZweHqoyvgvbdvQsRIW+hg==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.DependencyInjection.Abstractions.targets", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net462/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Abstractions/8.0.0": {"sha512": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "type": "package", "path": "microsoft.extensions.fileproviders.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Abstractions.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Abstractions.targets", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net462/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Abstractions.xml", "microsoft.extensions.fileproviders.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileProviders.Physical/8.0.0": {"sha512": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "type": "package", "path": "microsoft.extensions.fileproviders.physical/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileProviders.Physical.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileProviders.Physical.targets", "lib/net462/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net462/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net6.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net7.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/net8.0/Microsoft.Extensions.FileProviders.Physical.xml", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.dll", "lib/netstandard2.0/Microsoft.Extensions.FileProviders.Physical.xml", "microsoft.extensions.fileproviders.physical.8.0.0.nupkg.sha512", "microsoft.extensions.fileproviders.physical.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.FileSystemGlobbing/8.0.0": {"sha512": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ==", "type": "package", "path": "microsoft.extensions.filesystemglobbing/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.FileSystemGlobbing.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.FileSystemGlobbing.targets", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net462/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net6.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net7.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/net8.0/Microsoft.Extensions.FileSystemGlobbing.xml", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.dll", "lib/netstandard2.0/Microsoft.Extensions.FileSystemGlobbing.xml", "microsoft.extensions.filesystemglobbing.8.0.0.nupkg.sha512", "microsoft.extensions.filesystemglobbing.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging/8.0.0": {"sha512": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "type": "package", "path": "microsoft.extensions.logging/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Logging.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.targets", "lib/net462/Microsoft.Extensions.Logging.dll", "lib/net462/Microsoft.Extensions.Logging.xml", "lib/net6.0/Microsoft.Extensions.Logging.dll", "lib/net6.0/Microsoft.Extensions.Logging.xml", "lib/net7.0/Microsoft.Extensions.Logging.dll", "lib/net7.0/Microsoft.Extensions.Logging.xml", "lib/net8.0/Microsoft.Extensions.Logging.dll", "lib/net8.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.8.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Logging.Abstractions/8.0.0": {"sha512": "arDBqTgFCyS0EvRV7O3MZturChstm50OJ0y9bDJvAcmEPJm0FFpFyjU/JLYyStNGGey081DvnQYlncNX5SJJGA==", "type": "package", "path": "microsoft.extensions.logging.abstractions/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn3.11/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.0/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Logging.Generators.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Logging.Generators.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Logging.Generators.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net462/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/net6.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Logging.Abstractions.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.targets", "lib/net462/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net462/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net6.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net7.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net8.0/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.8.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Options/8.0.0": {"sha512": "JOVOfqpnqlVLUzINQ2fox8evY2SKLYJ3BV8QDe/Jyp21u1T7r45x/R/5QdteURMR5r01GxeJSBBUOCOyaNXA3g==", "type": "package", "path": "microsoft.extensions.options/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn4.4/cs/Microsoft.Extensions.Options.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-<PERSON>/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/Microsoft.Extensions.Options.SourceGeneration.resources.dll", "buildTransitive/net461/Microsoft.Extensions.Options.targets", "buildTransitive/net462/Microsoft.Extensions.Options.targets", "buildTransitive/net6.0/Microsoft.Extensions.Options.targets", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Options.targets", "buildTransitive/netstandard2.0/Microsoft.Extensions.Options.targets", "lib/net462/Microsoft.Extensions.Options.dll", "lib/net462/Microsoft.Extensions.Options.xml", "lib/net6.0/Microsoft.Extensions.Options.dll", "lib/net6.0/Microsoft.Extensions.Options.xml", "lib/net7.0/Microsoft.Extensions.Options.dll", "lib/net7.0/Microsoft.Extensions.Options.xml", "lib/net8.0/Microsoft.Extensions.Options.dll", "lib/net8.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.1/Microsoft.Extensions.Options.dll", "lib/netstandard2.1/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.8.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.Extensions.Primitives/8.0.0": {"sha512": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g==", "type": "package", "path": "microsoft.extensions.primitives/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/Microsoft.Extensions.Primitives.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/Microsoft.Extensions.Primitives.targets", "lib/net462/Microsoft.Extensions.Primitives.dll", "lib/net462/Microsoft.Extensions.Primitives.xml", "lib/net6.0/Microsoft.Extensions.Primitives.dll", "lib/net6.0/Microsoft.Extensions.Primitives.xml", "lib/net7.0/Microsoft.Extensions.Primitives.dll", "lib/net7.0/Microsoft.Extensions.Primitives.xml", "lib/net8.0/Microsoft.Extensions.Primitives.dll", "lib/net8.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.8.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NET.ILLink.Tasks/8.0.15": {"sha512": "s4eXlcRGyHeCgFUGQnhq0e/SCHBPp0jOHgMqZg3fQ2OCHJSm1aOUhI6RFWuVIcEb9ig2WgI2kWukk8wu72EbUQ==", "type": "package", "path": "microsoft.net.illink.tasks/8.0.15", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "Sdk/Sdk.props", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/cs/ILLink.CodeFixProvider.dll", "analyzers/dotnet/cs/ILLink.RoslynAnalyzer.dll", "build/Microsoft.NET.ILLink.Analyzers.props", "build/Microsoft.NET.ILLink.Tasks.props", "build/Microsoft.NET.ILLink.targets", "microsoft.net.illink.tasks.8.0.15.nupkg.sha512", "microsoft.net.illink.tasks.nuspec", "tools/net472/ILLink.Tasks.dll", "tools/net472/ILLink.Tasks.dll.config", "tools/net472/Mono.Cecil.Mdb.dll", "tools/net472/Mono.Cecil.Pdb.dll", "tools/net472/Mono.Cecil.Rocks.dll", "tools/net472/Mono.Cecil.dll", "tools/net472/Sdk/Sdk.props", "tools/net472/System.Buffers.dll", "tools/net472/System.Collections.Immutable.dll", "tools/net472/System.Memory.dll", "tools/net472/System.Numerics.Vectors.dll", "tools/net472/System.Reflection.Metadata.dll", "tools/net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/net472/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net472/build/Microsoft.NET.ILLink.Tasks.props", "tools/net472/build/Microsoft.NET.ILLink.targets", "tools/net8.0/ILLink.Tasks.deps.json", "tools/net8.0/ILLink.Tasks.dll", "tools/net8.0/Mono.Cecil.Mdb.dll", "tools/net8.0/Mono.Cecil.Pdb.dll", "tools/net8.0/Mono.Cecil.Rocks.dll", "tools/net8.0/Mono.Cecil.dll", "tools/net8.0/Sdk/Sdk.props", "tools/net8.0/build/Microsoft.NET.ILLink.Analyzers.props", "tools/net8.0/build/Microsoft.NET.ILLink.Tasks.props", "tools/net8.0/build/Microsoft.NET.ILLink.targets", "tools/net8.0/illink.deps.json", "tools/net8.0/illink.dll", "tools/net8.0/illink.runtimeconfig.json", "useSharedDesignerContext.txt"]}, "Microsoft.NETCore.Platforms/5.0.0": {"sha512": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "type": "package", "path": "microsoft.netcore.platforms/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.5.0.0.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/1.1.0": {"sha512": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "type": "package", "path": "microsoft.netcore.targets/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "microsoft.netcore.targets.1.1.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json"]}, "Microsoft.Win32.Primitives/4.3.0": {"sha512": "9ZQKCWxH7Ijp9BfahvL2Zyf1cJIk8XYLF6Yjzr2yi0b2cOut/HQ31qf1ThHAgCc3WiZMdnWcfJCgN82/0UunxA==", "type": "package", "path": "microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/Microsoft.Win32.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.win32.primitives.4.3.0.nupkg.sha512", "microsoft.win32.primitives.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.dll", "ref/netstandard1.3/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/de/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/es/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/it/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Primitives.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._"]}, "Microsoft.Win32.Registry/5.0.0": {"sha512": "dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "type": "package", "path": "microsoft.win32.registry/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.dll", "lib/net461/Microsoft.Win32.Registry.xml", "lib/netstandard1.3/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.dll", "lib/netstandard2.0/Microsoft.Win32.Registry.xml", "microsoft.win32.registry.5.0.0.nupkg.sha512", "microsoft.win32.registry.nuspec", "ref/net46/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.dll", "ref/net461/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/Microsoft.Win32.Registry.dll", "ref/netstandard1.3/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/de/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/es/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/fr/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/it/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ja/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ko/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/ru/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hans/Microsoft.Win32.Registry.xml", "ref/netstandard1.3/zh-hant/Microsoft.Win32.Registry.xml", "ref/netstandard2.0/Microsoft.Win32.Registry.dll", "ref/netstandard2.0/Microsoft.Win32.Registry.xml", "runtimes/win/lib/net46/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.dll", "runtimes/win/lib/net461/Microsoft.Win32.Registry.xml", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.dll", "runtimes/win/lib/netstandard2.0/Microsoft.Win32.Registry.xml", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Xaml.Behaviors.Wpf/1.1.77": {"sha512": "MCu674ZETgU18EbxfwIlRpUPJ02YbZenLsMCXTkpeA7KUBpXfFaOUDlEO+7UWu5AFnUoydg+aQENJkuaZPheMQ==", "type": "package", "path": "microsoft.xaml.behaviors.wpf/1.1.77", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/Design/Microsoft.Xaml.Behaviors.Design.dll", "lib/net462/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net462/Microsoft.Xaml.Behaviors.dll", "lib/net462/Microsoft.Xaml.Behaviors.pdb", "lib/net462/Microsoft.Xaml.Behaviors.xml", "lib/net6.0-windows7.0/Design/Microsoft.Xaml.Behaviors.DesignTools.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.dll", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.pdb", "lib/net6.0-windows7.0/Microsoft.Xaml.Behaviors.xml", "microsoft.xaml.behaviors.wpf.1.1.77.nupkg.sha512", "microsoft.xaml.behaviors.wpf.nuspec", "tools/Install.ps1"]}, "Newtonsoft.Json/13.0.3": {"sha512": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "type": "package", "path": "newtonsoft.json/13.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.md", "README.md", "lib/net20/Newtonsoft.Json.dll", "lib/net20/Newtonsoft.Json.xml", "lib/net35/Newtonsoft.Json.dll", "lib/net35/Newtonsoft.Json.xml", "lib/net40/Newtonsoft.Json.dll", "lib/net40/Newtonsoft.Json.xml", "lib/net45/Newtonsoft.Json.dll", "lib/net45/Newtonsoft.Json.xml", "lib/net6.0/Newtonsoft.Json.dll", "lib/net6.0/Newtonsoft.Json.xml", "lib/netstandard1.0/Newtonsoft.Json.dll", "lib/netstandard1.0/Newtonsoft.Json.xml", "lib/netstandard1.3/Newtonsoft.Json.dll", "lib/netstandard1.3/Newtonsoft.Json.xml", "lib/netstandard2.0/Newtonsoft.Json.dll", "lib/netstandard2.0/Newtonsoft.Json.xml", "newtonsoft.json.13.0.3.nupkg.sha512", "newtonsoft.json.nuspec", "packageIcon.png"]}, "OpenTK/3.3.1": {"sha512": "vJ00JIs0EOEhqU3+1w6ocurNUFaewd7YXV8sixI029AQeQaht0QjDGgXm2Fk/y8A5Wtx6UsmrjJDKi4USoVeSg==", "type": "package", "path": "opentk/3.3.1", "files": [".nupkg.metadata", ".signature.p7s", "content/OpenTK.dll.config", "lib/net20/OpenTK.dll", "lib/net20/OpenTK.pdb", "lib/net20/OpenTK.xml", "opentk.3.3.1.nupkg.sha512", "opentk.nuspec"]}, "OpenTK.GLWpfControl/3.3.0": {"sha512": "viHlwA0RIYLA1EyoA+gOkfqpA9xMZV6HigVYhaHaEsYb3jk518RdCKEdFggu/P4oYUNYZmmHaUYDC9pwarkXjA==", "type": "package", "path": "opentk.glwpfcontrol/3.3.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net452/GLWpfControl.dll", "lib/net452/GLWpfControl.pdb", "opentk.glwpfcontrol.3.3.0.nupkg.sha512", "opentk.glwpfcontrol.nuspec"]}, "runtime.android-arm.runtime.native.System.IO.Ports/9.0.4": {"sha512": "5nNvTbKRkrIYTm48h2n2gPR4Roat2W1ALkThnqeKuGYcnTVMnrAXi0IfLZR3IhIlQ3CfQ4zqjTWFHk3O2/cQDQ==", "type": "package", "path": "runtime.android-arm.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.android-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/libSystem.IO.Ports.Native.so", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-arm64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "zSIn0tjbcEMj8SCerMo1UGq1dKWbTXTM2qtu0O8h+C0QsPh6L0DZKd92kHuVe3DsryotKVdqHivbQv3JCUHX6A==", "type": "package", "path": "runtime.android-arm64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-arm64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.android-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "Ut0XmnR22XEhVH9niETFA5MX18ri8Hj273iC2v4B4GxVdczjdIdoCV/NXEqr0Krq9ngK5IQr2bZ8cGu9l4i1Yg==", "type": "package", "path": "runtime.android-x64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.android-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/libSystem.IO.Ports.Native.so", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.android-x86.runtime.native.System.IO.Ports/9.0.4": {"sha512": "t194lL5QZ9wYU5DmR8YApguB7bshAngwi89sttOPcGwBZxUdW9Zxg30VXAnecPwy2f0kulHyfhyHac216rABKw==", "type": "package", "path": "runtime.android-x86.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.android-x86.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.android-x86.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.any.System.Collections/4.3.0": {"sha512": "23g6rqftKmovn2cLeGsuHUYm0FD7pdutb0uQMJpZ3qTvq+zHkgmt6J65VtRry4WDGYlmkMa4xDACtaQ94alNag==", "type": "package", "path": "runtime.any.system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Collections.dll", "lib/netstandard1.3/System.Collections.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.collections.4.3.0.nupkg.sha512", "runtime.any.system.collections.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Diagnostics.Tracing/4.3.0": {"sha512": "1lpifymjGDzoYIaam6/Hyqf8GhBI3xXYLK2TgEvTtuZMorG3Kb9QnMTIKhLjJYXIiu1JvxjngHvtVFQQlpQ3HQ==", "type": "package", "path": "runtime.any.system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Diagnostics.Tracing.dll", "lib/netstandard1.5/System.Diagnostics.Tracing.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.diagnostics.tracing.4.3.0.nupkg.sha512", "runtime.any.system.diagnostics.tracing.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Globalization/4.3.0": {"sha512": "sMDBnad4rp4t7GY442Jux0MCUuKL4otn5BK6Ni0ARTXTSpRNBzZ7hpMfKSvnVSED5kYJm96YOWsqV0JH0d2uuw==", "type": "package", "path": "runtime.any.system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Globalization.dll", "lib/netstandard1.3/System.Globalization.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.globalization.4.3.0.nupkg.sha512", "runtime.any.system.globalization.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.IO/4.3.0": {"sha512": "SDZ5AD1DtyRoxYtEcqQ3HDlcrorMYXZeCt7ZhG9US9I5Vva+gpIWDGMkcwa5XiKL0ceQKRZIX2x0XEjLX7PDzQ==", "type": "package", "path": "runtime.any.system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.IO.dll", "lib/netstandard1.5/System.IO.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.io.4.3.0.nupkg.sha512", "runtime.any.system.io.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Reflection/4.3.0": {"sha512": "hLC3A3rI8jipR5d9k7+f0MgRCW6texsAp0MWkN/ci18FMtQ9KH7E2vDn/DH2LkxsszlpJpOn9qy6Z6/69rH6eQ==", "type": "package", "path": "runtime.any.system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.dll", "lib/netstandard1.5/System.Reflection.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.reflection.4.3.0.nupkg.sha512", "runtime.any.system.reflection.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Reflection.Primitives/4.3.0": {"sha512": "Nrm1p3armp6TTf2xuvaa+jGTTmncALWFq22CpmwRvhDf6dE9ZmH40EbOswD4GnFLrMRS0Ki6Kx5aUPmKK/hZBg==", "type": "package", "path": "runtime.any.system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Reflection.Primitives.dll", "lib/netstandard1.3/System.Reflection.Primitives.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.reflection.primitives.4.3.0.nupkg.sha512", "runtime.any.system.reflection.primitives.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Resources.ResourceManager/4.3.0": {"sha512": "Lxb89SMvf8w9p9+keBLyL6H6x/TEmc6QVsIIA0T36IuyOY3kNvIdyGddA2qt35cRamzxF8K5p0Opq4G4HjNbhQ==", "type": "package", "path": "runtime.any.system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Resources.ResourceManager.dll", "lib/netstandard1.3/System.Resources.ResourceManager.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.resources.resourcemanager.4.3.0.nupkg.sha512", "runtime.any.system.resources.resourcemanager.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Runtime/4.3.0": {"sha512": "fRS7zJgaG9NkifaAxGGclDDoRn9HC7hXACl52Or06a/fxdzDajWb5wov3c6a+gVSlekRoexfjwQSK9sh5um5LQ==", "type": "package", "path": "runtime.any.system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.dll", "lib/netstandard1.5/System.Runtime.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.runtime.4.3.0.nupkg.sha512", "runtime.any.system.runtime.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Runtime.Handles/4.3.0": {"sha512": "GG84X6vufoEzqx8PbeBKheE4srOhimv+yLtGb/JkR3Y2FmoqmueLNFU4Xx8Y67plFpltQSdK74x0qlEhIpv/CQ==", "type": "package", "path": "runtime.any.system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/netstandard1.3/System.Runtime.Handles.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.runtime.handles.4.3.0.nupkg.sha512", "runtime.any.system.runtime.handles.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Runtime.InteropServices/4.3.0": {"sha512": "lBoFeQfxe/4eqjPi46E0LU/YaCMdNkQ8B4MZu/mkzdIAZh8RQ1NYZSj0egrQKdgdvlPFtP4STtob40r4o2DBAw==", "type": "package", "path": "runtime.any.system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Runtime.InteropServices.dll", "lib/netstandard1.5/System.Runtime.InteropServices.dll", "lib/netstandard1.6/System.Runtime.InteropServices.dll", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.runtime.interopservices.4.3.0.nupkg.sha512", "runtime.any.system.runtime.interopservices.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Text.Encoding/4.3.0": {"sha512": "+ihI5VaXFCMVPJNstG4O4eo1CfbrByLxRrQQTqOTp1ttK0kUKDqOdBSTaCB2IBk/QtjDrs6+x4xuezyMXdm0HQ==", "type": "package", "path": "runtime.any.system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Text.Encoding.dll", "lib/netstandard1.3/System.Text.Encoding.dll", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.text.encoding.4.3.0.nupkg.sha512", "runtime.any.system.text.encoding.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Text.Encoding.Extensions/4.3.0": {"sha512": "NLrxmLsfRrOuVqPWG+2lrQZnE53MLVeo+w9c54EV+TUo4c8rILpsDXfY8pPiOy9kHpUHHP07ugKmtsU3vVW5Jg==", "type": "package", "path": "runtime.any.system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Text.Encoding.Extensions.dll", "lib/netstandard1.3/System.Text.Encoding.Extensions.dll", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.text.encoding.extensions.4.3.0.nupkg.sha512", "runtime.any.system.text.encoding.extensions.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.any.System.Threading.Tasks/4.3.0": {"sha512": "OhBAVBQG5kFj1S+hCEQ3TUHBAEtZ3fbEMgZMRNdN8A0Pj4x+5nTELEqL59DU0TjKVE6II3dqKw4Dklb3szT65w==", "type": "package", "path": "runtime.any.system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.Tasks.dll", "lib/netstandard1.3/System.Threading.Tasks.dll", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/netstandard/_._", "runtime.any.system.threading.tasks.4.3.0.nupkg.sha512", "runtime.any.system.threading.tasks.nuspec", "runtimes/aot/lib/netcore50/_._"]}, "runtime.linux-arm.runtime.native.System.IO.Ports/9.0.4": {"sha512": "45EPNw3aK3C/LNb/n3J4fLoDAZHMBpQJ/c1wcsJQZXp/w4s7MIHceYB9xqhYjCmtSOE2pBhDgogtRzfETHfriw==", "type": "package", "path": "runtime.linux-arm.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-arm64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "27N/9PkhBqSGKtdop6Qg4AOdcAtWeGQSUeBVF+oijFPTAZvlcugmtxLZRldovEjd2SZL30PiOjoVIQWKD6kR1w==", "type": "package", "path": "runtime.linux-arm64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-arm64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-arm64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "T7Z3P/NjvCetnKBvb3/azngU7KvN9i9iNZaywK4Gptoi/OZNUT1Zh2btwSFC3p8dKtG7YdtSASG556bVPGWMCA==", "type": "package", "path": "runtime.linux-bionic-arm64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-bionic-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-bionic-x64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "dbf+R8kZcJo0hAbQhcDjRZKBO5b8Nge8hJVCe/U8yo5cZY8ryDlwGfDkUemFeKdI6cxzcV6t+B6YsO6HzwzZvw==", "type": "package", "path": "runtime.linux-bionic-x64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-bionic-x64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-bionic-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm.runtime.native.System.IO.Ports/9.0.4": {"sha512": "H9ObF2A79dVfVKwNQ9zEOlIgAr6Cufsz+eMALq8CQUcn3MX1F4tBlhtP5nE2JEK6ERTeIgKJ3Jt59tZ6Il1p5A==", "type": "package", "path": "runtime.linux-musl-arm.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-musl-arm.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-arm64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "zgqN6GKr/8mRQb1I3sLzHdCbxv01eXrWEorDu6dg2AktYX4ICsGiHzpm+SkeLxznIM68hMkslGr4A8FF1VLf6Q==", "type": "package", "path": "runtime.linux-musl-arm64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-arm64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-musl-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-musl-x64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "ol6VYfBYGqSfqzcIPdIM6LOC8xnJjuJg3MUczi0vuSA0PfTYweep5DDyP3qmglh7wrSvvnaedAP6505Kp2jjPg==", "type": "package", "path": "runtime.linux-musl-x64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-musl-x64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-musl-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/libSystem.IO.Ports.Native.so", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.linux-x64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "/YaCelKLkIC57MAZ/1l7ctBG1q2CbwQxRwjjKLGt7eSqsX+A3IIG41hMsxFTCqIJisEPC8ov3zVTtnSKfop3vg==", "type": "package", "path": "runtime.linux-x64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.linux-x64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.linux-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/libSystem.IO.Ports.Native.so", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-arm64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "3aVVHiHrK0iA3KhC+9k3H2kFccEZtzp9Q5qVB4WYPMx75+bvKq7YG/f8eImrnXY+0CawwdU4T2F1N/yyd3P1zw==", "type": "package", "path": "runtime.maccatalyst-arm64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.maccatalyst-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.maccatalyst-x64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "mY8meyI74mHWD8C+YD5T4glgH1moe/QYIvOjKoJp68oUa9H2hAq7KTvVD74u9ZMOU0Ectt7pj1jINziduYaXHg==", "type": "package", "path": "runtime.maccatalyst-x64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.maccatalyst-x64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.maccatalyst-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.native.System/4.3.0": {"sha512": "c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "type": "package", "path": "runtime.native.system/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.4.3.0.nupkg.sha512", "runtime.native.system.nuspec"]}, "runtime.native.System.IO.Compression/4.3.0": {"sha512": "INBPonS5QPEgn7naufQFXJEp3zX6L4bwHgJ/ZH78aBTpeNfQMtf7C6VrAFhlq2xxWBveIOWyFzQjJ8XzHMhdOQ==", "type": "package", "path": "runtime.native.system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/netstandard1.0/_._", "runtime.native.system.io.compression.4.3.0.nupkg.sha512", "runtime.native.system.io.compression.nuspec"]}, "runtime.native.System.IO.Ports/9.0.4": {"sha512": "8acFIuku7gZytPQ8IQXikr5gpYY+OmRL0hUQth6vUxTSgWHxTHt8O17BD+dnuSJTrT+oVjqcvFuE7y+GHZVu5A==", "type": "package", "path": "runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.native.system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "runtime.osx-arm64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "aLNRjefYluZLbXK7vxU3CezR+Nrimff1OQivr71SoZCS5nLXPi8cSKib8apgdwiooR2fHOlGF+FR6k2D0l2B1A==", "type": "package", "path": "runtime.osx-arm64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-arm64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.osx-arm64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/libSystem.IO.Ports.Native.dylib", "runtimes/osx-x64/native/_._", "useSharedDesignerContext.txt"]}, "runtime.osx-x64.runtime.native.System.IO.Ports/9.0.4": {"sha512": "b4Ik3pgeaGXmy3J3+fa3QmZatpq0ikWq6toEQYKTHuGIL/0dzYWYIoUNvSTEbsr3IF7XZPrNOKTxK7tcIsMqWA==", "type": "package", "path": "runtime.osx-x64.runtime.native.system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "runtime.osx-x64.runtime.native.system.io.ports.9.0.4.nupkg.sha512", "runtime.osx-x64.runtime.native.system.io.ports.nuspec", "runtimes/android-arm/native/_._", "runtimes/android-arm64/native/_._", "runtimes/android-x64/native/_._", "runtimes/android-x86/native/_._", "runtimes/linux-arm/native/_._", "runtimes/linux-arm64/native/_._", "runtimes/linux-bionic-arm64/native/_._", "runtimes/linux-bionic-x64/native/_._", "runtimes/linux-musl-arm/native/_._", "runtimes/linux-musl-arm64/native/_._", "runtimes/linux-musl-x64/native/_._", "runtimes/linux-x64/native/_._", "runtimes/maccatalyst-arm64/native/_._", "runtimes/maccatalyst-x64/native/_._", "runtimes/osx-arm64/native/_._", "runtimes/osx-x64/native/libSystem.IO.Ports.Native.dylib", "useSharedDesignerContext.txt"]}, "runtime.win.Microsoft.Win32.Primitives/4.3.0": {"sha512": "NU51SEt/ZaD2MF48sJ17BIqx7rjeNNLXUevfMOjqQIetdndXwYjZfZsT6jD+rSWp/FYxjesdK4xUSl4OTEI0jw==", "type": "package", "path": "runtime.win.microsoft.win32.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "runtime.win.microsoft.win32.primitives.4.3.0.nupkg.sha512", "runtime.win.microsoft.win32.primitives.nuspec", "runtimes/win/lib/net/_._", "runtimes/win/lib/netstandard1.3/Microsoft.Win32.Primitives.dll"]}, "runtime.win.System.Diagnostics.Debug/4.3.0": {"sha512": "hHHP0WCStene2jjeYcuDkETozUYF/3sHVRHAEOgS3L15hlip24ssqCTnJC28Z03Wpo078oMcJd0H4egD2aJI8g==", "type": "package", "path": "runtime.win.system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "runtime.win.system.diagnostics.debug.4.3.0.nupkg.sha512", "runtime.win.system.diagnostics.debug.nuspec", "runtimes/aot/lib/netcore50/System.Diagnostics.Debug.dll", "runtimes/win/lib/net45/_._", "runtimes/win/lib/netcore50/System.Diagnostics.Debug.dll", "runtimes/win/lib/netstandard1.3/System.Diagnostics.Debug.dll", "runtimes/win/lib/win8/_._", "runtimes/win/lib/wp80/_._", "runtimes/win/lib/wpa81/_._"]}, "runtime.win.System.IO.FileSystem/4.3.0": {"sha512": "Z37zcSCpXuGCYtFbqYO0TwOVXxS2d+BXgSoDFZmRg8BC4Cuy54edjyIvhhcfCrDQA9nl+EPFTgHN54dRAK7mNA==", "type": "package", "path": "runtime.win.system.io.filesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "runtime.win.system.io.filesystem.4.3.0.nupkg.sha512", "runtime.win.system.io.filesystem.nuspec", "runtimes/win/lib/net/_._", "runtimes/win/lib/netcore50/System.IO.FileSystem.dll", "runtimes/win/lib/netstandard1.3/System.IO.FileSystem.dll", "runtimes/win/lib/win8/_._", "runtimes/win/lib/wp8/_._", "runtimes/win/lib/wpa81/_._"]}, "runtime.win.System.Net.Primitives/4.3.0": {"sha512": "lkXXykakvXUU+Zq2j0pC6EO20lEhijjqMc01XXpp1CJN+DeCwl3nsj4t5Xbpz3kA7yQyTqw6d9SyIzsyLsV3zA==", "type": "package", "path": "runtime.win.system.net.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "runtime.win.system.net.primitives.4.3.0.nupkg.sha512", "runtime.win.system.net.primitives.nuspec", "runtimes/win/lib/net/_._", "runtimes/win/lib/netcore50/System.Net.Primitives.dll", "runtimes/win/lib/netstandard1.3/System.Net.Primitives.dll"]}, "runtime.win.System.Net.Sockets/4.3.0": {"sha512": "FK/2gX6MmuLIKNCGsV59Fe4IYrLrI5n9pQ1jh477wiivEM/NCXDT2dRetH5FSfY0bQ+VgTLcS3zcmjQ8my3nxQ==", "type": "package", "path": "runtime.win.system.net.sockets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "runtime.win.system.net.sockets.4.3.0.nupkg.sha512", "runtime.win.system.net.sockets.nuspec", "runtimes/win/lib/net/_._", "runtimes/win/lib/netcore50/System.Net.Sockets.dll", "runtimes/win/lib/netstandard1.3/System.Net.Sockets.dll"]}, "runtime.win.System.Runtime.Extensions/4.3.0": {"sha512": "RkgHVhUPvzZxuUubiZe8yr/6CypRVXj0VBzaR8hsqQ8f+rUo7e4PWrHTLOCjd8fBMGWCrY//fi7Ku3qXD7oHRw==", "type": "package", "path": "runtime.win.system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "runtime.win.system.runtime.extensions.4.3.0.nupkg.sha512", "runtime.win.system.runtime.extensions.nuspec", "runtimes/aot/lib/netcore50/System.Runtime.Extensions.dll", "runtimes/win/lib/net/_._", "runtimes/win/lib/netcore50/System.Runtime.Extensions.dll", "runtimes/win/lib/netstandard1.5/System.Runtime.Extensions.dll"]}, "SharpCompress/0.37.2": {"sha512": "cFBpTct57aubLQXkdqMmgP8GGTFRh7fnRWP53lgE/EYUpDZJ27SSvTkdjB4OYQRZ20SJFpzczUquKLbt/9xkhw==", "type": "package", "path": "sharpcompress/0.37.2", "files": [".nupkg.metadata", ".signature.p7s", "README.md", "lib/net462/SharpCompress.dll", "lib/net6.0/SharpCompress.dll", "lib/net8.0/SharpCompress.dll", "lib/netstandard2.0/SharpCompress.dll", "lib/netstandard2.1/SharpCompress.dll", "sharpcompress.0.37.2.nupkg.sha512", "sharpcompress.nuspec"]}, "SkiaSharp/3.116.1": {"sha512": "DNDwbRjP+aMo27dV2h/uHCVTcWubWWxHnPLiePNyl24f4Pv43mQ8AQQeseOrKR+J3AOCEs6t0sUjo0aa3j3RWQ==", "type": "package", "path": "skiasharp/3.116.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "interactive-extensions/dotnet/SkiaSharp.DotNet.Interactive.dll", "lib/net462/SkiaSharp.dll", "lib/net462/SkiaSharp.pdb", "lib/net6.0/SkiaSharp.dll", "lib/net6.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.dll", "lib/net8.0-android34.0/SkiaSharp.pdb", "lib/net8.0-android34.0/SkiaSharp.xml", "lib/net8.0-ios17.0/SkiaSharp.dll", "lib/net8.0-ios17.0/SkiaSharp.pdb", "lib/net8.0-maccatalyst17.0/SkiaSharp.dll", "lib/net8.0-maccatalyst17.0/SkiaSharp.pdb", "lib/net8.0-macos14.0/SkiaSharp.dll", "lib/net8.0-macos14.0/SkiaSharp.pdb", "lib/net8.0-tizen7.0/SkiaSharp.dll", "lib/net8.0-tizen7.0/SkiaSharp.pdb", "lib/net8.0-tvos17.0/SkiaSharp.dll", "lib/net8.0-tvos17.0/SkiaSharp.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.pdb", "lib/net8.0/SkiaSharp.dll", "lib/net8.0/SkiaSharp.pdb", "lib/netstandard2.0/SkiaSharp.dll", "lib/netstandard2.0/SkiaSharp.pdb", "lib/netstandard2.1/SkiaSharp.dll", "lib/netstandard2.1/SkiaSharp.pdb", "ref/net462/SkiaSharp.dll", "ref/net6.0/SkiaSharp.dll", "ref/net8.0-android34.0/SkiaSharp.dll", "ref/net8.0-ios17.0/SkiaSharp.dll", "ref/net8.0-maccatalyst17.0/SkiaSharp.dll", "ref/net8.0-macos14.0/SkiaSharp.dll", "ref/net8.0-tizen7.0/SkiaSharp.dll", "ref/net8.0-tvos17.0/SkiaSharp.dll", "ref/net8.0-windows10.0.19041/SkiaSharp.dll", "ref/net8.0/SkiaSharp.dll", "ref/netstandard2.0/SkiaSharp.dll", "ref/netstandard2.1/SkiaSharp.dll", "skiasharp.3.116.1.nupkg.sha512", "skiasharp.nuspec"]}, "SkiaSharp.HarfBuzz/3.116.1": {"sha512": "ibDG1+quN86vBd9ztjDAC9wnvS1nRZ6ydTUOSod4NsRHWdLLGzWYn1IOF4Cg9iJh5cQHdpzhUZBQE0JMKznrow==", "type": "package", "path": "skiasharp.harfbuzz/3.116.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/SkiaSharp.HarfBuzz.dll", "lib/net462/SkiaSharp.HarfBuzz.pdb", "lib/net6.0/SkiaSharp.HarfBuzz.dll", "lib/net6.0/SkiaSharp.HarfBuzz.pdb", "lib/net8.0/SkiaSharp.HarfBuzz.dll", "lib/net8.0/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.0/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.0/SkiaSharp.HarfBuzz.pdb", "lib/netstandard2.1/SkiaSharp.HarfBuzz.dll", "lib/netstandard2.1/SkiaSharp.HarfBuzz.pdb", "skiasharp.harfbuzz.3.116.1.nupkg.sha512", "skiasharp.harfbuzz.nuspec"]}, "SkiaSharp.NativeAssets.macOS/3.116.1": {"sha512": "3KPvpKysDmEMt0NnAZPX5U6KFk0LmG/72/IjAIJemIksIZ0Tjs9pGpr3L+zboVCv1MLVoJLKl3nJDXUG6Jda6A==", "type": "package", "path": "skiasharp.nativeassets.macos/3.116.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/SkiaSharp.NativeAssets.macOS.targets", "buildTransitive/net8.0-macos14.0/SkiaSharp.NativeAssets.macOS.targets", "icon.png", "lib/net462/_._", "lib/net6.0/_._", "lib/net8.0-macos14.0/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/osx/native/libSkiaSharp.dylib", "skiasharp.nativeassets.macos.3.116.1.nupkg.sha512", "skiasharp.nativeassets.macos.nuspec"]}, "SkiaSharp.NativeAssets.Win32/3.116.1": {"sha512": "dRQ75MCI8oz6zAs2Y1w6pq6ARs4MhdNG+gf3doOxOxdnueDXffQLGQIxON54GDoxc0WjKOoHMKBR4DhaduwwQw==", "type": "package", "path": "skiasharp.nativeassets.win32/3.116.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "THIRD-PARTY-NOTICES.txt", "buildTransitive/net462/SkiaSharp.NativeAssets.Win32.targets", "icon.png", "lib/net462/_._", "lib/net6.0-windows10.0.19041/_._", "lib/net6.0/_._", "lib/net8.0-windows10.0.19041/_._", "lib/net8.0/_._", "lib/netstandard2.0/_._", "lib/netstandard2.1/_._", "runtimes/win-arm64/native/libSkiaSharp.dll", "runtimes/win-x64/native/libSkiaSharp.dll", "runtimes/win-x86/native/libSkiaSharp.dll", "skiasharp.nativeassets.win32.3.116.1.nupkg.sha512", "skiasharp.nativeassets.win32.nuspec"]}, "SkiaSharp.Views.Desktop.Common/3.116.1": {"sha512": "/ZDg00zVgjN/N5ghDCnP0+9TfOSmMZkMtnoQpaigsBc4Ghg9Qe+iimZuA5nrEWgaygW64LnVan+p8T1wDtS+kQ==", "type": "package", "path": "skiasharp.views.desktop.common/3.116.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/SkiaSharp.Views.Desktop.Common.dll", "lib/net462/SkiaSharp.Views.Desktop.Common.pdb", "lib/net6.0-windows10.0.19041/SkiaSharp.Views.Desktop.Common.dll", "lib/net6.0-windows10.0.19041/SkiaSharp.Views.Desktop.Common.pdb", "lib/net6.0/SkiaSharp.Views.Desktop.Common.dll", "lib/net6.0/SkiaSharp.Views.Desktop.Common.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.Views.Desktop.Common.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.Views.Desktop.Common.pdb", "lib/net8.0/SkiaSharp.Views.Desktop.Common.dll", "lib/net8.0/SkiaSharp.Views.Desktop.Common.pdb", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.dll", "lib/netstandard2.0/SkiaSharp.Views.Desktop.Common.pdb", "lib/netstandard2.1/SkiaSharp.Views.Desktop.Common.dll", "lib/netstandard2.1/SkiaSharp.Views.Desktop.Common.pdb", "skiasharp.views.desktop.common.3.116.1.nupkg.sha512", "skiasharp.views.desktop.common.nuspec"]}, "SkiaSharp.Views.WPF/3.116.1": {"sha512": "28vXtX/56h1RCmTNubJQcrVNxmx2aV8wGbyUjZOnkocNRHHfG7zoChYHTjwBt7R7TCojTo3RbLBkcP/CLtJLNw==", "type": "package", "path": "skiasharp.views.wpf/3.116.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.txt", "README.md", "icon.png", "lib/net462/SkiaSharp.Views.WPF.dll", "lib/net462/SkiaSharp.Views.WPF.pdb", "lib/net6.0-windows10.0.19041/SkiaSharp.Views.WPF.dll", "lib/net6.0-windows10.0.19041/SkiaSharp.Views.WPF.pdb", "lib/net8.0-windows10.0.19041/SkiaSharp.Views.WPF.dll", "lib/net8.0-windows10.0.19041/SkiaSharp.Views.WPF.pdb", "skiasharp.views.wpf.3.116.1.nupkg.sha512", "skiasharp.views.wpf.nuspec"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.CodeDom/9.0.5": {"sha512": "cuzLM2MWutf9ZBEMPYYfd0DXwYdvntp7VCT6a/wvbKCa2ZuvGmW74xi+YBa2mrfEieAXqM4TNKlMmSnfAfpUoQ==", "type": "package", "path": "system.codedom/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.CodeDom.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.CodeDom.targets", "lib/net462/System.CodeDom.dll", "lib/net462/System.CodeDom.xml", "lib/net8.0/System.CodeDom.dll", "lib/net8.0/System.CodeDom.xml", "lib/net9.0/System.CodeDom.dll", "lib/net9.0/System.CodeDom.xml", "lib/netstandard2.0/System.CodeDom.dll", "lib/netstandard2.0/System.CodeDom.xml", "system.codedom.9.0.5.nupkg.sha512", "system.codedom.nuspec", "useSharedDesignerContext.txt"]}, "System.Collections/4.3.0": {"sha512": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "type": "package", "path": "system.collections/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Collections.dll", "ref/netcore50/System.Collections.xml", "ref/netcore50/de/System.Collections.xml", "ref/netcore50/es/System.Collections.xml", "ref/netcore50/fr/System.Collections.xml", "ref/netcore50/it/System.Collections.xml", "ref/netcore50/ja/System.Collections.xml", "ref/netcore50/ko/System.Collections.xml", "ref/netcore50/ru/System.Collections.xml", "ref/netcore50/zh-hans/System.Collections.xml", "ref/netcore50/zh-hant/System.Collections.xml", "ref/netstandard1.0/System.Collections.dll", "ref/netstandard1.0/System.Collections.xml", "ref/netstandard1.0/de/System.Collections.xml", "ref/netstandard1.0/es/System.Collections.xml", "ref/netstandard1.0/fr/System.Collections.xml", "ref/netstandard1.0/it/System.Collections.xml", "ref/netstandard1.0/ja/System.Collections.xml", "ref/netstandard1.0/ko/System.Collections.xml", "ref/netstandard1.0/ru/System.Collections.xml", "ref/netstandard1.0/zh-hans/System.Collections.xml", "ref/netstandard1.0/zh-hant/System.Collections.xml", "ref/netstandard1.3/System.Collections.dll", "ref/netstandard1.3/System.Collections.xml", "ref/netstandard1.3/de/System.Collections.xml", "ref/netstandard1.3/es/System.Collections.xml", "ref/netstandard1.3/fr/System.Collections.xml", "ref/netstandard1.3/it/System.Collections.xml", "ref/netstandard1.3/ja/System.Collections.xml", "ref/netstandard1.3/ko/System.Collections.xml", "ref/netstandard1.3/ru/System.Collections.xml", "ref/netstandard1.3/zh-hans/System.Collections.xml", "ref/netstandard1.3/zh-hant/System.Collections.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.collections.4.3.0.nupkg.sha512", "system.collections.nuspec"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Device.Gpio/3.2.0": {"sha512": "jaL93IC1S2wZfCSLWt8/1lQCp/pDAzDNE5dfLq+pubEhCp0BjDZze6rJMu2KdOuCOeRxHmBuhtWrIRlrTKFsTg==", "type": "package", "path": "system.device.gpio/3.2.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "README.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net5.0/System.Device.Gpio.targets", "lib/net6.0-windows10.0.17763/System.Device.Gpio.dll", "lib/net6.0-windows10.0.17763/System.Device.Gpio.xml", "lib/net6.0/System.Device.Gpio.dll", "lib/net6.0/System.Device.Gpio.xml", "lib/netstandard2.0/System.Device.Gpio.dll", "lib/netstandard2.0/System.Device.Gpio.xml", "system.device.gpio.3.2.0.nupkg.sha512", "system.device.gpio.nuspec"]}, "System.Diagnostics.Debug/4.3.0": {"sha512": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "type": "package", "path": "system.diagnostics.debug/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Diagnostics.Debug.dll", "ref/netcore50/System.Diagnostics.Debug.xml", "ref/netcore50/de/System.Diagnostics.Debug.xml", "ref/netcore50/es/System.Diagnostics.Debug.xml", "ref/netcore50/fr/System.Diagnostics.Debug.xml", "ref/netcore50/it/System.Diagnostics.Debug.xml", "ref/netcore50/ja/System.Diagnostics.Debug.xml", "ref/netcore50/ko/System.Diagnostics.Debug.xml", "ref/netcore50/ru/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hans/System.Diagnostics.Debug.xml", "ref/netcore50/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.0/System.Diagnostics.Debug.dll", "ref/netstandard1.0/System.Diagnostics.Debug.xml", "ref/netstandard1.0/de/System.Diagnostics.Debug.xml", "ref/netstandard1.0/es/System.Diagnostics.Debug.xml", "ref/netstandard1.0/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.0/it/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.0/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.0/zh-hant/System.Diagnostics.Debug.xml", "ref/netstandard1.3/System.Diagnostics.Debug.dll", "ref/netstandard1.3/System.Diagnostics.Debug.xml", "ref/netstandard1.3/de/System.Diagnostics.Debug.xml", "ref/netstandard1.3/es/System.Diagnostics.Debug.xml", "ref/netstandard1.3/fr/System.Diagnostics.Debug.xml", "ref/netstandard1.3/it/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ja/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ko/System.Diagnostics.Debug.xml", "ref/netstandard1.3/ru/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Debug.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Debug.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.debug.4.3.0.nupkg.sha512", "system.diagnostics.debug.nuspec"]}, "System.Diagnostics.Tracing/4.3.0": {"sha512": "rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "type": "package", "path": "system.diagnostics.tracing/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Diagnostics.Tracing.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.dll", "ref/netcore50/System.Diagnostics.Tracing.xml", "ref/netcore50/de/System.Diagnostics.Tracing.xml", "ref/netcore50/es/System.Diagnostics.Tracing.xml", "ref/netcore50/fr/System.Diagnostics.Tracing.xml", "ref/netcore50/it/System.Diagnostics.Tracing.xml", "ref/netcore50/ja/System.Diagnostics.Tracing.xml", "ref/netcore50/ko/System.Diagnostics.Tracing.xml", "ref/netcore50/ru/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hans/System.Diagnostics.Tracing.xml", "ref/netcore50/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/System.Diagnostics.Tracing.dll", "ref/netstandard1.1/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.1/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/System.Diagnostics.Tracing.dll", "ref/netstandard1.2/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.2/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/System.Diagnostics.Tracing.dll", "ref/netstandard1.3/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.3/zh-hant/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/System.Diagnostics.Tracing.dll", "ref/netstandard1.5/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/de/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/es/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/fr/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/it/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ja/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ko/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/ru/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hans/System.Diagnostics.Tracing.xml", "ref/netstandard1.5/zh-hant/System.Diagnostics.Tracing.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.diagnostics.tracing.4.3.0.nupkg.sha512", "system.diagnostics.tracing.nuspec"]}, "System.Globalization/4.3.0": {"sha512": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "type": "package", "path": "system.globalization/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Globalization.dll", "ref/netcore50/System.Globalization.xml", "ref/netcore50/de/System.Globalization.xml", "ref/netcore50/es/System.Globalization.xml", "ref/netcore50/fr/System.Globalization.xml", "ref/netcore50/it/System.Globalization.xml", "ref/netcore50/ja/System.Globalization.xml", "ref/netcore50/ko/System.Globalization.xml", "ref/netcore50/ru/System.Globalization.xml", "ref/netcore50/zh-hans/System.Globalization.xml", "ref/netcore50/zh-hant/System.Globalization.xml", "ref/netstandard1.0/System.Globalization.dll", "ref/netstandard1.0/System.Globalization.xml", "ref/netstandard1.0/de/System.Globalization.xml", "ref/netstandard1.0/es/System.Globalization.xml", "ref/netstandard1.0/fr/System.Globalization.xml", "ref/netstandard1.0/it/System.Globalization.xml", "ref/netstandard1.0/ja/System.Globalization.xml", "ref/netstandard1.0/ko/System.Globalization.xml", "ref/netstandard1.0/ru/System.Globalization.xml", "ref/netstandard1.0/zh-hans/System.Globalization.xml", "ref/netstandard1.0/zh-hant/System.Globalization.xml", "ref/netstandard1.3/System.Globalization.dll", "ref/netstandard1.3/System.Globalization.xml", "ref/netstandard1.3/de/System.Globalization.xml", "ref/netstandard1.3/es/System.Globalization.xml", "ref/netstandard1.3/fr/System.Globalization.xml", "ref/netstandard1.3/it/System.Globalization.xml", "ref/netstandard1.3/ja/System.Globalization.xml", "ref/netstandard1.3/ko/System.Globalization.xml", "ref/netstandard1.3/ru/System.Globalization.xml", "ref/netstandard1.3/zh-hans/System.Globalization.xml", "ref/netstandard1.3/zh-hant/System.Globalization.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.globalization.4.3.0.nupkg.sha512", "system.globalization.nuspec"]}, "System.IO/4.3.0": {"sha512": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "type": "package", "path": "system.io/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.IO.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.IO.dll", "ref/netcore50/System.IO.dll", "ref/netcore50/System.IO.xml", "ref/netcore50/de/System.IO.xml", "ref/netcore50/es/System.IO.xml", "ref/netcore50/fr/System.IO.xml", "ref/netcore50/it/System.IO.xml", "ref/netcore50/ja/System.IO.xml", "ref/netcore50/ko/System.IO.xml", "ref/netcore50/ru/System.IO.xml", "ref/netcore50/zh-hans/System.IO.xml", "ref/netcore50/zh-hant/System.IO.xml", "ref/netstandard1.0/System.IO.dll", "ref/netstandard1.0/System.IO.xml", "ref/netstandard1.0/de/System.IO.xml", "ref/netstandard1.0/es/System.IO.xml", "ref/netstandard1.0/fr/System.IO.xml", "ref/netstandard1.0/it/System.IO.xml", "ref/netstandard1.0/ja/System.IO.xml", "ref/netstandard1.0/ko/System.IO.xml", "ref/netstandard1.0/ru/System.IO.xml", "ref/netstandard1.0/zh-hans/System.IO.xml", "ref/netstandard1.0/zh-hant/System.IO.xml", "ref/netstandard1.3/System.IO.dll", "ref/netstandard1.3/System.IO.xml", "ref/netstandard1.3/de/System.IO.xml", "ref/netstandard1.3/es/System.IO.xml", "ref/netstandard1.3/fr/System.IO.xml", "ref/netstandard1.3/it/System.IO.xml", "ref/netstandard1.3/ja/System.IO.xml", "ref/netstandard1.3/ko/System.IO.xml", "ref/netstandard1.3/ru/System.IO.xml", "ref/netstandard1.3/zh-hans/System.IO.xml", "ref/netstandard1.3/zh-hant/System.IO.xml", "ref/netstandard1.5/System.IO.dll", "ref/netstandard1.5/System.IO.xml", "ref/netstandard1.5/de/System.IO.xml", "ref/netstandard1.5/es/System.IO.xml", "ref/netstandard1.5/fr/System.IO.xml", "ref/netstandard1.5/it/System.IO.xml", "ref/netstandard1.5/ja/System.IO.xml", "ref/netstandard1.5/ko/System.IO.xml", "ref/netstandard1.5/ru/System.IO.xml", "ref/netstandard1.5/zh-hans/System.IO.xml", "ref/netstandard1.5/zh-hant/System.IO.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.4.3.0.nupkg.sha512", "system.io.nuspec"]}, "System.IO.Compression/4.3.0": {"sha512": "YHndyoiV90iu4iKG115ibkhrG+S3jBm8Ap9OwoUAzO5oPDAWcr0SFwQFm0HjM8WkEZWo0zvLTyLmbvTkW1bXgg==", "type": "package", "path": "system.io.compression/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.IO.Compression.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.dll", "ref/netcore50/System.IO.Compression.xml", "ref/netcore50/de/System.IO.Compression.xml", "ref/netcore50/es/System.IO.Compression.xml", "ref/netcore50/fr/System.IO.Compression.xml", "ref/netcore50/it/System.IO.Compression.xml", "ref/netcore50/ja/System.IO.Compression.xml", "ref/netcore50/ko/System.IO.Compression.xml", "ref/netcore50/ru/System.IO.Compression.xml", "ref/netcore50/zh-hans/System.IO.Compression.xml", "ref/netcore50/zh-hant/System.IO.Compression.xml", "ref/netstandard1.1/System.IO.Compression.dll", "ref/netstandard1.1/System.IO.Compression.xml", "ref/netstandard1.1/de/System.IO.Compression.xml", "ref/netstandard1.1/es/System.IO.Compression.xml", "ref/netstandard1.1/fr/System.IO.Compression.xml", "ref/netstandard1.1/it/System.IO.Compression.xml", "ref/netstandard1.1/ja/System.IO.Compression.xml", "ref/netstandard1.1/ko/System.IO.Compression.xml", "ref/netstandard1.1/ru/System.IO.Compression.xml", "ref/netstandard1.1/zh-hans/System.IO.Compression.xml", "ref/netstandard1.1/zh-hant/System.IO.Compression.xml", "ref/netstandard1.3/System.IO.Compression.dll", "ref/netstandard1.3/System.IO.Compression.xml", "ref/netstandard1.3/de/System.IO.Compression.xml", "ref/netstandard1.3/es/System.IO.Compression.xml", "ref/netstandard1.3/fr/System.IO.Compression.xml", "ref/netstandard1.3/it/System.IO.Compression.xml", "ref/netstandard1.3/ja/System.IO.Compression.xml", "ref/netstandard1.3/ko/System.IO.Compression.xml", "ref/netstandard1.3/ru/System.IO.Compression.xml", "ref/netstandard1.3/zh-hans/System.IO.Compression.xml", "ref/netstandard1.3/zh-hant/System.IO.Compression.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.IO.Compression.dll", "runtimes/win/lib/net46/System.IO.Compression.dll", "runtimes/win/lib/netstandard1.3/System.IO.Compression.dll", "system.io.compression.4.3.0.nupkg.sha512", "system.io.compression.nuspec"]}, "System.IO.FileSystem/4.3.0": {"sha512": "3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "type": "package", "path": "system.io.filesystem/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.dll", "ref/netstandard1.3/System.IO.FileSystem.xml", "ref/netstandard1.3/de/System.IO.FileSystem.xml", "ref/netstandard1.3/es/System.IO.FileSystem.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.xml", "ref/netstandard1.3/it/System.IO.FileSystem.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.4.3.0.nupkg.sha512", "system.io.filesystem.nuspec"]}, "System.IO.FileSystem.Primitives/4.3.0": {"sha512": "6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "type": "package", "path": "system.io.filesystem.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.IO.FileSystem.Primitives.dll", "lib/netstandard1.3/System.IO.FileSystem.Primitives.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.dll", "ref/netstandard1.3/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/de/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/es/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/fr/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/it/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ja/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ko/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/ru/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hans/System.IO.FileSystem.Primitives.xml", "ref/netstandard1.3/zh-hant/System.IO.FileSystem.Primitives.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.io.filesystem.primitives.4.3.0.nupkg.sha512", "system.io.filesystem.primitives.nuspec"]}, "System.IO.Pipelines/8.0.0": {"sha512": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA==", "type": "package", "path": "system.io.pipelines/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Pipelines.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Pipelines.targets", "lib/net462/System.IO.Pipelines.dll", "lib/net462/System.IO.Pipelines.xml", "lib/net6.0/System.IO.Pipelines.dll", "lib/net6.0/System.IO.Pipelines.xml", "lib/net7.0/System.IO.Pipelines.dll", "lib/net7.0/System.IO.Pipelines.xml", "lib/net8.0/System.IO.Pipelines.dll", "lib/net8.0/System.IO.Pipelines.xml", "lib/netstandard2.0/System.IO.Pipelines.dll", "lib/netstandard2.0/System.IO.Pipelines.xml", "system.io.pipelines.8.0.0.nupkg.sha512", "system.io.pipelines.nuspec", "useSharedDesignerContext.txt"]}, "System.IO.Ports/9.0.4": {"sha512": "3YWwrBYZg2MabN7QYAOLToYJn551a+lXHsLW9biyXi+162+BjrkXrRsXioPSAoc+7mSim8B/iFPlOYc1BKnOhA==", "type": "package", "path": "system.io.ports/9.0.4", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.IO.Ports.targets", "buildTransitive/net462/_._", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.IO.Ports.targets", "lib/net462/System.IO.Ports.dll", "lib/net462/System.IO.Ports.xml", "lib/net8.0/System.IO.Ports.dll", "lib/net8.0/System.IO.Ports.xml", "lib/net9.0/System.IO.Ports.dll", "lib/net9.0/System.IO.Ports.xml", "lib/netstandard2.0/System.IO.Ports.dll", "lib/netstandard2.0/System.IO.Ports.xml", "runtimes/unix/lib/net8.0/System.IO.Ports.dll", "runtimes/unix/lib/net8.0/System.IO.Ports.xml", "runtimes/unix/lib/net9.0/System.IO.Ports.dll", "runtimes/unix/lib/net9.0/System.IO.Ports.xml", "runtimes/win/lib/net8.0/System.IO.Ports.dll", "runtimes/win/lib/net8.0/System.IO.Ports.xml", "runtimes/win/lib/net9.0/System.IO.Ports.dll", "runtimes/win/lib/net9.0/System.IO.Ports.xml", "system.io.ports.9.0.4.nupkg.sha512", "system.io.ports.nuspec", "useSharedDesignerContext.txt"]}, "System.Linq/4.3.0": {"sha512": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "type": "package", "path": "system.linq/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net463/System.Linq.dll", "lib/netcore50/System.Linq.dll", "lib/netstandard1.6/System.Linq.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net463/System.Linq.dll", "ref/netcore50/System.Linq.dll", "ref/netcore50/System.Linq.xml", "ref/netcore50/de/System.Linq.xml", "ref/netcore50/es/System.Linq.xml", "ref/netcore50/fr/System.Linq.xml", "ref/netcore50/it/System.Linq.xml", "ref/netcore50/ja/System.Linq.xml", "ref/netcore50/ko/System.Linq.xml", "ref/netcore50/ru/System.Linq.xml", "ref/netcore50/zh-hans/System.Linq.xml", "ref/netcore50/zh-hant/System.Linq.xml", "ref/netstandard1.0/System.Linq.dll", "ref/netstandard1.0/System.Linq.xml", "ref/netstandard1.0/de/System.Linq.xml", "ref/netstandard1.0/es/System.Linq.xml", "ref/netstandard1.0/fr/System.Linq.xml", "ref/netstandard1.0/it/System.Linq.xml", "ref/netstandard1.0/ja/System.Linq.xml", "ref/netstandard1.0/ko/System.Linq.xml", "ref/netstandard1.0/ru/System.Linq.xml", "ref/netstandard1.0/zh-hans/System.Linq.xml", "ref/netstandard1.0/zh-hant/System.Linq.xml", "ref/netstandard1.6/System.Linq.dll", "ref/netstandard1.6/System.Linq.xml", "ref/netstandard1.6/de/System.Linq.xml", "ref/netstandard1.6/es/System.Linq.xml", "ref/netstandard1.6/fr/System.Linq.xml", "ref/netstandard1.6/it/System.Linq.xml", "ref/netstandard1.6/ja/System.Linq.xml", "ref/netstandard1.6/ko/System.Linq.xml", "ref/netstandard1.6/ru/System.Linq.xml", "ref/netstandard1.6/zh-hans/System.Linq.xml", "ref/netstandard1.6/zh-hant/System.Linq.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.linq.4.3.0.nupkg.sha512", "system.linq.nuspec"]}, "System.Management/9.0.5": {"sha512": "n6o9PZm9p25+zAzC3/48K0oHnaPKTInRrxqFq1fi/5TPbMLjuoCm/h//mS3cUmSy+9AO1Z+qsC/Ilt/ZFatv5Q==", "type": "package", "path": "system.management/9.0.5", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net8.0/_._", "buildTransitive/netcoreapp2.0/System.Management.targets", "lib/net462/_._", "lib/net8.0/System.Management.dll", "lib/net8.0/System.Management.xml", "lib/net9.0/System.Management.dll", "lib/net9.0/System.Management.xml", "lib/netstandard2.0/System.Management.dll", "lib/netstandard2.0/System.Management.xml", "runtimes/win/lib/net8.0/System.Management.dll", "runtimes/win/lib/net8.0/System.Management.xml", "runtimes/win/lib/net9.0/System.Management.dll", "runtimes/win/lib/net9.0/System.Management.xml", "system.management.9.0.5.nupkg.sha512", "system.management.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Net.NameResolution/4.3.0": {"sha512": "AFYl08R7MrsrEjqpQWTZWBadqXyTzNDaWpMqyxhb0d6sGhV6xMDKueuBXlLL30gz+DIRY6MpdgnHWlCh5wmq9w==", "type": "package", "path": "system.net.nameresolution/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.NameResolution.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.NameResolution.dll", "ref/netstandard1.3/System.Net.NameResolution.dll", "ref/netstandard1.3/System.Net.NameResolution.xml", "ref/netstandard1.3/de/System.Net.NameResolution.xml", "ref/netstandard1.3/es/System.Net.NameResolution.xml", "ref/netstandard1.3/fr/System.Net.NameResolution.xml", "ref/netstandard1.3/it/System.Net.NameResolution.xml", "ref/netstandard1.3/ja/System.Net.NameResolution.xml", "ref/netstandard1.3/ko/System.Net.NameResolution.xml", "ref/netstandard1.3/ru/System.Net.NameResolution.xml", "ref/netstandard1.3/zh-hans/System.Net.NameResolution.xml", "ref/netstandard1.3/zh-hant/System.Net.NameResolution.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/unix/lib/netstandard1.3/System.Net.NameResolution.dll", "runtimes/win/lib/net46/System.Net.NameResolution.dll", "runtimes/win/lib/netcore50/System.Net.NameResolution.dll", "runtimes/win/lib/netstandard1.3/System.Net.NameResolution.dll", "system.net.nameresolution.4.3.0.nupkg.sha512", "system.net.nameresolution.nuspec"]}, "System.Net.NetworkInformation/4.3.0": {"sha512": "zNVmWVry0pAu7lcrRBhwwU96WUdbsrGL3azyzsbXmVNptae1+Za+UgOe9Z6s8iaWhPn7/l4wQqhC56HZWq7tkg==", "type": "package", "path": "system.net.networkinformation/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net46/System.Net.NetworkInformation.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net46/System.Net.NetworkInformation.dll", "ref/netcore50/System.Net.NetworkInformation.dll", "ref/netcore50/System.Net.NetworkInformation.xml", "ref/netcore50/de/System.Net.NetworkInformation.xml", "ref/netcore50/es/System.Net.NetworkInformation.xml", "ref/netcore50/fr/System.Net.NetworkInformation.xml", "ref/netcore50/it/System.Net.NetworkInformation.xml", "ref/netcore50/ja/System.Net.NetworkInformation.xml", "ref/netcore50/ko/System.Net.NetworkInformation.xml", "ref/netcore50/ru/System.Net.NetworkInformation.xml", "ref/netcore50/zh-hans/System.Net.NetworkInformation.xml", "ref/netcore50/zh-hant/System.Net.NetworkInformation.xml", "ref/netstandard1.0/System.Net.NetworkInformation.dll", "ref/netstandard1.0/System.Net.NetworkInformation.xml", "ref/netstandard1.0/de/System.Net.NetworkInformation.xml", "ref/netstandard1.0/es/System.Net.NetworkInformation.xml", "ref/netstandard1.0/fr/System.Net.NetworkInformation.xml", "ref/netstandard1.0/it/System.Net.NetworkInformation.xml", "ref/netstandard1.0/ja/System.Net.NetworkInformation.xml", "ref/netstandard1.0/ko/System.Net.NetworkInformation.xml", "ref/netstandard1.0/ru/System.Net.NetworkInformation.xml", "ref/netstandard1.0/zh-hans/System.Net.NetworkInformation.xml", "ref/netstandard1.0/zh-hant/System.Net.NetworkInformation.xml", "ref/netstandard1.3/System.Net.NetworkInformation.dll", "ref/netstandard1.3/System.Net.NetworkInformation.xml", "ref/netstandard1.3/de/System.Net.NetworkInformation.xml", "ref/netstandard1.3/es/System.Net.NetworkInformation.xml", "ref/netstandard1.3/fr/System.Net.NetworkInformation.xml", "ref/netstandard1.3/it/System.Net.NetworkInformation.xml", "ref/netstandard1.3/ja/System.Net.NetworkInformation.xml", "ref/netstandard1.3/ko/System.Net.NetworkInformation.xml", "ref/netstandard1.3/ru/System.Net.NetworkInformation.xml", "ref/netstandard1.3/zh-hans/System.Net.NetworkInformation.xml", "ref/netstandard1.3/zh-hant/System.Net.NetworkInformation.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/linux/lib/netstandard1.3/System.Net.NetworkInformation.dll", "runtimes/osx/lib/netstandard1.3/System.Net.NetworkInformation.dll", "runtimes/win/lib/net46/System.Net.NetworkInformation.dll", "runtimes/win/lib/netcore50/System.Net.NetworkInformation.dll", "runtimes/win/lib/netstandard1.3/System.Net.NetworkInformation.dll", "system.net.networkinformation.4.3.0.nupkg.sha512", "system.net.networkinformation.nuspec"]}, "System.Net.Primitives/4.3.0": {"sha512": "qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "type": "package", "path": "system.net.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Net.Primitives.dll", "ref/netcore50/System.Net.Primitives.xml", "ref/netcore50/de/System.Net.Primitives.xml", "ref/netcore50/es/System.Net.Primitives.xml", "ref/netcore50/fr/System.Net.Primitives.xml", "ref/netcore50/it/System.Net.Primitives.xml", "ref/netcore50/ja/System.Net.Primitives.xml", "ref/netcore50/ko/System.Net.Primitives.xml", "ref/netcore50/ru/System.Net.Primitives.xml", "ref/netcore50/zh-hans/System.Net.Primitives.xml", "ref/netcore50/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.0/System.Net.Primitives.dll", "ref/netstandard1.0/System.Net.Primitives.xml", "ref/netstandard1.0/de/System.Net.Primitives.xml", "ref/netstandard1.0/es/System.Net.Primitives.xml", "ref/netstandard1.0/fr/System.Net.Primitives.xml", "ref/netstandard1.0/it/System.Net.Primitives.xml", "ref/netstandard1.0/ja/System.Net.Primitives.xml", "ref/netstandard1.0/ko/System.Net.Primitives.xml", "ref/netstandard1.0/ru/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.1/System.Net.Primitives.dll", "ref/netstandard1.1/System.Net.Primitives.xml", "ref/netstandard1.1/de/System.Net.Primitives.xml", "ref/netstandard1.1/es/System.Net.Primitives.xml", "ref/netstandard1.1/fr/System.Net.Primitives.xml", "ref/netstandard1.1/it/System.Net.Primitives.xml", "ref/netstandard1.1/ja/System.Net.Primitives.xml", "ref/netstandard1.1/ko/System.Net.Primitives.xml", "ref/netstandard1.1/ru/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.1/zh-hant/System.Net.Primitives.xml", "ref/netstandard1.3/System.Net.Primitives.dll", "ref/netstandard1.3/System.Net.Primitives.xml", "ref/netstandard1.3/de/System.Net.Primitives.xml", "ref/netstandard1.3/es/System.Net.Primitives.xml", "ref/netstandard1.3/fr/System.Net.Primitives.xml", "ref/netstandard1.3/it/System.Net.Primitives.xml", "ref/netstandard1.3/ja/System.Net.Primitives.xml", "ref/netstandard1.3/ko/System.Net.Primitives.xml", "ref/netstandard1.3/ru/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hans/System.Net.Primitives.xml", "ref/netstandard1.3/zh-hant/System.Net.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.primitives.4.3.0.nupkg.sha512", "system.net.primitives.nuspec"]}, "System.Net.Sockets/4.3.0": {"sha512": "m6icV6TqQOAdgt5N/9I5KNpjom/5NFtkmGseEH+AK/hny8XrytLH3+b5M8zL/Ycg3fhIocFpUMyl/wpFnVRvdw==", "type": "package", "path": "system.net.sockets/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Net.Sockets.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.dll", "ref/netstandard1.3/System.Net.Sockets.xml", "ref/netstandard1.3/de/System.Net.Sockets.xml", "ref/netstandard1.3/es/System.Net.Sockets.xml", "ref/netstandard1.3/fr/System.Net.Sockets.xml", "ref/netstandard1.3/it/System.Net.Sockets.xml", "ref/netstandard1.3/ja/System.Net.Sockets.xml", "ref/netstandard1.3/ko/System.Net.Sockets.xml", "ref/netstandard1.3/ru/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hans/System.Net.Sockets.xml", "ref/netstandard1.3/zh-hant/System.Net.Sockets.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.net.sockets.4.3.0.nupkg.sha512", "system.net.sockets.nuspec"]}, "System.Private.Uri/4.3.0": {"sha512": "I4SwANiUGho1esj4V4oSlPllXjzCZDE+5XXso2P03LW2vOda2Enzh8DWOxwN6hnrJyp314c7KuVu31QYhRzOGg==", "type": "package", "path": "system.private.uri/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "ref/netstandard/_._", "system.private.uri.4.3.0.nupkg.sha512", "system.private.uri.nuspec"]}, "System.Reflection/4.3.0": {"sha512": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "type": "package", "path": "system.reflection/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Reflection.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Reflection.dll", "ref/netcore50/System.Reflection.dll", "ref/netcore50/System.Reflection.xml", "ref/netcore50/de/System.Reflection.xml", "ref/netcore50/es/System.Reflection.xml", "ref/netcore50/fr/System.Reflection.xml", "ref/netcore50/it/System.Reflection.xml", "ref/netcore50/ja/System.Reflection.xml", "ref/netcore50/ko/System.Reflection.xml", "ref/netcore50/ru/System.Reflection.xml", "ref/netcore50/zh-hans/System.Reflection.xml", "ref/netcore50/zh-hant/System.Reflection.xml", "ref/netstandard1.0/System.Reflection.dll", "ref/netstandard1.0/System.Reflection.xml", "ref/netstandard1.0/de/System.Reflection.xml", "ref/netstandard1.0/es/System.Reflection.xml", "ref/netstandard1.0/fr/System.Reflection.xml", "ref/netstandard1.0/it/System.Reflection.xml", "ref/netstandard1.0/ja/System.Reflection.xml", "ref/netstandard1.0/ko/System.Reflection.xml", "ref/netstandard1.0/ru/System.Reflection.xml", "ref/netstandard1.0/zh-hans/System.Reflection.xml", "ref/netstandard1.0/zh-hant/System.Reflection.xml", "ref/netstandard1.3/System.Reflection.dll", "ref/netstandard1.3/System.Reflection.xml", "ref/netstandard1.3/de/System.Reflection.xml", "ref/netstandard1.3/es/System.Reflection.xml", "ref/netstandard1.3/fr/System.Reflection.xml", "ref/netstandard1.3/it/System.Reflection.xml", "ref/netstandard1.3/ja/System.Reflection.xml", "ref/netstandard1.3/ko/System.Reflection.xml", "ref/netstandard1.3/ru/System.Reflection.xml", "ref/netstandard1.3/zh-hans/System.Reflection.xml", "ref/netstandard1.3/zh-hant/System.Reflection.xml", "ref/netstandard1.5/System.Reflection.dll", "ref/netstandard1.5/System.Reflection.xml", "ref/netstandard1.5/de/System.Reflection.xml", "ref/netstandard1.5/es/System.Reflection.xml", "ref/netstandard1.5/fr/System.Reflection.xml", "ref/netstandard1.5/it/System.Reflection.xml", "ref/netstandard1.5/ja/System.Reflection.xml", "ref/netstandard1.5/ko/System.Reflection.xml", "ref/netstandard1.5/ru/System.Reflection.xml", "ref/netstandard1.5/zh-hans/System.Reflection.xml", "ref/netstandard1.5/zh-hant/System.Reflection.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.4.3.0.nupkg.sha512", "system.reflection.nuspec"]}, "System.Reflection.Primitives/4.3.0": {"sha512": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "type": "package", "path": "system.reflection.primitives/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Reflection.Primitives.dll", "ref/netcore50/System.Reflection.Primitives.xml", "ref/netcore50/de/System.Reflection.Primitives.xml", "ref/netcore50/es/System.Reflection.Primitives.xml", "ref/netcore50/fr/System.Reflection.Primitives.xml", "ref/netcore50/it/System.Reflection.Primitives.xml", "ref/netcore50/ja/System.Reflection.Primitives.xml", "ref/netcore50/ko/System.Reflection.Primitives.xml", "ref/netcore50/ru/System.Reflection.Primitives.xml", "ref/netcore50/zh-hans/System.Reflection.Primitives.xml", "ref/netcore50/zh-hant/System.Reflection.Primitives.xml", "ref/netstandard1.0/System.Reflection.Primitives.dll", "ref/netstandard1.0/System.Reflection.Primitives.xml", "ref/netstandard1.0/de/System.Reflection.Primitives.xml", "ref/netstandard1.0/es/System.Reflection.Primitives.xml", "ref/netstandard1.0/fr/System.Reflection.Primitives.xml", "ref/netstandard1.0/it/System.Reflection.Primitives.xml", "ref/netstandard1.0/ja/System.Reflection.Primitives.xml", "ref/netstandard1.0/ko/System.Reflection.Primitives.xml", "ref/netstandard1.0/ru/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hans/System.Reflection.Primitives.xml", "ref/netstandard1.0/zh-hant/System.Reflection.Primitives.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.reflection.primitives.4.3.0.nupkg.sha512", "system.reflection.primitives.nuspec"]}, "System.Resources.ResourceManager/4.3.0": {"sha512": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "type": "package", "path": "system.resources.resourcemanager/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Resources.ResourceManager.dll", "ref/netcore50/System.Resources.ResourceManager.xml", "ref/netcore50/de/System.Resources.ResourceManager.xml", "ref/netcore50/es/System.Resources.ResourceManager.xml", "ref/netcore50/fr/System.Resources.ResourceManager.xml", "ref/netcore50/it/System.Resources.ResourceManager.xml", "ref/netcore50/ja/System.Resources.ResourceManager.xml", "ref/netcore50/ko/System.Resources.ResourceManager.xml", "ref/netcore50/ru/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hans/System.Resources.ResourceManager.xml", "ref/netcore50/zh-hant/System.Resources.ResourceManager.xml", "ref/netstandard1.0/System.Resources.ResourceManager.dll", "ref/netstandard1.0/System.Resources.ResourceManager.xml", "ref/netstandard1.0/de/System.Resources.ResourceManager.xml", "ref/netstandard1.0/es/System.Resources.ResourceManager.xml", "ref/netstandard1.0/fr/System.Resources.ResourceManager.xml", "ref/netstandard1.0/it/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ja/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ko/System.Resources.ResourceManager.xml", "ref/netstandard1.0/ru/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hans/System.Resources.ResourceManager.xml", "ref/netstandard1.0/zh-hant/System.Resources.ResourceManager.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.resources.resourcemanager.4.3.0.nupkg.sha512", "system.resources.resourcemanager.nuspec"]}, "System.Runtime/4.3.0": {"sha512": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "type": "package", "path": "system.runtime/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.dll", "lib/portable-net45+win8+wp80+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.dll", "ref/netcore50/System.Runtime.dll", "ref/netcore50/System.Runtime.xml", "ref/netcore50/de/System.Runtime.xml", "ref/netcore50/es/System.Runtime.xml", "ref/netcore50/fr/System.Runtime.xml", "ref/netcore50/it/System.Runtime.xml", "ref/netcore50/ja/System.Runtime.xml", "ref/netcore50/ko/System.Runtime.xml", "ref/netcore50/ru/System.Runtime.xml", "ref/netcore50/zh-hans/System.Runtime.xml", "ref/netcore50/zh-hant/System.Runtime.xml", "ref/netstandard1.0/System.Runtime.dll", "ref/netstandard1.0/System.Runtime.xml", "ref/netstandard1.0/de/System.Runtime.xml", "ref/netstandard1.0/es/System.Runtime.xml", "ref/netstandard1.0/fr/System.Runtime.xml", "ref/netstandard1.0/it/System.Runtime.xml", "ref/netstandard1.0/ja/System.Runtime.xml", "ref/netstandard1.0/ko/System.Runtime.xml", "ref/netstandard1.0/ru/System.Runtime.xml", "ref/netstandard1.0/zh-hans/System.Runtime.xml", "ref/netstandard1.0/zh-hant/System.Runtime.xml", "ref/netstandard1.2/System.Runtime.dll", "ref/netstandard1.2/System.Runtime.xml", "ref/netstandard1.2/de/System.Runtime.xml", "ref/netstandard1.2/es/System.Runtime.xml", "ref/netstandard1.2/fr/System.Runtime.xml", "ref/netstandard1.2/it/System.Runtime.xml", "ref/netstandard1.2/ja/System.Runtime.xml", "ref/netstandard1.2/ko/System.Runtime.xml", "ref/netstandard1.2/ru/System.Runtime.xml", "ref/netstandard1.2/zh-hans/System.Runtime.xml", "ref/netstandard1.2/zh-hant/System.Runtime.xml", "ref/netstandard1.3/System.Runtime.dll", "ref/netstandard1.3/System.Runtime.xml", "ref/netstandard1.3/de/System.Runtime.xml", "ref/netstandard1.3/es/System.Runtime.xml", "ref/netstandard1.3/fr/System.Runtime.xml", "ref/netstandard1.3/it/System.Runtime.xml", "ref/netstandard1.3/ja/System.Runtime.xml", "ref/netstandard1.3/ko/System.Runtime.xml", "ref/netstandard1.3/ru/System.Runtime.xml", "ref/netstandard1.3/zh-hans/System.Runtime.xml", "ref/netstandard1.3/zh-hant/System.Runtime.xml", "ref/netstandard1.5/System.Runtime.dll", "ref/netstandard1.5/System.Runtime.xml", "ref/netstandard1.5/de/System.Runtime.xml", "ref/netstandard1.5/es/System.Runtime.xml", "ref/netstandard1.5/fr/System.Runtime.xml", "ref/netstandard1.5/it/System.Runtime.xml", "ref/netstandard1.5/ja/System.Runtime.xml", "ref/netstandard1.5/ko/System.Runtime.xml", "ref/netstandard1.5/ru/System.Runtime.xml", "ref/netstandard1.5/zh-hans/System.Runtime.xml", "ref/netstandard1.5/zh-hant/System.Runtime.xml", "ref/portable-net45+win8+wp80+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.4.3.0.nupkg.sha512", "system.runtime.nuspec"]}, "System.Runtime.Extensions/4.3.0": {"sha512": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "type": "package", "path": "system.runtime.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.dll", "ref/netcore50/System.Runtime.Extensions.xml", "ref/netcore50/de/System.Runtime.Extensions.xml", "ref/netcore50/es/System.Runtime.Extensions.xml", "ref/netcore50/fr/System.Runtime.Extensions.xml", "ref/netcore50/it/System.Runtime.Extensions.xml", "ref/netcore50/ja/System.Runtime.Extensions.xml", "ref/netcore50/ko/System.Runtime.Extensions.xml", "ref/netcore50/ru/System.Runtime.Extensions.xml", "ref/netcore50/zh-hans/System.Runtime.Extensions.xml", "ref/netcore50/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.0/System.Runtime.Extensions.dll", "ref/netstandard1.0/System.Runtime.Extensions.xml", "ref/netstandard1.0/de/System.Runtime.Extensions.xml", "ref/netstandard1.0/es/System.Runtime.Extensions.xml", "ref/netstandard1.0/fr/System.Runtime.Extensions.xml", "ref/netstandard1.0/it/System.Runtime.Extensions.xml", "ref/netstandard1.0/ja/System.Runtime.Extensions.xml", "ref/netstandard1.0/ko/System.Runtime.Extensions.xml", "ref/netstandard1.0/ru/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.3/System.Runtime.Extensions.dll", "ref/netstandard1.3/System.Runtime.Extensions.xml", "ref/netstandard1.3/de/System.Runtime.Extensions.xml", "ref/netstandard1.3/es/System.Runtime.Extensions.xml", "ref/netstandard1.3/fr/System.Runtime.Extensions.xml", "ref/netstandard1.3/it/System.Runtime.Extensions.xml", "ref/netstandard1.3/ja/System.Runtime.Extensions.xml", "ref/netstandard1.3/ko/System.Runtime.Extensions.xml", "ref/netstandard1.3/ru/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Extensions.xml", "ref/netstandard1.5/System.Runtime.Extensions.dll", "ref/netstandard1.5/System.Runtime.Extensions.xml", "ref/netstandard1.5/de/System.Runtime.Extensions.xml", "ref/netstandard1.5/es/System.Runtime.Extensions.xml", "ref/netstandard1.5/fr/System.Runtime.Extensions.xml", "ref/netstandard1.5/it/System.Runtime.Extensions.xml", "ref/netstandard1.5/ja/System.Runtime.Extensions.xml", "ref/netstandard1.5/ko/System.Runtime.Extensions.xml", "ref/netstandard1.5/ru/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hans/System.Runtime.Extensions.xml", "ref/netstandard1.5/zh-hant/System.Runtime.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.extensions.4.3.0.nupkg.sha512", "system.runtime.extensions.nuspec"]}, "System.Runtime.Handles/4.3.0": {"sha512": "OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "type": "package", "path": "system.runtime.handles/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/_._", "ref/netstandard1.3/System.Runtime.Handles.dll", "ref/netstandard1.3/System.Runtime.Handles.xml", "ref/netstandard1.3/de/System.Runtime.Handles.xml", "ref/netstandard1.3/es/System.Runtime.Handles.xml", "ref/netstandard1.3/fr/System.Runtime.Handles.xml", "ref/netstandard1.3/it/System.Runtime.Handles.xml", "ref/netstandard1.3/ja/System.Runtime.Handles.xml", "ref/netstandard1.3/ko/System.Runtime.Handles.xml", "ref/netstandard1.3/ru/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hans/System.Runtime.Handles.xml", "ref/netstandard1.3/zh-hant/System.Runtime.Handles.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.handles.4.3.0.nupkg.sha512", "system.runtime.handles.nuspec"]}, "System.Runtime.InteropServices/4.3.0": {"sha512": "uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "type": "package", "path": "system.runtime.interopservices/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net462/System.Runtime.InteropServices.dll", "lib/net463/System.Runtime.InteropServices.dll", "lib/portable-net45+win8+wpa81/_._", "lib/win8/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net462/System.Runtime.InteropServices.dll", "ref/net463/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.dll", "ref/netcore50/System.Runtime.InteropServices.xml", "ref/netcore50/de/System.Runtime.InteropServices.xml", "ref/netcore50/es/System.Runtime.InteropServices.xml", "ref/netcore50/fr/System.Runtime.InteropServices.xml", "ref/netcore50/it/System.Runtime.InteropServices.xml", "ref/netcore50/ja/System.Runtime.InteropServices.xml", "ref/netcore50/ko/System.Runtime.InteropServices.xml", "ref/netcore50/ru/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hans/System.Runtime.InteropServices.xml", "ref/netcore50/zh-hant/System.Runtime.InteropServices.xml", "ref/netcoreapp1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.dll", "ref/netstandard1.1/System.Runtime.InteropServices.xml", "ref/netstandard1.1/de/System.Runtime.InteropServices.xml", "ref/netstandard1.1/es/System.Runtime.InteropServices.xml", "ref/netstandard1.1/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.1/it/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.1/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.1/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.2/System.Runtime.InteropServices.dll", "ref/netstandard1.2/System.Runtime.InteropServices.xml", "ref/netstandard1.2/de/System.Runtime.InteropServices.xml", "ref/netstandard1.2/es/System.Runtime.InteropServices.xml", "ref/netstandard1.2/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.2/it/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.2/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.2/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.3/System.Runtime.InteropServices.dll", "ref/netstandard1.3/System.Runtime.InteropServices.xml", "ref/netstandard1.3/de/System.Runtime.InteropServices.xml", "ref/netstandard1.3/es/System.Runtime.InteropServices.xml", "ref/netstandard1.3/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.3/it/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.3/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.3/zh-hant/System.Runtime.InteropServices.xml", "ref/netstandard1.5/System.Runtime.InteropServices.dll", "ref/netstandard1.5/System.Runtime.InteropServices.xml", "ref/netstandard1.5/de/System.Runtime.InteropServices.xml", "ref/netstandard1.5/es/System.Runtime.InteropServices.xml", "ref/netstandard1.5/fr/System.Runtime.InteropServices.xml", "ref/netstandard1.5/it/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ja/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ko/System.Runtime.InteropServices.xml", "ref/netstandard1.5/ru/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hans/System.Runtime.InteropServices.xml", "ref/netstandard1.5/zh-hant/System.Runtime.InteropServices.xml", "ref/portable-net45+win8+wpa81/_._", "ref/win8/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.runtime.interopservices.4.3.0.nupkg.sha512", "system.runtime.interopservices.nuspec"]}, "System.Security.AccessControl/5.0.0": {"sha512": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "type": "package", "path": "system.security.accesscontrol/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.dll", "lib/net461/System.Security.AccessControl.xml", "lib/netstandard1.3/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.dll", "lib/netstandard2.0/System.Security.AccessControl.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.dll", "ref/net461/System.Security.AccessControl.xml", "ref/netstandard1.3/System.Security.AccessControl.dll", "ref/netstandard1.3/System.Security.AccessControl.xml", "ref/netstandard1.3/de/System.Security.AccessControl.xml", "ref/netstandard1.3/es/System.Security.AccessControl.xml", "ref/netstandard1.3/fr/System.Security.AccessControl.xml", "ref/netstandard1.3/it/System.Security.AccessControl.xml", "ref/netstandard1.3/ja/System.Security.AccessControl.xml", "ref/netstandard1.3/ko/System.Security.AccessControl.xml", "ref/netstandard1.3/ru/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hans/System.Security.AccessControl.xml", "ref/netstandard1.3/zh-hant/System.Security.AccessControl.xml", "ref/netstandard2.0/System.Security.AccessControl.dll", "ref/netstandard2.0/System.Security.AccessControl.xml", "ref/uap10.0.16299/_._", "runtimes/win/lib/net46/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.dll", "runtimes/win/lib/net461/System.Security.AccessControl.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.AccessControl.xml", "runtimes/win/lib/netstandard1.3/System.Security.AccessControl.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.accesscontrol.5.0.0.nupkg.sha512", "system.security.accesscontrol.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Security.Principal.Windows/5.0.0": {"sha512": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "type": "package", "path": "system.security.principal.windows/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net46/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.dll", "lib/net461/System.Security.Principal.Windows.xml", "lib/netstandard1.3/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.dll", "lib/netstandard2.0/System.Security.Principal.Windows.xml", "lib/uap10.0.16299/_._", "ref/net46/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.dll", "ref/net461/System.Security.Principal.Windows.xml", "ref/netcoreapp3.0/System.Security.Principal.Windows.dll", "ref/netcoreapp3.0/System.Security.Principal.Windows.xml", "ref/netstandard1.3/System.Security.Principal.Windows.dll", "ref/netstandard1.3/System.Security.Principal.Windows.xml", "ref/netstandard1.3/de/System.Security.Principal.Windows.xml", "ref/netstandard1.3/es/System.Security.Principal.Windows.xml", "ref/netstandard1.3/fr/System.Security.Principal.Windows.xml", "ref/netstandard1.3/it/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ja/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ko/System.Security.Principal.Windows.xml", "ref/netstandard1.3/ru/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hans/System.Security.Principal.Windows.xml", "ref/netstandard1.3/zh-hant/System.Security.Principal.Windows.xml", "ref/netstandard2.0/System.Security.Principal.Windows.dll", "ref/netstandard2.0/System.Security.Principal.Windows.xml", "ref/uap10.0.16299/_._", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/unix/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/net46/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.dll", "runtimes/win/lib/net461/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.0/System.Security.Principal.Windows.xml", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.dll", "runtimes/win/lib/netcoreapp2.1/System.Security.Principal.Windows.xml", "runtimes/win/lib/netstandard1.3/System.Security.Principal.Windows.dll", "runtimes/win/lib/uap10.0.16299/_._", "system.security.principal.windows.5.0.0.nupkg.sha512", "system.security.principal.windows.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Text.Encoding/4.3.0": {"sha512": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "type": "package", "path": "system.text.encoding/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.dll", "ref/netcore50/System.Text.Encoding.xml", "ref/netcore50/de/System.Text.Encoding.xml", "ref/netcore50/es/System.Text.Encoding.xml", "ref/netcore50/fr/System.Text.Encoding.xml", "ref/netcore50/it/System.Text.Encoding.xml", "ref/netcore50/ja/System.Text.Encoding.xml", "ref/netcore50/ko/System.Text.Encoding.xml", "ref/netcore50/ru/System.Text.Encoding.xml", "ref/netcore50/zh-hans/System.Text.Encoding.xml", "ref/netcore50/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.0/System.Text.Encoding.dll", "ref/netstandard1.0/System.Text.Encoding.xml", "ref/netstandard1.0/de/System.Text.Encoding.xml", "ref/netstandard1.0/es/System.Text.Encoding.xml", "ref/netstandard1.0/fr/System.Text.Encoding.xml", "ref/netstandard1.0/it/System.Text.Encoding.xml", "ref/netstandard1.0/ja/System.Text.Encoding.xml", "ref/netstandard1.0/ko/System.Text.Encoding.xml", "ref/netstandard1.0/ru/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.xml", "ref/netstandard1.3/System.Text.Encoding.dll", "ref/netstandard1.3/System.Text.Encoding.xml", "ref/netstandard1.3/de/System.Text.Encoding.xml", "ref/netstandard1.3/es/System.Text.Encoding.xml", "ref/netstandard1.3/fr/System.Text.Encoding.xml", "ref/netstandard1.3/it/System.Text.Encoding.xml", "ref/netstandard1.3/ja/System.Text.Encoding.xml", "ref/netstandard1.3/ko/System.Text.Encoding.xml", "ref/netstandard1.3/ru/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.4.3.0.nupkg.sha512", "system.text.encoding.nuspec"]}, "System.Text.Encoding.Extensions/4.3.0": {"sha512": "YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "type": "package", "path": "system.text.encoding.extensions/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Text.Encoding.Extensions.dll", "ref/netcore50/System.Text.Encoding.Extensions.xml", "ref/netcore50/de/System.Text.Encoding.Extensions.xml", "ref/netcore50/es/System.Text.Encoding.Extensions.xml", "ref/netcore50/fr/System.Text.Encoding.Extensions.xml", "ref/netcore50/it/System.Text.Encoding.Extensions.xml", "ref/netcore50/ja/System.Text.Encoding.Extensions.xml", "ref/netcore50/ko/System.Text.Encoding.Extensions.xml", "ref/netcore50/ru/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netcore50/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/System.Text.Encoding.Extensions.dll", "ref/netstandard1.0/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.0/zh-hant/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/System.Text.Encoding.Extensions.dll", "ref/netstandard1.3/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/de/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/es/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/fr/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/it/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ja/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ko/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/ru/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hans/System.Text.Encoding.Extensions.xml", "ref/netstandard1.3/zh-hant/System.Text.Encoding.Extensions.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.text.encoding.extensions.4.3.0.nupkg.sha512", "system.text.encoding.extensions.nuspec"]}, "System.Text.Encodings.Web/8.0.0": {"sha512": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ==", "type": "package", "path": "system.text.encodings.web/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Text.Encodings.Web.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Text.Encodings.Web.targets", "lib/net462/System.Text.Encodings.Web.dll", "lib/net462/System.Text.Encodings.Web.xml", "lib/net6.0/System.Text.Encodings.Web.dll", "lib/net6.0/System.Text.Encodings.Web.xml", "lib/net7.0/System.Text.Encodings.Web.dll", "lib/net7.0/System.Text.Encodings.Web.xml", "lib/net8.0/System.Text.Encodings.Web.dll", "lib/net8.0/System.Text.Encodings.Web.xml", "lib/netstandard2.0/System.Text.Encodings.Web.dll", "lib/netstandard2.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net6.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net7.0/System.Text.Encodings.Web.xml", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.dll", "runtimes/browser/lib/net8.0/System.Text.Encodings.Web.xml", "system.text.encodings.web.8.0.0.nupkg.sha512", "system.text.encodings.web.nuspec", "useSharedDesignerContext.txt"]}, "System.Text.Json/8.0.0": {"sha512": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "type": "package", "path": "system.text.json/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "analyzers/dotnet/roslyn3.11/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn3.11/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn3.11/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.0/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.0/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/System.Text.Json.SourceGeneration.dll", "analyzers/dotnet/roslyn4.4/cs/cs/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/de/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/es/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/fr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/it/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ja/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ko/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pl/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/pt-BR/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/ru/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/tr/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hans/System.Text.Json.SourceGeneration.resources.dll", "analyzers/dotnet/roslyn4.4/cs/zh-Hant/System.Text.Json.SourceGeneration.resources.dll", "buildTransitive/net461/System.Text.Json.targets", "buildTransitive/net462/System.Text.Json.targets", "buildTransitive/net6.0/System.Text.Json.targets", "buildTransitive/netcoreapp2.0/System.Text.Json.targets", "buildTransitive/netstandard2.0/System.Text.Json.targets", "lib/net462/System.Text.Json.dll", "lib/net462/System.Text.Json.xml", "lib/net6.0/System.Text.Json.dll", "lib/net6.0/System.Text.Json.xml", "lib/net7.0/System.Text.Json.dll", "lib/net7.0/System.Text.Json.xml", "lib/net8.0/System.Text.Json.dll", "lib/net8.0/System.Text.Json.xml", "lib/netstandard2.0/System.Text.Json.dll", "lib/netstandard2.0/System.Text.Json.xml", "system.text.json.8.0.0.nupkg.sha512", "system.text.json.nuspec", "useSharedDesignerContext.txt"]}, "System.Threading/4.3.0": {"sha512": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "type": "package", "path": "system.threading/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/System.Threading.dll", "lib/netstandard1.3/System.Threading.dll", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.dll", "ref/netcore50/System.Threading.xml", "ref/netcore50/de/System.Threading.xml", "ref/netcore50/es/System.Threading.xml", "ref/netcore50/fr/System.Threading.xml", "ref/netcore50/it/System.Threading.xml", "ref/netcore50/ja/System.Threading.xml", "ref/netcore50/ko/System.Threading.xml", "ref/netcore50/ru/System.Threading.xml", "ref/netcore50/zh-hans/System.Threading.xml", "ref/netcore50/zh-hant/System.Threading.xml", "ref/netstandard1.0/System.Threading.dll", "ref/netstandard1.0/System.Threading.xml", "ref/netstandard1.0/de/System.Threading.xml", "ref/netstandard1.0/es/System.Threading.xml", "ref/netstandard1.0/fr/System.Threading.xml", "ref/netstandard1.0/it/System.Threading.xml", "ref/netstandard1.0/ja/System.Threading.xml", "ref/netstandard1.0/ko/System.Threading.xml", "ref/netstandard1.0/ru/System.Threading.xml", "ref/netstandard1.0/zh-hans/System.Threading.xml", "ref/netstandard1.0/zh-hant/System.Threading.xml", "ref/netstandard1.3/System.Threading.dll", "ref/netstandard1.3/System.Threading.xml", "ref/netstandard1.3/de/System.Threading.xml", "ref/netstandard1.3/es/System.Threading.xml", "ref/netstandard1.3/fr/System.Threading.xml", "ref/netstandard1.3/it/System.Threading.xml", "ref/netstandard1.3/ja/System.Threading.xml", "ref/netstandard1.3/ko/System.Threading.xml", "ref/netstandard1.3/ru/System.Threading.xml", "ref/netstandard1.3/zh-hans/System.Threading.xml", "ref/netstandard1.3/zh-hant/System.Threading.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "runtimes/aot/lib/netcore50/System.Threading.dll", "system.threading.4.3.0.nupkg.sha512", "system.threading.nuspec"]}, "System.Threading.Overlapped/4.3.0": {"sha512": "m3HQ2dPiX/DSTpf+yJt8B0c+SRvzfqAJKx+QDWi+VLhz8svLT23MVjEOHPF/KiSLeArKU/iHescrbLd3yVgyNg==", "type": "package", "path": "system.threading.overlapped/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/net46/System.Threading.Overlapped.dll", "ref/net46/System.Threading.Overlapped.dll", "ref/netstandard1.3/System.Threading.Overlapped.dll", "ref/netstandard1.3/System.Threading.Overlapped.xml", "ref/netstandard1.3/de/System.Threading.Overlapped.xml", "ref/netstandard1.3/es/System.Threading.Overlapped.xml", "ref/netstandard1.3/fr/System.Threading.Overlapped.xml", "ref/netstandard1.3/it/System.Threading.Overlapped.xml", "ref/netstandard1.3/ja/System.Threading.Overlapped.xml", "ref/netstandard1.3/ko/System.Threading.Overlapped.xml", "ref/netstandard1.3/ru/System.Threading.Overlapped.xml", "ref/netstandard1.3/zh-hans/System.Threading.Overlapped.xml", "ref/netstandard1.3/zh-hant/System.Threading.Overlapped.xml", "runtimes/unix/lib/netstandard1.3/System.Threading.Overlapped.dll", "runtimes/win/lib/net46/System.Threading.Overlapped.dll", "runtimes/win/lib/netcore50/System.Threading.Overlapped.dll", "runtimes/win/lib/netstandard1.3/System.Threading.Overlapped.dll", "system.threading.overlapped.4.3.0.nupkg.sha512", "system.threading.overlapped.nuspec"]}, "System.Threading.Tasks/4.3.0": {"sha512": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "type": "package", "path": "system.threading.tasks/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/System.Threading.Tasks.dll", "ref/netcore50/System.Threading.Tasks.xml", "ref/netcore50/de/System.Threading.Tasks.xml", "ref/netcore50/es/System.Threading.Tasks.xml", "ref/netcore50/fr/System.Threading.Tasks.xml", "ref/netcore50/it/System.Threading.Tasks.xml", "ref/netcore50/ja/System.Threading.Tasks.xml", "ref/netcore50/ko/System.Threading.Tasks.xml", "ref/netcore50/ru/System.Threading.Tasks.xml", "ref/netcore50/zh-hans/System.Threading.Tasks.xml", "ref/netcore50/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.0/System.Threading.Tasks.dll", "ref/netstandard1.0/System.Threading.Tasks.xml", "ref/netstandard1.0/de/System.Threading.Tasks.xml", "ref/netstandard1.0/es/System.Threading.Tasks.xml", "ref/netstandard1.0/fr/System.Threading.Tasks.xml", "ref/netstandard1.0/it/System.Threading.Tasks.xml", "ref/netstandard1.0/ja/System.Threading.Tasks.xml", "ref/netstandard1.0/ko/System.Threading.Tasks.xml", "ref/netstandard1.0/ru/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.0/zh-hant/System.Threading.Tasks.xml", "ref/netstandard1.3/System.Threading.Tasks.dll", "ref/netstandard1.3/System.Threading.Tasks.xml", "ref/netstandard1.3/de/System.Threading.Tasks.xml", "ref/netstandard1.3/es/System.Threading.Tasks.xml", "ref/netstandard1.3/fr/System.Threading.Tasks.xml", "ref/netstandard1.3/it/System.Threading.Tasks.xml", "ref/netstandard1.3/ja/System.Threading.Tasks.xml", "ref/netstandard1.3/ko/System.Threading.Tasks.xml", "ref/netstandard1.3/ru/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hans/System.Threading.Tasks.xml", "ref/netstandard1.3/zh-hant/System.Threading.Tasks.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.4.3.0.nupkg.sha512", "system.threading.tasks.nuspec"]}, "System.Threading.Tasks.Extensions/4.5.4": {"sha512": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "type": "package", "path": "system.threading.tasks.extensions/4.5.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net461/System.Threading.Tasks.Extensions.dll", "lib/net461/System.Threading.Tasks.Extensions.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard1.0/System.Threading.Tasks.Extensions.xml", "lib/netstandard2.0/System.Threading.Tasks.Extensions.dll", "lib/netstandard2.0/System.Threading.Tasks.Extensions.xml", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.dll", "lib/portable-net45+win8+wp8+wpa81/System.Threading.Tasks.Extensions.xml", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/netcoreapp2.1/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.tasks.extensions.4.5.4.nupkg.sha512", "system.threading.tasks.extensions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Threading.Thread/4.3.0": {"sha512": "OHmbT+Zz065NKII/ZHcH9XO1dEuLGI1L2k7uYss+9C1jLxTC9kTZZuzUOyXHayRk+dft9CiDf3I/QZ0t8JKyBQ==", "type": "package", "path": "system.threading.thread/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Threading.Thread.dll", "lib/netcore50/_._", "lib/netstandard1.3/System.Threading.Thread.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Threading.Thread.dll", "ref/netstandard1.3/System.Threading.Thread.dll", "ref/netstandard1.3/System.Threading.Thread.xml", "ref/netstandard1.3/de/System.Threading.Thread.xml", "ref/netstandard1.3/es/System.Threading.Thread.xml", "ref/netstandard1.3/fr/System.Threading.Thread.xml", "ref/netstandard1.3/it/System.Threading.Thread.xml", "ref/netstandard1.3/ja/System.Threading.Thread.xml", "ref/netstandard1.3/ko/System.Threading.Thread.xml", "ref/netstandard1.3/ru/System.Threading.Thread.xml", "ref/netstandard1.3/zh-hans/System.Threading.Thread.xml", "ref/netstandard1.3/zh-hant/System.Threading.Thread.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.thread.4.3.0.nupkg.sha512", "system.threading.thread.nuspec"]}, "System.Threading.ThreadPool/4.3.0": {"sha512": "k/+g4b7vjdd4aix83sTgC9VG6oXYKAktSfNIJUNGxPEj7ryEOfzHHhfnmsZvjxawwcD9HyWXKCXmPjX8U4zeSw==", "type": "package", "path": "system.threading.threadpool/4.3.0", "files": [".nupkg.metadata", ".signature.p7s", "ThirdPartyNotices.txt", "dotnet_library_license.txt", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Threading.ThreadPool.dll", "lib/netcore50/_._", "lib/netstandard1.3/System.Threading.ThreadPool.dll", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net46/System.Threading.ThreadPool.dll", "ref/netstandard1.3/System.Threading.ThreadPool.dll", "ref/netstandard1.3/System.Threading.ThreadPool.xml", "ref/netstandard1.3/de/System.Threading.ThreadPool.xml", "ref/netstandard1.3/es/System.Threading.ThreadPool.xml", "ref/netstandard1.3/fr/System.Threading.ThreadPool.xml", "ref/netstandard1.3/it/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ja/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ko/System.Threading.ThreadPool.xml", "ref/netstandard1.3/ru/System.Threading.ThreadPool.xml", "ref/netstandard1.3/zh-hans/System.Threading.ThreadPool.xml", "ref/netstandard1.3/zh-hant/System.Threading.ThreadPool.xml", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.threading.threadpool.4.3.0.nupkg.sha512", "system.threading.threadpool.nuspec"]}, "ZstdSharp.Port/0.8.0": {"sha512": "Z62eNBIu8E8YtbqlMy57tK3dV1+m2b9NhPeaYovB5exmLKvrGCqOhJTzrEUH5VyUWU6vwX3c1XHJGhW5HVs8dA==", "type": "package", "path": "zstdsharp.port/0.8.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net462/ZstdSharp.dll", "lib/net5.0/ZstdSharp.dll", "lib/net6.0/ZstdSharp.dll", "lib/net7.0/ZstdSharp.dll", "lib/net8.0/ZstdSharp.dll", "lib/netcoreapp3.1/ZstdSharp.dll", "lib/netstandard2.0/ZstdSharp.dll", "lib/netstandard2.1/ZstdSharp.dll", "zstdsharp.port.0.8.0.nupkg.sha512", "zstdsharp.port.nuspec"]}, "VolvoFlashWR.Communication/1.0.0": {"type": "project", "path": "../VolvoFlashWR.Communication/VolvoFlashWR.Communication.csproj", "msbuildProject": "../VolvoFlashWR.Communication/VolvoFlashWR.Communication.csproj"}, "VolvoFlashWR.Core/1.0.0": {"type": "project", "path": "../VolvoFlashWR.Core/VolvoFlashWR.Core.csproj", "msbuildProject": "../VolvoFlashWR.Core/VolvoFlashWR.Core.csproj"}, "VolvoFlashWR.UI/1.0.0": {"type": "project", "path": "../VolvoFlashWR.UI/VolvoFlashWR.UI.csproj", "msbuildProject": "../VolvoFlashWR.UI/VolvoFlashWR.UI.csproj"}}, "projectFileDependencyGroups": {"net8.0-windows7.0": ["HidSharp >= 2.1.0", "Microsoft.Extensions.Configuration >= 8.0.0", "Microsoft.Extensions.Configuration.Json >= 8.0.0", "Microsoft.Extensions.DependencyInjection >= 8.0.0", "Microsoft.Extensions.Logging >= 8.0.0", "Microsoft.Extensions.Logging.Abstractions >= 8.0.0", "Microsoft.NET.ILLink.Tasks >= 8.0.15", "Newtonsoft.Json >= 13.0.3", "System.IO.Ports >= 9.0.4", "VolvoFlashWR.UI >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Launcher\\VolvoFlashWR.Launcher.csproj", "projectName": "VolvoFlashWR.Launcher", "projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Launcher\\VolvoFlashWR.Launcher.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Launcher\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "projectReferences": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\VolvoFlashWR.UI.csproj": {"projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.UI\\VolvoFlashWR.UI.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0-windows7.0": {"targetAlias": "net8.0-windows", "dependencies": {"HidSharp": {"target": "Package", "version": "[2.1.0, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.NET.ILLink.Tasks": {"suppressParent": "All", "target": "Package", "version": "[8.0.15, )", "autoReferenced": true}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.IO.Ports": {"target": "Package", "version": "[9.0.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.WindowsDesktop.App.WPF": {"privateAssets": "none"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "logs": [{"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'OpenTK 3.3.1' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "OpenTK", "targetGraphs": ["net8.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'OpenTK.GLWpfControl 3.3.0' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "OpenTK.GLWpfControl", "targetGraphs": ["net8.0-windows7.0"]}, {"code": "NU1701", "level": "Warning", "warningLevel": 1, "message": "Package 'SkiaSharp.Views.WPF 3.116.1' was restored using '.NETFramework,Version=v4.6.1, .NETFramework,Version=v4.6.2, .NETFramework,Version=v4.7, .NETFramework,Version=v4.7.1, .NETFramework,Version=v4.7.2, .NETFramework,Version=v4.8, .NETFramework,Version=v4.8.1' instead of the project target framework 'net8.0-windows7.0'. This package may not be fully compatible with your project.", "libraryId": "SkiaSharp.Views.WPF", "targetGraphs": ["net8.0-windows7.0"]}]}