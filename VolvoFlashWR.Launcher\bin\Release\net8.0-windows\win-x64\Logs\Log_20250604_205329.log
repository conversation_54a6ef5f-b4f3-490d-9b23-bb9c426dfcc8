Log started at 6/4/2025 8:53:29 PM
2025-06-04 20:53:29.971 [Information] LoggingService: Logging service initialized
2025-06-04 20:53:29.989 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 20:53:29.991 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config
2025-06-04 20:53:30.065 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-06-04 20:53:30.066 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 20:53:30.067 [Information] App: Configuration service initialized successfully
2025-06-04 20:53:30.069 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 20:53:30.069 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: ''
2025-06-04 20:53:30.070 [Information] App: Environment variable exists: False, not 'false': True
2025-06-04 20:53:30.070 [Information] App: Final useDummyImplementations value: False
2025-06-04 20:53:30.070 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: ''
2025-06-04 20:53:30.071 [Information] App: usePatchedImplementation flag is: False
2025-06-04 20:53:30.071 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: ''
2025-06-04 20:53:30.072 [Information] App: APCI_LIBRARY_PATH environment variable is set to: ''
2025-06-04 20:53:30.072 [Information] App: VERBOSE_LOGGING environment variable is set to: ''
2025-06-04 20:53:30.072 [Information] App: verboseLogging flag is: False
2025-06-04 20:53:30.074 [Information] App: Verifying real hardware requirements...
2025-06-04 20:53:30.075 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\WUDFPuma.dll
2025-06-04 20:53:30.075 [Warning] App: ✗ Missing critical library: apci.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\apci.dll
2025-06-04 20:53:30.076 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlus.dll
2025-06-04 20:53:30.076 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlusData.dll
2025-06-04 20:53:30.076 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:53:30.077 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 20:53:30.077 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\config.json
2025-06-04 20:53:30.077 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 20:53:30.090 [Information] App: Creating standard VocomServiceFactory instance
2025-06-04 20:53:30.090 [Information] App: Successfully created standard VocomServiceFactory instance
2025-06-04 20:53:30.091 [Information] App: Using VolvoFlashWR.Communication.Vocom.VocomServiceFactory Vocom service factory
2025-06-04 20:53:30.091 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 20:53:30.141 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 20:53:30.145 [Information] VocomServiceFactory: Creating Vocom service with default settings
2025-06-04 20:53:30.146 [Information] VocomServiceFactory: Phoenix Vocom adapter not enabled, skipping
2025-06-04 20:53:30.146 [Information] VocomServiceFactory: Phoenix adapter initialization failed, attempting to create standard Vocom driver
2025-06-04 20:53:30.148 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 20:53:30.151 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 20:53:30.158 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 20:53:30.159 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:53:30.159 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:53:30.161 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:53:30.162 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 20:53:30.166 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-06-04 20:53:30.167 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-06-04 20:53:30.170 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 20:53:30.172 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-06-04 20:53:30.173 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-06-04 20:53:30.174 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 20:53:30.178 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 20:53:30.179 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 20:53:30.180 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 20:53:30.181 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 20:53:30.182 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 20:53:30.182 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 20:53:30.182 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 20:53:30.186 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 20:53:30.188 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 20:53:30.190 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 20:53:30.191 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 20:53:30.443 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 20:53:30.444 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 20:53:30.446 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 20:53:30.448 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 20:53:30.450 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 20:53:30.452 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 20:53:30.454 [Information] VocomService: Initializing Vocom service
2025-06-04 20:53:30.459 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:53:30.475 [Information] VocomService: PTT application is not running
2025-06-04 20:53:30.479 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:53:30.480 [Information] VocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 20:53:30.480 [Information] App: Initializing Vocom service
2025-06-04 20:53:30.481 [Information] VocomService: Initializing Vocom service
2025-06-04 20:53:30.481 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:53:30.496 [Information] VocomService: PTT application is not running
2025-06-04 20:53:30.497 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:53:30.500 [Information] VocomService: Scanning for Vocom devices
2025-06-04 20:53:30.505 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 20:53:30.541 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 20:53:30.547 [Information] VocomService: Found 2 Vocom devices
2025-06-04 20:53:30.548 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 20:53:30.551 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:53:30.552 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:53:30.570 [Information] VocomService: PTT application is not running
2025-06-04 20:53:30.573 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:53:30.576 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 20:53:31.381 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:53:31.382 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:53:31.383 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 20:53:31.388 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 20:53:31.392 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:31.393 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 20:53:31.398 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 20:53:31.400 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 20:53:31.401 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 20:53:31.404 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 20:53:31.407 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 20:53:31.416 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 20:53:31.419 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 20:53:31.425 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 20:53:31.437 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:53:31.444 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:53:31.458 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:53:31.459 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 20:53:31.460 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 20:53:31.461 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:53:31.461 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:53:31.462 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 20:53:31.462 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:53:31.462 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 20:53:31.463 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 20:53:31.468 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 20:53:31.469 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 20:53:31.469 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 20:53:31.469 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 20:53:31.470 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 20:53:31.470 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 20:53:31.471 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 20:53:31.471 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 20:53:31.476 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 20:53:31.481 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.481 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.481 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.482 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.484 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.486 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.700 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.700 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0140 = 0x01
2025-06-04 20:53:31.701 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.703 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.704 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.705 [Information] CANRegisterAccess: Successfully wrote value 0x01 to register 0x0140
2025-06-04 20:53:31.707 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 20:53:31.712 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 20:53:31.714 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.714 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.715 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.715 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.715 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.716 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.716 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.716 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.717 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.717 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.717 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.718 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.719 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.721 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-06-04 20:53:31.722 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-06-04 20:53:31.723 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-06-04 20:53:31.723 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.724 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.724 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.724 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.725 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.725 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.726 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.726 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0142 = 0x01
2025-06-04 20:53:31.726 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.727 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.727 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.727 [Information] CANRegisterAccess: Successfully wrote value 0x01 to register 0x0142
2025-06-04 20:53:31.728 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-06-04 20:53:31.728 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.728 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.729 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.729 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.729 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.729 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.730 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.730 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0143 = 0x1C
2025-06-04 20:53:31.731 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.731 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.731 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.731 [Information] CANRegisterAccess: Successfully wrote value 0x1C to register 0x0143
2025-06-04 20:53:31.732 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-06-04 20:53:31.732 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-06-04 20:53:31.732 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.733 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.733 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.733 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.734 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.734 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.734 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.735 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0141 = 0x80
2025-06-04 20:53:31.735 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.735 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.736 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.736 [Information] CANRegisterAccess: Successfully wrote value 0x80 to register 0x0141
2025-06-04 20:53:31.736 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-06-04 20:53:31.737 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-06-04 20:53:31.737 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.737 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.750 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.750 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.751 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.751 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.751 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.752 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x014B = 0x00
2025-06-04 20:53:31.752 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.753 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.753 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.753 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x014B
2025-06-04 20:53:31.754 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-06-04 20:53:31.754 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.755 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.755 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.755 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.756 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.756 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.756 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.757 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0010 = 0x00
2025-06-04 20:53:31.757 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.758 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.758 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.758 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0010
2025-06-04 20:53:31.758 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-06-04 20:53:31.759 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.759 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.759 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.760 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.760 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.761 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.761 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.761 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0014 = 0xFF
2025-06-04 20:53:31.761 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.762 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.762 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.762 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0014
2025-06-04 20:53:31.763 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-06-04 20:53:31.763 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.763 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.764 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.764 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.764 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.765 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.765 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.765 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0011 = 0x00
2025-06-04 20:53:31.766 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.766 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.766 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.767 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0011
2025-06-04 20:53:31.767 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-06-04 20:53:31.767 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.768 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.768 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.768 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.769 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.769 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.769 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.770 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0015 = 0xFF
2025-06-04 20:53:31.770 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.770 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.771 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.771 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0015
2025-06-04 20:53:31.771 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-06-04 20:53:31.772 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.772 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.772 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.773 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.773 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.773 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.774 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.774 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0012 = 0x00
2025-06-04 20:53:31.774 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.775 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.775 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.775 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0012
2025-06-04 20:53:31.775 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-06-04 20:53:31.776 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.776 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.776 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.777 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.777 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.777 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.777 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.778 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0016 = 0xFF
2025-06-04 20:53:31.778 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.778 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.779 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.779 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0016
2025-06-04 20:53:31.779 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-06-04 20:53:31.779 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.780 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.780 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.780 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.781 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.781 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.781 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.781 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0013 = 0x00
2025-06-04 20:53:31.782 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.782 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.782 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.783 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0013
2025-06-04 20:53:31.783 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-06-04 20:53:31.783 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.784 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.784 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.784 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.785 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.785 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.785 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.786 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0017 = 0xFF
2025-06-04 20:53:31.786 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.786 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.787 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.787 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0017
2025-06-04 20:53:31.787 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-06-04 20:53:31.788 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.788 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.789 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.789 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.790 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.790 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.791 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.791 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0014 = 0x00
2025-06-04 20:53:31.792 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.792 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.792 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.792 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0014
2025-06-04 20:53:31.793 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-06-04 20:53:31.793 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.793 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.794 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.794 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.794 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.794 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.795 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.795 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0018 = 0xFF
2025-06-04 20:53:31.795 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.796 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.796 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.796 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0018
2025-06-04 20:53:31.797 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-06-04 20:53:31.797 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.797 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.797 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.798 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.798 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.798 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.799 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.799 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0015 = 0x00
2025-06-04 20:53:31.800 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.800 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.800 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.801 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0015
2025-06-04 20:53:31.801 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-06-04 20:53:31.801 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.802 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.802 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.802 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.803 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.803 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.804 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.804 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0019 = 0xFF
2025-06-04 20:53:31.804 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.805 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.805 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.805 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0019
2025-06-04 20:53:31.806 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-06-04 20:53:31.806 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.806 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.807 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.807 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.807 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.808 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.808 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.808 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0016 = 0x00
2025-06-04 20:53:31.809 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.809 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.809 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.809 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0016
2025-06-04 20:53:31.810 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-06-04 20:53:31.810 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.810 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.810 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.811 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.811 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.811 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.811 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.812 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x001A = 0xFF
2025-06-04 20:53:31.812 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.812 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.813 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.813 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x001A
2025-06-04 20:53:31.814 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-06-04 20:53:31.814 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.814 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.814 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.815 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.815 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.815 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.816 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.816 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0017 = 0x00
2025-06-04 20:53:31.816 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.817 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.817 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.817 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0017
2025-06-04 20:53:31.818 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-06-04 20:53:31.818 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.818 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.818 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.819 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.819 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.819 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.820 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.820 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x001B = 0xFF
2025-06-04 20:53:31.820 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.821 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.821 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.821 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x001B
2025-06-04 20:53:31.822 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-06-04 20:53:31.822 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-06-04 20:53:31.822 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.822 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.823 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.823 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.824 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.824 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.825 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.825 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0140 = 0x0C
2025-06-04 20:53:31.825 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.825 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.826 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:31.826 [Information] CANRegisterAccess: Successfully wrote value 0x0C to register 0x0140
2025-06-04 20:53:31.827 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-06-04 20:53:31.827 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:53:31.827 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.827 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.828 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.828 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.828 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.829 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.829 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.829 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.830 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.830 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.830 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.830 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.831 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.840 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.841 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.841 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.841 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.842 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.842 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.842 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.843 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.843 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.843 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.845 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.845 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.846 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.852 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.852 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.853 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.853 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.853 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.854 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.854 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.854 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.855 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.855 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.855 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.856 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.856 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.861 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.862 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.862 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.862 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.863 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.863 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.863 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.864 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.864 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.864 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.864 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.865 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.865 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.871 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.872 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.872 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.873 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.873 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.874 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.874 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.874 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.875 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.875 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.875 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.875 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.876 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.882 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.883 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.883 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.883 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.884 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.884 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.884 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.884 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.885 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.885 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.885 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.886 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.886 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.892 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.893 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.893 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.893 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.894 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.894 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.894 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.895 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.895 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.895 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.895 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.896 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.896 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.902 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.903 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.903 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.903 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.904 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.904 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.904 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.905 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.905 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.905 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.906 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.906 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.907 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.913 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.913 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.914 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.914 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.914 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.915 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.915 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.915 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.916 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.916 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.916 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.917 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.917 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.923 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.924 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.924 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.924 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.925 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.925 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.925 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.926 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.926 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.926 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.927 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.927 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.927 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.933 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.933 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.934 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.934 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.934 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.935 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.935 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.935 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.936 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.936 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.937 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.937 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.937 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.943 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.944 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.944 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.945 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.945 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.946 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.946 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.947 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.947 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.947 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.948 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.948 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.949 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.955 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.956 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.957 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.957 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.958 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.958 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.958 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.958 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.959 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.959 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.959 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.960 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.960 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.966 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.966 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.967 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.967 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.967 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.968 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.968 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.968 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.968 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.969 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.969 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.969 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.970 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.979 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.979 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.980 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.980 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.980 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.980 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.981 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.981 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.981 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.981 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.982 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.982 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.982 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:31.995 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:31.996 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:31.996 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:31.997 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:31.997 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:31.997 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.997 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.998 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:31.998 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:31.998 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.999 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:31.999 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:31.999 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:32.007 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:32.008 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.008 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.008 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.009 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.009 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.010 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.010 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.010 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:32.011 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.015 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.016 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:32.016 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:32.024 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:32.025 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.025 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.025 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.026 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.026 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.026 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.027 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.027 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:32.027 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.028 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.028 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:32.029 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:32.035 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:32.036 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.036 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.036 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.037 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.037 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.038 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.040 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.041 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:32.041 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.041 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.042 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:32.042 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:32.049 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:32.050 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.050 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.050 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.051 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.051 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.051 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.051 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.052 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:32.052 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.052 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.053 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:32.053 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:32.059 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:53:32.060 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to exit
2025-06-04 20:53:32.063 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:53:32.064 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:53:32.075 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 20:53:32.076 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 20:53:32.076 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 20:53:32.079 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.080 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.080 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.080 [Information] VocomService: Using generic data transfer
2025-06-04 20:53:32.083 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 20:53:32.084 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 20:53:32.084 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.084 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.085 [Warning] VocomNativeInterop: WUDFPuma mode: Invalid CAN frame format or length 4
2025-06-04 20:53:32.085 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.087 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 20:53:32.089 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:32.092 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 20:53:32.093 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 20:53:32.095 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:53:32.096 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:53:32.107 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:53:32.108 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 20:53:32.108 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 20:53:32.119 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 20:53:32.130 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 20:53:32.141 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 20:53:32.152 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 20:53:32.163 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:53:32.165 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:53:32.165 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:53:32.176 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:53:32.177 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 20:53:32.177 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 20:53:32.188 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 20:53:32.199 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 20:53:32.210 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 20:53:32.221 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 20:53:32.232 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 20:53:32.243 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:53:32.245 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:53:32.246 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:53:32.257 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:53:32.258 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 20:53:32.259 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:53:32.259 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:53:32.259 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 20:53:32.260 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:53:32.260 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 20:53:32.260 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 20:53:32.261 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 20:53:32.261 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 20:53:32.261 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 20:53:32.262 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 20:53:32.262 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 20:53:32.262 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 20:53:32.263 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 20:53:32.263 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 20:53:32.263 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 20:53:32.364 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:53:32.365 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 20:53:32.368 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 20:53:32.370 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:32.370 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 20:53:32.371 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 20:53:32.371 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:32.372 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 20:53:32.373 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 20:53:32.373 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:32.374 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 20:53:32.374 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 20:53:32.375 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:32.375 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 20:53:32.376 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 20:53:32.377 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 20:53:32.382 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 20:53:32.384 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 20:53:32.389 [Information] BackupService: Initializing backup service
2025-06-04 20:53:32.390 [Information] BackupService: Backup service initialized successfully
2025-06-04 20:53:32.390 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 20:53:32.391 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 20:53:32.394 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 20:53:32.441 [Information] BackupService: Compressing backup data
2025-06-04 20:53:32.449 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-06-04 20:53:32.450 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 20:53:32.450 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 20:53:32.452 [Information] BackupService: Compressing backup data
2025-06-04 20:53:32.459 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-06-04 20:53:32.461 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 20:53:32.461 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 20:53:32.463 [Information] BackupService: Compressing backup data
2025-06-04 20:53:32.465 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-06-04 20:53:32.465 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 20:53:32.466 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 20:53:32.466 [Information] BackupService: Compressing backup data
2025-06-04 20:53:32.467 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-04 20:53:32.468 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 20:53:32.468 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 20:53:32.469 [Information] BackupService: Compressing backup data
2025-06-04 20:53:32.470 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (450 bytes)
2025-06-04 20:53:32.470 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 20:53:32.470 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 20:53:32.471 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 20:53:32.472 [Information] BackupService: Compressing backup data
2025-06-04 20:53:32.478 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (514 bytes)
2025-06-04 20:53:32.478 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 20:53:32.479 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 20:53:32.481 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 20:53:32.484 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 20:53:32.487 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 20:53:32.557 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-06-04 20:53:32.558 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 20:53:32.559 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 20:53:32.559 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 20:53:32.560 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 20:53:32.561 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 20:53:32.562 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 20:53:32.566 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 20:53:32.566 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 20:53:32.578 [Information] LicensingService: Initializing licensing service
2025-06-04 20:53:32.632 [Information] LicensingService: License information loaded successfully
2025-06-04 20:53:32.635 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 20:53:32.635 [Information] App: Licensing service initialized successfully
2025-06-04 20:53:32.636 [Information] App: License status: Trial
2025-06-04 20:53:32.636 [Information] App: Trial period: 30 days remaining
2025-06-04 20:53:32.637 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 20:53:32.826 [Information] VocomService: Initializing Vocom service
2025-06-04 20:53:32.826 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:53:32.838 [Information] VocomService: PTT application is not running
2025-06-04 20:53:32.840 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:53:32.891 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 20:53:32.891 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 20:53:32.891 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 20:53:32.892 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 20:53:32.892 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 20:53:32.893 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 20:53:32.894 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 20:53:32.895 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 20:53:32.895 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:53:32.896 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:53:32.906 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:53:32.907 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 20:53:32.907 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 20:53:32.908 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:53:32.908 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:53:32.908 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 20:53:32.909 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:53:32.909 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 20:53:32.909 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 20:53:32.910 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 20:53:32.910 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 20:53:32.910 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 20:53:32.910 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 20:53:32.911 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 20:53:32.911 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 20:53:32.911 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 20:53:32.912 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 20:53:32.912 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 20:53:32.912 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.913 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.913 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.913 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.913 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.913 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.914 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.914 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0140 = 0x01
2025-06-04 20:53:32.914 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.915 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.916 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.916 [Information] CANRegisterAccess: Successfully wrote value 0x01 to register 0x0140
2025-06-04 20:53:32.916 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 20:53:32.917 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 20:53:32.917 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:32.917 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.917 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.917 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.918 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.918 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.919 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.919 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.919 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:32.920 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.921 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.921 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:32.922 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:32.923 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-06-04 20:53:32.923 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-06-04 20:53:32.924 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-06-04 20:53:32.924 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.925 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.925 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.925 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.925 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.926 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.927 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.927 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0142 = 0x01
2025-06-04 20:53:32.928 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.928 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.929 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.929 [Information] CANRegisterAccess: Successfully wrote value 0x01 to register 0x0142
2025-06-04 20:53:32.929 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-06-04 20:53:32.930 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.930 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.930 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.931 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.931 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.931 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.931 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.932 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0143 = 0x1C
2025-06-04 20:53:32.932 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.932 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.933 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.933 [Information] CANRegisterAccess: Successfully wrote value 0x1C to register 0x0143
2025-06-04 20:53:32.934 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-06-04 20:53:32.934 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-06-04 20:53:32.934 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.934 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.935 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.935 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.935 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.936 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.936 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.936 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0141 = 0x80
2025-06-04 20:53:32.937 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.937 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.937 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.938 [Information] CANRegisterAccess: Successfully wrote value 0x80 to register 0x0141
2025-06-04 20:53:32.938 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-06-04 20:53:32.939 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-06-04 20:53:32.939 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.939 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.940 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.940 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.940 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.941 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.941 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.941 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x014B = 0x00
2025-06-04 20:53:32.942 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.942 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.943 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.943 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x014B
2025-06-04 20:53:32.943 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-06-04 20:53:32.944 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.944 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.944 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.944 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.945 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.945 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.945 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.946 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0010 = 0x00
2025-06-04 20:53:32.946 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.946 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.947 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.947 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0010
2025-06-04 20:53:32.948 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-06-04 20:53:32.948 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.948 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.948 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.949 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.949 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.949 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.952 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.953 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0014 = 0xFF
2025-06-04 20:53:32.953 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.954 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.954 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.955 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0014
2025-06-04 20:53:32.955 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-06-04 20:53:32.955 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.956 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.956 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.957 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.957 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.957 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.958 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.959 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0011 = 0x00
2025-06-04 20:53:32.959 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.960 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.960 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.961 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0011
2025-06-04 20:53:32.961 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-06-04 20:53:32.962 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.962 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.962 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.963 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.963 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.963 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.963 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.964 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0015 = 0xFF
2025-06-04 20:53:32.964 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.965 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.965 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.965 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0015
2025-06-04 20:53:32.966 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-06-04 20:53:32.966 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.966 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.966 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.967 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.967 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.967 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.968 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.968 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0012 = 0x00
2025-06-04 20:53:32.968 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.969 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.969 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.970 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0012
2025-06-04 20:53:32.970 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-06-04 20:53:32.970 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.971 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.971 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.972 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.972 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.972 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.973 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.974 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0016 = 0xFF
2025-06-04 20:53:32.974 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.975 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.975 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.976 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0016
2025-06-04 20:53:32.976 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-06-04 20:53:32.976 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.977 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.977 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.977 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.978 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.978 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.978 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.979 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0013 = 0x00
2025-06-04 20:53:32.979 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.979 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.980 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.980 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0013
2025-06-04 20:53:32.981 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-06-04 20:53:32.981 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.981 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.981 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.982 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.982 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.982 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.983 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.983 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0017 = 0xFF
2025-06-04 20:53:32.984 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.984 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.985 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.985 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0017
2025-06-04 20:53:32.986 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-06-04 20:53:32.986 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.986 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.986 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.987 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.987 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.987 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.988 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.988 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0014 = 0x00
2025-06-04 20:53:32.989 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.990 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.991 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.991 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0014
2025-06-04 20:53:32.992 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-06-04 20:53:32.992 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.993 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.993 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.993 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.994 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.994 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.995 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:32.995 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0018 = 0xFF
2025-06-04 20:53:32.996 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.996 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:32.997 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:32.997 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0018
2025-06-04 20:53:32.998 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-06-04 20:53:32.998 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:32.998 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:32.999 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:32.999 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:32.999 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.000 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.000 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.001 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0015 = 0x00
2025-06-04 20:53:33.001 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.001 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.002 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.002 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0015
2025-06-04 20:53:33.003 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-06-04 20:53:33.003 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.003 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.003 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.004 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.004 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.004 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.008 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.008 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0019 = 0xFF
2025-06-04 20:53:33.009 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.009 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.009 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.010 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x0019
2025-06-04 20:53:33.010 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-06-04 20:53:33.010 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.011 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.011 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.011 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.011 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.012 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.014 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.014 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0016 = 0x00
2025-06-04 20:53:33.015 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.015 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.016 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.016 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0016
2025-06-04 20:53:33.016 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-06-04 20:53:33.017 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.017 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.017 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.018 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.018 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.018 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.018 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.019 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x001A = 0xFF
2025-06-04 20:53:33.019 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.019 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.020 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.020 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x001A
2025-06-04 20:53:33.021 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-06-04 20:53:33.021 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.022 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.022 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.022 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.023 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.023 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.024 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.025 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0017 = 0x00
2025-06-04 20:53:33.025 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.026 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.026 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.027 [Information] CANRegisterAccess: Successfully wrote value 0x00 to register 0x0017
2025-06-04 20:53:33.027 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-06-04 20:53:33.027 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.028 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.028 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.028 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.029 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.029 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.029 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.030 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x001B = 0xFF
2025-06-04 20:53:33.030 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.031 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.031 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.031 [Information] CANRegisterAccess: Successfully wrote value 0xFF to register 0x001B
2025-06-04 20:53:33.032 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-06-04 20:53:33.032 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-06-04 20:53:33.032 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.032 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.033 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.033 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.033 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.034 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.034 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.034 [Information] VocomNativeInterop: WUDFPuma mode: Write register 0x0140 = 0x0C
2025-06-04 20:53:33.035 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.035 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.035 [Information] VocomService: Sent 5 bytes and received 2 bytes response
2025-06-04 20:53:33.036 [Information] CANRegisterAccess: Successfully wrote value 0x0C to register 0x0140
2025-06-04 20:53:33.036 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-06-04 20:53:33.036 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:53:33.037 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.037 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.037 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.038 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.038 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.038 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.038 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.041 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.041 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.042 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.042 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.043 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.043 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.049 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.050 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.050 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.050 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.079 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.079 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.079 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.081 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.081 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.081 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.082 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.082 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.083 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.089 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.089 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.089 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.090 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.090 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.090 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.091 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.091 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.092 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.092 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.093 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.093 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.094 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.099 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.100 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.100 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.100 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.101 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.101 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.101 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.102 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.102 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.102 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.102 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.103 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.103 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.109 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.110 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.110 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.110 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.111 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.111 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.111 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.111 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.112 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.112 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.113 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.113 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.113 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.119 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.120 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.120 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.120 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.121 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.121 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.121 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.122 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.122 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.122 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.123 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.123 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.124 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.130 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.131 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.131 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.132 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.132 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.132 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.133 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.133 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.133 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.134 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.134 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.134 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.135 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.141 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.142 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.142 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.142 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.142 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.143 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.143 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.143 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.144 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.144 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.144 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.145 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.145 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.151 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.152 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.152 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.152 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.152 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.153 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.153 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.153 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.154 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.154 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.154 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.155 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.155 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.162 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.163 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.163 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.163 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.164 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.164 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.164 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.165 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.165 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.165 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.166 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.166 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.167 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.173 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.174 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.174 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.175 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.175 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.175 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.175 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.176 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.176 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.176 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.177 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.177 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.178 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.183 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.184 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.184 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.184 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.185 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.185 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.185 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.185 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.186 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.186 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.187 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.187 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.188 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.194 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.195 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.195 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.195 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.196 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.196 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.196 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.197 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.197 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.197 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.198 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.198 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.198 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.204 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.205 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.206 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.206 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.207 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.207 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.207 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.208 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.209 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.209 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.210 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.210 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.210 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.216 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.217 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.217 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.217 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.218 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.218 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.218 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.219 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.219 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.219 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.220 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.220 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.221 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.227 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.228 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.228 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.228 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.229 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.229 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.229 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.230 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.230 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.230 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.231 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.231 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.232 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.238 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.239 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.240 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.240 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.240 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.240 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.241 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.242 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.242 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.243 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.243 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.244 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.244 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.250 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.251 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.251 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.251 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.252 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.252 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.252 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.252 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.253 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.253 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.253 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.254 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.254 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.260 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.261 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.261 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.261 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.261 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.262 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.262 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.262 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.263 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.263 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.263 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.264 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.264 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.270 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:53:33.271 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.271 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.271 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.272 [Information] VocomService: Detected CAN protocol request
2025-06-04 20:53:33.272 [Information] VocomDriver: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.272 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.273 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.274 [Information] VocomNativeInterop: WUDFPuma mode: Read register 0x0141 = 0x01
2025-06-04 20:53:33.274 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.275 [Information] VocomDriver: Successfully sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.275 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.276 [Information] CANRegisterAccess: Read value 0x01 from register 0x0141
2025-06-04 20:53:33.282 [Warning] CANRegisterAccess: Timeout waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:53:33.283 [Error] CANProtocolHandler: Timeout waiting for CAN initialization mode to exit
2025-06-04 20:53:33.283 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:53:33.284 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:53:33.294 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 20:53:33.295 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 20:53:33.295 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 20:53:33.295 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:53:33.296 [Information] VocomService: Using real implementation for SendAndReceiveDataAsync
2025-06-04 20:53:33.296 [Information] VocomService: Sending data to device 88890300-BT using Vocom driver
2025-06-04 20:53:33.296 [Information] VocomService: Using generic data transfer
2025-06-04 20:53:33.296 [Information] VocomDriver: Sending raw data to Vocom device 88890300-BT
2025-06-04 20:53:33.297 [Warning] VocomDriver: Unknown protocol identifier in raw data: 0x02, using CAN as default
2025-06-04 20:53:33.297 [Information] VocomNativeInterop: Sending CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.297 [Information] VocomNativeInterop: WUDFPuma mode: Sending CAN frame to real hardware for device 88890300-BT
2025-06-04 20:53:33.298 [Warning] VocomNativeInterop: WUDFPuma mode: Invalid CAN frame format or length 4
2025-06-04 20:53:33.298 [Information] VocomNativeInterop: Sent CAN frame to Vocom device 88890300-BT
2025-06-04 20:53:33.299 [Information] VocomDriver: Successfully sent raw data to Vocom device 88890300-BT
2025-06-04 20:53:33.299 [Information] VocomService: Sent 4 bytes and received 3 bytes response
2025-06-04 20:53:33.299 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 20:53:33.300 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 20:53:33.300 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:53:33.301 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:53:33.311 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:53:33.312 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 20:53:33.312 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 20:53:33.324 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 20:53:33.335 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 20:53:33.346 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 20:53:33.357 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 20:53:33.368 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:53:33.369 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:53:33.369 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:53:33.380 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:53:33.381 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 20:53:33.381 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 20:53:33.392 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 20:53:33.403 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 20:53:33.414 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 20:53:33.425 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 20:53:33.436 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 20:53:33.447 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:53:33.448 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:53:33.448 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:53:33.459 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:53:33.460 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 20:53:33.460 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:53:33.460 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:53:33.461 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 20:53:33.461 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:53:33.461 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 20:53:33.462 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 20:53:33.462 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 20:53:33.462 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 20:53:33.462 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 20:53:33.463 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 20:53:33.463 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 20:53:33.463 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 20:53:33.463 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 20:53:33.464 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 20:53:33.464 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 20:53:33.564 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:53:33.565 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 20:53:33.565 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 20:53:33.566 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:33.566 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 20:53:33.567 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 20:53:33.567 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:33.567 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 20:53:33.568 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 20:53:33.568 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:33.568 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 20:53:33.569 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 20:53:33.569 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:53:33.569 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 20:53:33.570 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 20:53:33.621 [Information] BackupService: Initializing backup service
2025-06-04 20:53:33.621 [Information] BackupService: Backup service initialized successfully
2025-06-04 20:53:33.673 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 20:53:33.673 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 20:53:33.675 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-06-04 20:53:33.675 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 20:53:33.726 [Information] BackupService: Getting predefined backup categories
2025-06-04 20:53:33.778 [Information] MainViewModel: Services initialized successfully
2025-06-04 20:53:33.781 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 20:53:33.782 [Information] VocomService: Scanning for Vocom devices
2025-06-04 20:53:33.783 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 20:53:33.784 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 20:53:33.786 [Information] VocomService: Found 2 Vocom devices
2025-06-04 20:53:33.787 [Information] MainViewModel: Found 2 Vocom device(s)
