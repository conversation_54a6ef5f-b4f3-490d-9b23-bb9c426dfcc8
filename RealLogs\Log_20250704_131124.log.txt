Log started at 7/4/2025 1:11:24 PM
2025-07-04 13:11:24.688 [Information] LoggingService: Logging service initialized
2025-07-04 13:11:24.695 [Information] App: Starting integrated application initialization
2025-07-04 13:11:24.698 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-04 13:11:24.700 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-04 13:11:24.701 [Information] IntegratedStartupService: Setting up application environment
2025-07-04 13:11:24.702 [Information] IntegratedStartupService: Application environment setup completed
2025-07-04 13:11:24.703 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-04 13:11:24.705 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-04 13:11:24.707 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-04 13:11:24.844 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-04 13:11:24.845 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-04 13:11:24.849 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-04 13:11:24.851 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-04 13:11:24.854 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-04 13:11:24.855 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-04 13:11:24.855 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-04 13:11:24.855 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-04 13:11:24.857 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-04 13:11:24.857 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:11:24.858 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:11:24.858 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:11:24.858 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:11:24.859 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:11:24.859 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:11:24.861 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-04 13:11:24.863 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-04 13:11:24.870 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-04 13:11:24.870 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-04 13:11:24.871 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-04 13:11:24.872 [Information] LibraryExtractor: Starting library extraction process
2025-07-04 13:11:24.874 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-04 13:11:24.875 [Information] LibraryExtractor: Copying system libraries
2025-07-04 13:11:24.878 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-04 13:11:24.888 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-04 13:11:39.031 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-04 13:11:59.452 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-04 13:12:27.721 [Information] LibraryExtractor: Verifying library extraction
2025-07-04 13:12:27.722 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-04 13:12:27.722 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-04 13:12:27.723 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-04 13:12:27.723 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-04 13:12:27.723 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-04 13:12:27.725 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-04 13:12:27.726 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-04 13:12:27.727 [Information] DependencyManager: Initializing dependency manager
2025-07-04 13:12:27.728 [Information] DependencyManager: Setting up library search paths
2025-07-04 13:12:27.729 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:12:27.729 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:12:27.729 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-04 13:12:27.730 [Information] DependencyManager: Updated PATH environment variable
2025-07-04 13:12:27.731 [Information] DependencyManager: Verifying required directories
2025-07-04 13:12:27.731 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:12:27.731 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:12:27.732 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-04 13:12:27.732 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-04 13:12:27.733 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-04 13:12:27.737 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-04 13:12:27.740 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-04 13:12:27.741 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-04 13:12:27.742 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-04 13:12:27.742 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-04 13:12:27.744 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 13:12:27.746 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 13:12:27.746 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:12:27.747 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:12:27.747 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:12:27.748 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-04 13:12:27.749 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-04 13:12:27.750 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-04 13:12:27.751 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:12:27.753 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-04 13:12:27.754 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-04 13:12:27.755 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-04 13:12:27.755 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-04 13:12:27.757 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-04 13:12:27.958 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-04 13:12:27.959 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-04 13:12:27.960 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-04 13:12:27.962 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-04 13:12:27.962 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-04 13:12:27.964 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-04 13:12:27.964 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-04 13:12:27.965 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-04 13:12:27.965 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-04 13:12:27.966 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-04 13:12:27.966 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-04 13:12:27.967 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-04 13:12:27.967 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 13:12:27.968 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 13:12:27.968 [Information] DependencyManager: Setting up environment variables
2025-07-04 13:12:27.968 [Information] DependencyManager: Environment variables configured
2025-07-04 13:12:27.969 [Information] DependencyManager: Verifying library loading status
2025-07-04 13:12:29.601 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-04 13:12:29.602 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-04 13:12:29.602 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-04 13:12:29.604 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-04 13:12:29.605 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-04 13:12:29.608 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-04 13:12:29.609 [Information] IntegratedStartupService: Verifying system readiness
2025-07-04 13:12:29.609 [Information] IntegratedStartupService: System readiness verification passed
2025-07-04 13:12:29.610 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-04 13:12:29.611 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-04 13:12:29.611 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-04 13:12:29.611 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:12:29.611 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:12:29.612 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-04 13:12:29.612 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-04 13:12:29.612 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-04 13:12:29.613 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:12:29.613 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-04 13:12:29.613 [Information] App: Integrated startup completed successfully
2025-07-04 13:12:29.615 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-04 13:12:29.633 [Information] App: Initializing application services
2025-07-04 13:12:29.634 [Information] AppConfigurationService: Initializing configuration service
2025-07-04 13:12:29.635 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-04 13:12:29.663 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-04 13:12:29.664 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-04 13:12:29.665 [Information] App: Configuration service initialized successfully
2025-07-04 13:12:29.666 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-04 13:12:29.666 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-04 13:12:29.670 [Information] App: Environment variable exists: True, not 'false': False
2025-07-04 13:12:29.671 [Information] App: Final useDummyImplementations value: False
2025-07-04 13:12:29.671 [Information] App: Updating config to NOT use dummy implementations
2025-07-04 13:12:29.679 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-04 13:12:29.680 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-04 13:12:29.680 [Information] App: usePatchedImplementation flag is: True
2025-07-04 13:12:29.680 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-04 13:12:29.681 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-04 13:12:29.681 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-04 13:12:29.681 [Information] App: verboseLogging flag is: True
2025-07-04 13:12:29.682 [Information] App: Verifying real hardware requirements...
2025-07-04 13:12:29.683 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-04 13:12:29.683 [Information] App: ✓ Found critical library: apci.dll
2025-07-04 13:12:29.683 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-04 13:12:29.684 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-04 13:12:29.684 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:12:29.684 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-04 13:12:29.684 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-04 13:12:29.685 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-04 13:12:29.695 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-04 13:12:29.696 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-04 13:12:29.697 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-04 13:12:29.698 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-04 13:12:29.698 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-04 13:12:29.711 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-04 13:12:29.712 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-04 13:12:29.712 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-04 13:12:29.712 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-04 13:12:29.713 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-04 13:12:29.743 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-04 13:12:29.746 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-04 13:12:29.747 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-04 13:12:29.747 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-04 13:12:29.747 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-04 13:12:29.747 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-04 13:12:29.749 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-04 13:12:29.751 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-04 13:12:29.753 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-04 13:12:29.754 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 13:12:29.754 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-04 13:12:29.761 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 13:12:29.771 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-04 13:12:29.772 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-04 13:12:29.773 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-04 13:12:29.773 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:12:29.774 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-04 13:12:29.774 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:12:29.774 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-04 13:12:29.775 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:12:29.775 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-04 13:12:29.776 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-04 13:12:29.776 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-04 13:12:29.777 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-04 13:12:29.777 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-04 13:12:29.778 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-04 13:12:29.778 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-04 13:12:29.780 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-04 13:12:29.781 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-04 13:12:29.781 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:12:29.782 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-04 13:12:29.782 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-04 13:12:29.783 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-04 13:12:29.784 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-04 13:12:29.784 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-04 13:12:29.785 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-04 13:12:29.785 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-04 13:12:29.785 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-04 13:12:29.786 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-04 13:12:29.786 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-04 13:12:29.787 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-07-04 13:12:29.788 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-07-04 13:12:29.788 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-04 13:12:29.789 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-04 13:12:29.789 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-04 13:12:29.789 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:12:29.789 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-04 13:12:29.790 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-04 13:12:29.790 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-04 13:12:29.792 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-04 13:12:29.793 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-04 13:12:29.794 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-04 13:12:29.798 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-04 13:12:29.799 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-04 13:12:29.799 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:12:29.799 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:12:29.801 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-04 13:12:29.802 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.803 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.803 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-04 13:12:29.803 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.804 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.804 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-04 13:12:29.805 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.805 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-04 13:12:29.806 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.806 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-04 13:12:29.807 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.808 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.808 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-04 13:12:29.808 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.809 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.809 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-04 13:12:29.810 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:12:29.811 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:12:29.812 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.812 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.813 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-04 13:12:29.813 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:12:29.814 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.814 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-04 13:12:29.815 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.816 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.816 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-04 13:12:29.817 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:12:29.817 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:12:29.818 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-04 13:12:29.819 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:12:29.820 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:12:29.821 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:12:29.823 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-04 13:12:29.823 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:12:29.823 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-04 13:12:29.824 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-04 13:12:29.824 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-04 13:12:29.825 [Information] VocomDriver: Initializing Vocom driver
2025-07-04 13:12:29.826 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-04 13:12:29.828 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-04 13:12:29.829 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:12:29.829 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:12:29.830 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:12:29.830 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-04 13:12:29.831 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-04 13:12:29.832 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-04 13:12:29.832 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-04 13:12:29.833 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-04 13:12:29.833 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-04 13:12:29.834 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:12:29.835 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-04 13:12:29.837 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-04 13:12:29.837 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-04 13:12:29.838 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-04 13:12:29.838 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-04 13:12:29.839 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-04 13:12:29.839 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-04 13:12:29.840 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-04 13:12:29.840 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-04 13:12:29.840 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-04 13:12:29.841 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-04 13:12:29.841 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-04 13:12:29.841 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-04 13:12:29.841 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-04 13:12:29.842 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-04 13:12:29.842 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-04 13:12:29.842 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-04 13:12:29.842 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-04 13:12:29.843 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-04 13:12:29.843 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-04 13:12:29.843 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-04 13:12:29.843 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-04 13:12:29.844 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-04 13:12:29.844 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-04 13:12:29.844 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-04 13:12:29.844 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-04 13:12:29.845 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-04 13:12:29.845 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-04 13:12:29.845 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-04 13:12:29.845 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-04 13:12:29.846 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-04 13:12:29.846 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-04 13:12:29.846 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-04 13:12:29.846 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-04 13:12:29.846 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-04 13:12:29.847 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-04 13:12:29.847 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-04 13:12:29.847 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-04 13:12:29.847 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-04 13:12:29.847 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-04 13:12:29.848 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-04 13:12:29.848 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-04 13:12:29.848 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-04 13:12:29.848 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-04 13:12:29.848 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-04 13:12:29.849 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-04 13:12:29.849 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-04 13:12:29.849 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-04 13:12:29.849 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-04 13:12:29.850 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-04 13:12:29.851 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-04 13:12:29.852 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-04 13:12:29.852 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-04 13:12:29.852 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-04 13:12:29.852 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-04 13:12:29.853 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-04 13:12:29.853 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-04 13:12:29.855 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-04 13:12:29.856 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-04 13:12:29.857 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-04 13:12:29.858 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-04 13:12:29.894 [Information] WiFiCommunicationService: WiFi is available
2025-07-04 13:12:29.894 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-04 13:12:29.896 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-04 13:12:29.896 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-04 13:12:29.898 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-04 13:12:29.898 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-04 13:12:29.899 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:12:29.900 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:12:29.901 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:12:29.903 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:12:29.907 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:12:29.907 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:12:29.907 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:12:29.908 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:12:29.908 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:12:29.909 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:12:29.910 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:29.926 [Information] VocomService: PTT application is not running
2025-07-04 13:12:29.928 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:12:29.929 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-04 13:12:29.929 [Information] App: Initializing Vocom service
2025-07-04 13:12:29.929 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:12:29.930 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:12:29.930 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:12:29.930 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:12:29.931 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:12:29.931 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:12:29.931 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:12:29.931 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:12:29.932 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:12:29.932 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:12:29.932 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:29.944 [Information] VocomService: PTT application is not running
2025-07-04 13:12:29.945 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:12:29.947 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:12:29.947 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:12:29.949 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:12:29.951 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:12:30.228 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:12:30.229 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:12:30.229 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:12:30.230 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:12:30.231 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:12:30.333 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:12:30.334 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:12:30.335 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:12:30.335 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-04 13:12:30.337 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:12:31.050 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:31.059 [Information] VocomService: PTT application is not running
2025-07-04 13:12:31.062 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:12:31.062 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.063 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:31.072 [Information] VocomService: PTT application is not running
2025-07-04 13:12:31.072 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.073 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.074 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:12:31.074 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:12:31.074 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.100 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:12:31.101 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.102 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:12:31.103 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:12:31.104 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:12:31.104 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:12:31.104 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:12:31.104 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:12:31.104 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:31.105 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-04 13:12:31.107 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-04 13:12:31.109 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:31.110 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-04 13:12:31.112 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:12:31.114 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:12:31.114 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:12:31.116 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:12:31.117 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:12:31.119 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:12:31.120 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:12:31.122 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:12:31.126 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:12:31.129 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:12:31.129 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:31.131 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:12:31.131 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:12:31.131 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:31.132 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:12:31.133 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:12:31.133 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:31.134 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:12:31.135 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:12:31.135 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:31.136 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:12:31.137 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:12:31.137 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:31.137 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:12:31.139 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:12:31.140 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:31.140 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:12:31.140 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:12:31.141 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:31.141 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:12:31.141 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:12:31.142 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:31.142 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:12:31.142 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:12:31.143 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:31.143 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:12:31.143 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:12:31.143 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:12:31.145 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:12:31.145 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:12:31.145 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:12:31.146 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:12:31.146 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:12:31.322 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:12:31.322 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:12:31.322 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:12:31.323 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:12:31.323 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:12:31.422 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:12:31.423 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:12:31.423 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:12:31.424 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:12:31.424 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:12:31.424 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:31.433 [Information] VocomService: PTT application is not running
2025-07-04 13:12:31.434 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:12:31.434 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.434 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:31.444 [Information] VocomService: PTT application is not running
2025-07-04 13:12:31.444 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.444 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.444 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:12:31.445 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:12:31.445 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.445 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:12:31.445 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:31.446 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:12:31.446 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:12:31.446 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:12:31.446 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:12:31.447 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:12:31.447 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:12:31.447 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:12:31.448 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:31.448 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:12:31.448 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:31.448 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:12:31.449 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:12:31.449 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:12:31.449 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-04 13:12:32.450 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-04 13:12:32.451 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:12:32.451 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:12:32.451 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:12:32.451 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:12:32.452 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:12:32.452 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:12:32.453 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:12:32.454 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:12:32.454 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:12:32.454 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:12:32.454 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:32.455 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:12:32.455 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:12:32.455 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:32.455 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:12:32.455 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:12:32.456 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:32.456 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:12:32.456 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:12:32.456 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:32.457 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:12:32.457 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:12:32.457 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:32.457 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:12:32.458 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:12:32.458 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:32.458 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:12:32.458 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:12:32.458 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:32.459 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:12:32.459 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:12:32.459 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:32.459 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:12:32.459 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:12:32.460 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:32.460 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:12:32.460 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:12:32.460 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:12:32.460 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:12:32.460 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:12:32.461 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:12:32.461 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:12:32.461 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:12:32.635 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:12:32.636 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:12:32.636 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:12:32.636 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:12:32.636 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:12:32.736 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:12:32.737 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:12:32.737 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:12:32.737 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:12:32.737 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:12:32.738 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:32.751 [Information] VocomService: PTT application is not running
2025-07-04 13:12:32.752 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:12:32.752 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:32.752 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:32.764 [Information] VocomService: PTT application is not running
2025-07-04 13:12:32.765 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:32.765 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:32.765 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:12:32.765 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:12:32.765 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:32.766 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:12:32.766 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:32.766 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:12:32.766 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:12:32.767 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:12:32.767 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:12:32.767 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:12:32.767 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:12:32.768 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:12:32.768 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:12:32.768 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:32.768 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:12:32.769 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:12:32.769 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:32.769 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:12:32.769 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:12:32.770 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:12:32.770 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:12:32.770 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-04 13:12:34.770 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-04 13:12:34.771 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:12:34.771 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:12:34.771 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:12:34.772 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:12:34.772 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:12:34.772 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:12:34.773 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:12:34.773 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:12:34.774 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:12:34.774 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:12:34.774 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:34.774 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:12:34.774 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:12:34.775 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:34.775 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:12:34.775 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:12:34.775 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:34.776 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:12:34.776 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:12:34.776 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:34.776 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:12:34.777 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:12:34.777 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:12:34.777 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:12:34.777 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:12:34.778 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:34.778 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:12:34.778 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:12:34.778 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:34.779 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:12:34.779 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:12:34.779 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:34.779 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:12:34.780 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:12:34.780 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:12:34.780 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:12:34.780 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:12:34.780 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:12:34.781 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:12:34.781 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:12:34.781 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:12:34.781 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:12:34.781 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:12:34.953 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:12:34.954 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:12:34.954 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:12:34.954 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:12:34.954 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:12:35.054 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:12:35.055 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:12:35.055 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:12:35.055 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:12:35.055 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:12:35.056 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:35.064 [Information] VocomService: PTT application is not running
2025-07-04 13:12:35.064 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:12:35.064 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:35.064 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:35.075 [Information] VocomService: PTT application is not running
2025-07-04 13:12:35.076 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:35.076 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:35.076 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:12:35.077 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:12:35.077 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:35.077 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:12:35.078 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:12:35.078 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:12:35.078 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:12:35.079 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:12:35.079 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:12:35.079 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:12:35.079 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:12:35.080 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:12:35.080 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:12:35.080 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:12:35.080 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:35.080 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:12:35.080 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:12:35.081 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:12:35.081 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:12:35.081 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:12:35.081 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:12:35.081 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:12:35.082 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:12:35.082 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:12:35.082 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-04 13:12:38.082 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-04 13:12:38.082 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-04 13:12:38.083 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-04 13:12:38.087 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-04 13:12:38.587 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-04 13:12:38.588 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-04 13:12:38.594 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-04 13:12:38.595 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-04 13:12:38.597 [Information] BackupService: Initializing backup service
2025-07-04 13:12:38.597 [Information] BackupService: Backup service initialized successfully
2025-07-04 13:12:38.598 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-04 13:12:38.598 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-04 13:12:38.599 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-04 13:12:38.619 [Information] BackupService: Compressing backup data
2025-07-04 13:12:38.625 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (447 bytes)
2025-07-04 13:12:38.626 [Information] BackupServiceFactory: Created template for category: Production
2025-07-04 13:12:38.626 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-04 13:12:38.626 [Information] BackupService: Compressing backup data
2025-07-04 13:12:38.627 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (447 bytes)
2025-07-04 13:12:38.627 [Information] BackupServiceFactory: Created template for category: Development
2025-07-04 13:12:38.628 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-04 13:12:38.628 [Information] BackupService: Compressing backup data
2025-07-04 13:12:38.629 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (445 bytes)
2025-07-04 13:12:38.629 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-04 13:12:38.629 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-04 13:12:38.630 [Information] BackupService: Compressing backup data
2025-07-04 13:12:38.631 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (448 bytes)
2025-07-04 13:12:38.631 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-04 13:12:38.631 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-04 13:12:38.632 [Information] BackupService: Compressing backup data
2025-07-04 13:12:38.633 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-04 13:12:38.633 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-04 13:12:38.633 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-04 13:12:38.634 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-04 13:12:38.634 [Information] BackupService: Compressing backup data
2025-07-04 13:12:38.635 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-04 13:12:38.635 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-04 13:12:38.635 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-04 13:12:38.637 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-04 13:12:38.639 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 13:12:38.641 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 13:12:38.673 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-04 13:12:38.674 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 13:12:38.675 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-04 13:12:38.675 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-04 13:12:38.676 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-04 13:12:38.676 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-04 13:12:38.677 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-04 13:12:38.679 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-04 13:12:38.679 [Information] App: Flash operation monitor service initialized successfully
2025-07-04 13:12:38.685 [Information] LicensingService: Initializing licensing service
2025-07-04 13:12:38.720 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-04 13:12:38.722 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-04 13:12:38.722 [Information] App: Licensing service initialized successfully
2025-07-04 13:12:38.722 [Information] App: License status: Trial
2025-07-04 13:12:38.723 [Information] App: Trial period: 30 days remaining
2025-07-04 13:12:38.723 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-04 13:12:38.867 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:12:38.868 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:12:38.868 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:12:38.868 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:12:38.869 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:12:38.869 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:12:38.869 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:12:38.869 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:12:38.870 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:12:38.870 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:12:38.870 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:12:38.878 [Information] VocomService: PTT application is not running
2025-07-04 13:12:38.879 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:12:38.930 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-04 13:12:39.433 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-04 13:12:39.483 [Information] BackupService: Initializing backup service
2025-07-04 13:12:39.483 [Information] BackupService: Backup service initialized successfully
2025-07-04 13:12:39.533 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 13:12:39.534 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 13:12:39.535 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-04 13:12:39.535 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 13:12:39.586 [Information] BackupService: Getting predefined backup categories
2025-07-04 13:12:39.637 [Information] MainViewModel: Services initialized successfully
2025-07-04 13:12:39.640 [Information] MainViewModel: Scanning for Vocom devices
2025-07-04 13:12:39.641 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:12:39.641 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:12:39.641 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:12:39.641 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:12:39.795 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:12:39.795 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:12:39.795 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:12:39.796 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:12:39.796 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:12:39.928 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:12:39.928 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:12:39.928 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:12:39.929 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-04 13:13:25.369 [Information] MainViewModel: Scanning for Vocom devices
2025-07-04 13:13:25.372 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:13:25.372 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:13:25.372 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:13:25.373 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:13:25.570 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:13:25.570 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:13:25.571 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:13:25.571 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:13:25.571 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:13:25.679 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:13:25.680 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:13:25.680 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:13:25.680 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-04 13:13:31.307 [Information] MainViewModel: Connecting to Vocom device 
2025-07-04 13:13:31.307 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:13:31.307 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:13:31.319 [Information] VocomService: PTT application is not running
2025-07-04 13:13:31.319 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:13:31.319 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:13:31.319 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:13:31.329 [Information] VocomService: PTT application is not running
2025-07-04 13:13:31.329 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:13:31.329 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:13:31.329 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:13:31.330 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:13:31.330 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:13:31.330 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:13:31.330 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:13:31.330 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:13:31.331 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:13:31.331 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:13:31.331 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:13:31.331 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:13:31.332 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:13:31.332 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:13:31.332 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:13:31.332 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:13:31.333 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 
2025-07-04 13:13:31.333 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:13:31.333 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:13:31.334 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:13:31.334 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:13:31.334 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:13:31.334 [Error] MainViewModel: Failed to connect to Vocom device 
