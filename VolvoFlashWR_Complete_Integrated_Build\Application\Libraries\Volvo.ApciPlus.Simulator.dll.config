﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="userSettings" type="System.Configuration.UserSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="Volvo.ApciPlus.Simulator.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" allowExeDefinition="MachineToLocalUser" requirePermission="false" />
    </sectionGroup>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System">
      <section name="DevExpress.LookAndFeel.Design.AppSettings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <applicationSettings>
    <DevExpress.LookAndFeel.Design.AppSettings>
      <setting name="DefaultAppSkin" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultPalette" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CompactUI" serializeAs="String">
        <value></value>
      </setting>
      <setting name="TouchScaleFactor" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DirectX" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterUserSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="RegisterBonusSkins" serializeAs="String">
        <value></value>
      </setting>
      <setting name="FontBehavior" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DefaultAppFont" serializeAs="String">
        <value></value>
      </setting>
      <setting name="DPIAwarenessMode" serializeAs="String">
        <value></value>
      </setting>
      <setting name="CustomPaletteCollection" serializeAs="Xml">
        <value />
      </setting>
    </DevExpress.LookAndFeel.Design.AppSettings>
  </applicationSettings>
  <startup></startup>
  <userSettings>
    <Volvo.ApciPlus.Simulator.Properties.Settings>
      <setting name="RecentAdapter" serializeAs="String">
        <value />
      </setting>
      <setting name="RecentProtocol" serializeAs="String">
        <value />
      </setting>
      <setting name="isOffline" serializeAs="String">
        <value>False</value>
      </setting>
      <setting name="RecentAdaptername" serializeAs="String">
        <value />
      </setting>
    </Volvo.ApciPlus.Simulator.Properties.Settings>
  </userSettings>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Telerik.WinControls.UI" publicKeyToken="5bb2a467cbec794e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2022.3.913.40" newVersion="2022.3.913.40" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Telerik.WinControls" publicKeyToken="5bb2a467cbec794e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2022.3.913.40" newVersion="2022.3.913.40" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="NHibernate" publicKeyToken="aa95f207798dfdb4" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.0.4000" newVersion="4.0.0.4000" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Data.SqlServerCe" publicKeyToken="89845dcd8080cc91" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.1.0" newVersion="3.5.1.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Data" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.EnterpriseLibrary.Common" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Practices.ServiceLocation" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-1.3.0.0" newVersion="1.3.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="ArmDot.Client" publicKeyToken="8df7f03897c0e059" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2024.7.0.0" newVersion="2024.7.0.0" />
      </dependentAssembly>
     
      <dependentAssembly>
        <assemblyIdentity name="Telerik.WinControls.GridView" publicKeyToken="5bb2a467cbec794e" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2022.3.913.40" newVersion="2022.3.913.40" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="ICSharpCode.SharpZipLib" publicKeyToken="1b03e6acf1164f73" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-0.85.4.369" newVersion="0.85.4.369" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="log4net" publicKeyToken="669e0ddf0bb1aa2a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="SharpCompress" publicKeyToken="afb0a02973931d96" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.2.0" newVersion="4.0.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="SQLitePCLRaw.core" publicKeyToken="1488e028ca7ab535" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.4.976" newVersion="2.0.4.976" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="SQLitePCLRaw.batteries_v2" publicKeyToken="8226ea5df37bcae9" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-2.0.4.976" newVersion="2.0.4.976" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
</configuration>