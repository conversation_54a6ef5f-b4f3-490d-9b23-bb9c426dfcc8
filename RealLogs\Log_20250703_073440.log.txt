Log started at 7/3/2025 7:34:40 AM
2025-07-03 07:34:40.189 [Information] LoggingService: Logging service initialized
2025-07-03 07:34:40.200 [Information] App: Starting integrated application initialization
2025-07-03 07:34:40.202 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-03 07:34:40.205 [Information] IntegratedStartupService: Setting up application environment
2025-07-03 07:34:40.206 [Information] IntegratedStartupService: Application environment setup completed
2025-07-03 07:34:40.209 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-03 07:34:40.211 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-03 07:34:40.213 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-03 07:34:40.363 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-03 07:34:40.365 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-03 07:34:40.366 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-03 07:34:40.366 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-03 07:34:40.380 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-03 07:34:40.392 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-03 07:34:40.394 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-03 07:34:40.394 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-03 07:34:40.402 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-03 07:34:40.412 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:34:40.412 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:34:40.413 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:34:40.413 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:34:40.413 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:34:40.414 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:34:40.416 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-03 07:34:40.416 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-03 07:34:40.419 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-03 07:34:40.420 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-03 07:34:40.422 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-03 07:34:40.424 [Information] LibraryExtractor: Starting library extraction process
2025-07-03 07:34:40.426 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-03 07:34:40.427 [Information] LibraryExtractor: Copying system libraries
2025-07-03 07:34:40.431 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-03 07:34:40.436 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-03 07:34:51.817 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-03 07:35:09.942 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-03 07:35:33.953 [Information] LibraryExtractor: Verifying library extraction
2025-07-03 07:35:33.954 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-03 07:35:33.954 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-03 07:35:33.954 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-03 07:35:33.955 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-03 07:35:33.955 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-03 07:35:33.957 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-03 07:35:33.959 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-03 07:35:33.959 [Information] DependencyManager: Initializing dependency manager
2025-07-03 07:35:33.960 [Information] DependencyManager: Setting up library search paths
2025-07-03 07:35:33.961 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:35:33.961 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-03 07:35:33.961 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-03 07:35:33.962 [Information] DependencyManager: Updated PATH environment variable
2025-07-03 07:35:33.963 [Information] DependencyManager: Verifying required directories
2025-07-03 07:35:33.963 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:35:33.963 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-03 07:35:33.963 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-03 07:35:33.964 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-03 07:35:33.964 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-03 07:35:33.967 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-03 07:35:33.967 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-03 07:35:33.968 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-03 07:35:33.969 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-03 07:35:33.969 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-03 07:35:33.970 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:35:33.970 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:35:33.970 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:35:33.971 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-03 07:35:33.975 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-03 07:35:34.034 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-03 07:35:34.077 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-03 07:35:34.277 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-03 07:35:34.351 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-03 07:35:34.398 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-03 07:35:34.398 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-03 07:35:34.399 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-03 07:35:34.399 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-03 07:35:34.400 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-03 07:35:34.400 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-03 07:35:34.400 [Information] DependencyManager: Setting up environment variables
2025-07-03 07:35:34.401 [Information] DependencyManager: Environment variables configured
2025-07-03 07:35:34.402 [Information] DependencyManager: Verifying library loading status
2025-07-03 07:35:34.507 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-03 07:35:34.508 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-03 07:35:34.508 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-03 07:35:34.510 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-03 07:35:34.512 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-03 07:35:34.514 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-03 07:35:34.515 [Information] IntegratedStartupService: Verifying system readiness
2025-07-03 07:35:34.515 [Information] IntegratedStartupService: System readiness verification passed
2025-07-03 07:35:34.516 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-03 07:35:34.517 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-03 07:35:34.517 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-03 07:35:34.517 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:35:34.517 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-03 07:35:34.518 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-03 07:35:34.518 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-03 07:35:34.518 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-03 07:35:34.518 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:35:34.519 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-03 07:35:34.519 [Information] App: Integrated startup completed successfully
2025-07-03 07:35:34.520 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-03 07:35:34.533 [Information] App: Initializing application services
2025-07-03 07:35:34.534 [Information] AppConfigurationService: Initializing configuration service
2025-07-03 07:35:34.535 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-03 07:35:34.564 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-03 07:35:34.565 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-03 07:35:34.565 [Information] App: Configuration service initialized successfully
2025-07-03 07:35:34.566 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-03 07:35:34.567 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-03 07:35:34.570 [Information] App: Environment variable exists: True, not 'false': False
2025-07-03 07:35:34.570 [Information] App: Final useDummyImplementations value: False
2025-07-03 07:35:34.570 [Information] App: Updating config to NOT use dummy implementations
2025-07-03 07:35:34.580 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-03 07:35:34.581 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-03 07:35:34.581 [Information] App: usePatchedImplementation flag is: True
2025-07-03 07:35:34.581 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-03 07:35:34.581 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-03 07:35:34.582 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-03 07:35:34.582 [Information] App: verboseLogging flag is: True
2025-07-03 07:35:34.583 [Information] App: Verifying real hardware requirements...
2025-07-03 07:35:34.583 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-03 07:35:34.584 [Information] App: ✓ Found critical library: apci.dll
2025-07-03 07:35:34.584 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-03 07:35:34.584 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-03 07:35:34.584 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:35:34.585 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-03 07:35:34.585 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-03 07:35:34.585 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-03 07:35:34.597 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-03 07:35:34.597 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-03 07:35:34.598 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-03 07:35:34.599 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-03 07:35:34.599 [Information] PatchedVocomServiceFactory: Assembly location: 
2025-07-03 07:35:34.612 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-03 07:35:34.613 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-03 07:35:34.613 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-03 07:35:34.613 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-03 07:35:34.614 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-03 07:35:34.644 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-03 07:35:34.646 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-03 07:35:34.646 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-03 07:35:34.646 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-03 07:35:34.647 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-03 07:35:34.647 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-03 07:35:34.648 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-03 07:35:34.650 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-03 07:35:34.651 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-03 07:35:34.653 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 07:35:34.654 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-03 07:35:34.660 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll
2025-07-03 07:35:34.662 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:35:34.663 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apcidb.dll
2025-07-03 07:35:34.664 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-03 07:35:34.667 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-03 07:35:34.671 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-03 07:35:34.672 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-03 07:35:34.674 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-03 07:35:34.675 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-03 07:35:34.676 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusTea2Data.dll
2025-07-03 07:35:34.677 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-03 07:35:34.679 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-07-03 07:35:34.680 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-03 07:35:34.681 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-03 07:35:34.681 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-03 07:35:34.682 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Core.dll
2025-07-03 07:35:34.683 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-03 07:35:34.684 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Logging.dll
2025-07-03 07:35:34.685 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-03 07:35:34.686 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.dll
2025-07-03 07:35:34.687 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-03 07:35:34.688 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-07-03 07:35:34.690 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-03 07:35:34.691 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Baf.Utility.dll
2025-07-03 07:35:34.692 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-03 07:35:34.693 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-03 07:35:34.694 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-03 07:35:34.695 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.ServiceContract.dll
2025-07-03 07:35:34.696 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-03 07:35:34.697 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.Utility.dll
2025-07-03 07:35:34.698 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-03 07:35:34.699 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll.config
2025-07-03 07:35:34.700 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-03 07:35:34.701 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll.config
2025-07-03 07:35:34.704 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-03 07:35:34.708 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.dll
2025-07-03 07:35:34.709 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-03 07:35:34.710 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.Caches.SysCache2.dll
2025-07-03 07:35:34.712 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-03 07:35:34.712 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Iesi.Collections.dll
2025-07-03 07:35:34.714 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-03 07:35:34.715 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Ionic.Zip.Reduced.dll
2025-07-03 07:35:34.716 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-03 07:35:34.717 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-03 07:35:34.719 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\DotNetZip.dll
2025-07-03 07:35:34.720 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-03 07:35:34.721 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\ICSharpCode.SharpZipLib.dll
2025-07-03 07:35:34.722 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-03 07:35:34.723 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.CommonDomain.Model.dll
2025-07-03 07:35:34.724 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-03 07:35:34.725 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.Contracts.Common.dll
2025-07-03 07:35:34.727 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-03 07:35:34.728 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.UtilityComponent.dll
2025-07-03 07:35:34.729 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-03 07:35:34.730 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\log4net.dll
2025-07-03 07:35:34.731 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-03 07:35:34.732 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-03 07:35:34.733 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\AutoMapper.dll
2025-07-03 07:35:34.734 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 07:35:34.736 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-07-03 07:35:34.738 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.AppContext.dll
2025-07-03 07:35:34.739 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-07-03 07:35:34.739 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Buffers.dll
2025-07-03 07:35:34.741 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-03 07:35:34.742 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Concurrent.dll
2025-07-03 07:35:34.743 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.dll
2025-07-03 07:35:34.744 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.dll
2025-07-03 07:35:34.744 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-03 07:35:34.745 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.NonGeneric.dll
2025-07-03 07:35:34.746 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-03 07:35:34.747 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Specialized.dll
2025-07-03 07:35:34.747 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-03 07:35:34.748 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.dll
2025-07-03 07:35:34.749 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-03 07:35:34.749 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.EventBasedAsync.dll
2025-07-03 07:35:34.750 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-03 07:35:34.752 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.Primitives.dll
2025-07-03 07:35:34.753 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-03 07:35:34.754 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.TypeConverter.dll
2025-07-03 07:35:34.755 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Console.dll
2025-07-03 07:35:34.755 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Console.dll
2025-07-03 07:35:34.756 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-03 07:35:34.757 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.Common.dll
2025-07-03 07:35:34.758 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-03 07:35:34.760 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SQLite.dll
2025-07-03 07:35:34.760 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-03 07:35:34.761 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SqlServerCe.dll
2025-07-03 07:35:34.762 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-03 07:35:34.763 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Contracts.dll
2025-07-03 07:35:34.764 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-03 07:35:34.764 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Debug.dll
2025-07-03 07:35:34.765 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-03 07:35:34.766 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.FileVersionInfo.dll
2025-07-03 07:35:34.768 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-03 07:35:34.769 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Process.dll
2025-07-03 07:35:34.770 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-03 07:35:34.771 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.StackTrace.dll
2025-07-03 07:35:34.772 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-03 07:35:34.773 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-07-03 07:35:34.774 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-03 07:35:34.775 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tools.dll
2025-07-03 07:35:34.776 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-03 07:35:34.777 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TraceSource.dll
2025-07-03 07:35:34.777 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-03 07:35:34.778 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tracing.dll
2025-07-03 07:35:34.779 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-03 07:35:34.780 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Drawing.Primitives.dll
2025-07-03 07:35:34.781 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-03 07:35:34.782 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Dynamic.Runtime.dll
2025-07-03 07:35:34.783 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-03 07:35:34.784 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Calendars.dll
2025-07-03 07:35:34.784 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-07-03 07:35:34.785 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.dll
2025-07-03 07:35:34.786 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-03 07:35:34.787 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Extensions.dll
2025-07-03 07:35:34.788 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-03 07:35:34.788 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.dll
2025-07-03 07:35:34.789 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-03 07:35:34.790 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.ZipFile.dll
2025-07-03 07:35:34.791 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.dll
2025-07-03 07:35:34.791 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.dll
2025-07-03 07:35:34.792 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-03 07:35:34.793 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.dll
2025-07-03 07:35:34.794 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-03 07:35:34.795 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.DriveInfo.dll
2025-07-03 07:35:34.797 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-03 07:35:34.798 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Primitives.dll
2025-07-03 07:35:34.799 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-03 07:35:34.800 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Watcher.dll
2025-07-03 07:35:34.801 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-03 07:35:34.802 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.IsolatedStorage.dll
2025-07-03 07:35:34.802 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-03 07:35:34.803 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.MemoryMappedFiles.dll
2025-07-03 07:35:34.804 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-03 07:35:34.805 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Pipes.dll
2025-07-03 07:35:34.805 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-03 07:35:34.806 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.UnmanagedMemoryStream.dll
2025-07-03 07:35:34.807 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.dll
2025-07-03 07:35:34.808 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.dll
2025-07-03 07:35:34.808 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-03 07:35:34.809 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Expressions.dll
2025-07-03 07:35:34.810 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-03 07:35:34.811 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Parallel.dll
2025-07-03 07:35:34.812 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-03 07:35:34.813 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Queryable.dll
2025-07-03 07:35:34.814 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Memory.dll
2025-07-03 07:35:34.815 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Memory.dll
2025-07-03 07:35:34.816 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-03 07:35:34.817 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Http.dll
2025-07-03 07:35:34.818 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-03 07:35:34.819 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NameResolution.dll
2025-07-03 07:35:34.820 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-03 07:35:34.820 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NetworkInformation.dll
2025-07-03 07:35:34.821 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-03 07:35:34.822 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Ping.dll
2025-07-03 07:35:34.823 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-03 07:35:34.823 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Primitives.dll
2025-07-03 07:35:34.825 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-03 07:35:34.827 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Requests.dll
2025-07-03 07:35:34.827 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-03 07:35:34.828 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Security.dll
2025-07-03 07:35:34.829 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-03 07:35:34.830 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Sockets.dll
2025-07-03 07:35:34.831 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-03 07:35:34.832 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebHeaderCollection.dll
2025-07-03 07:35:34.833 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-03 07:35:34.834 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.Client.dll
2025-07-03 07:35:34.835 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-03 07:35:34.836 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.dll
2025-07-03 07:35:34.837 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-03 07:35:34.838 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Numerics.Vectors.dll
2025-07-03 07:35:34.838 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-03 07:35:34.839 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ObjectModel.dll
2025-07-03 07:35:34.839 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-07-03 07:35:34.840 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.dll
2025-07-03 07:35:34.841 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-03 07:35:34.842 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Extensions.dll
2025-07-03 07:35:34.844 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-03 07:35:34.846 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Primitives.dll
2025-07-03 07:35:34.847 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-03 07:35:34.848 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Reader.dll
2025-07-03 07:35:34.848 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-03 07:35:34.849 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.ResourceManager.dll
2025-07-03 07:35:34.850 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-03 07:35:34.851 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Writer.dll
2025-07-03 07:35:34.851 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-03 07:35:34.852 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-07-03 07:35:34.853 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-03 07:35:34.854 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.VisualC.dll
2025-07-03 07:35:34.854 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-07-03 07:35:34.855 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.dll
2025-07-03 07:35:34.856 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-03 07:35:34.856 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Extensions.dll
2025-07-03 07:35:34.857 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-03 07:35:34.858 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Handles.dll
2025-07-03 07:35:34.860 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-03 07:35:34.862 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.dll
2025-07-03 07:35:34.864 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-03 07:35:34.866 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-03 07:35:34.867 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-03 07:35:34.868 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Numerics.dll
2025-07-03 07:35:34.868 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-03 07:35:34.869 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Formatters.dll
2025-07-03 07:35:34.870 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-03 07:35:34.870 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Json.dll
2025-07-03 07:35:34.871 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-03 07:35:34.872 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Primitives.dll
2025-07-03 07:35:34.872 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-03 07:35:34.873 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Xml.dll
2025-07-03 07:35:34.874 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-03 07:35:34.875 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Claims.dll
2025-07-03 07:35:34.876 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-03 07:35:34.876 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Algorithms.dll
2025-07-03 07:35:34.877 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-03 07:35:34.878 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Csp.dll
2025-07-03 07:35:34.878 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-03 07:35:34.879 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Encoding.dll
2025-07-03 07:35:34.880 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-03 07:35:34.881 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Primitives.dll
2025-07-03 07:35:34.881 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-03 07:35:34.882 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.X509Certificates.dll
2025-07-03 07:35:34.883 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-03 07:35:34.885 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Principal.dll
2025-07-03 07:35:34.886 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-03 07:35:34.887 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.SecureString.dll
2025-07-03 07:35:34.888 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-03 07:35:34.889 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.dll
2025-07-03 07:35:34.890 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-03 07:35:34.891 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.Extensions.dll
2025-07-03 07:35:34.892 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-03 07:35:34.893 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.RegularExpressions.dll
2025-07-03 07:35:34.894 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.dll
2025-07-03 07:35:34.895 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.dll
2025-07-03 07:35:34.896 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-03 07:35:34.897 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Overlapped.dll
2025-07-03 07:35:34.898 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-03 07:35:34.899 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.dll
2025-07-03 07:35:34.900 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-03 07:35:34.901 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Extensions.dll
2025-07-03 07:35:34.902 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-03 07:35:34.903 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Parallel.dll
2025-07-03 07:35:34.904 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-03 07:35:34.905 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Thread.dll
2025-07-03 07:35:34.906 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-03 07:35:34.907 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.ThreadPool.dll
2025-07-03 07:35:34.908 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-03 07:35:34.910 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Timer.dll
2025-07-03 07:35:34.910 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-03 07:35:34.911 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ValueTuple.dll
2025-07-03 07:35:34.912 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-03 07:35:34.912 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.ReaderWriter.dll
2025-07-03 07:35:34.913 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-03 07:35:34.914 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XDocument.dll
2025-07-03 07:35:34.915 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-03 07:35:34.916 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlDocument.dll
2025-07-03 07:35:34.917 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-03 07:35:34.918 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlSerializer.dll
2025-07-03 07:35:34.919 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-03 07:35:34.920 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.dll
2025-07-03 07:35:34.921 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-03 07:35:34.922 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.XDocument.dll
2025-07-03 07:35:34.922 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-07-03 07:35:34.923 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\SystemInterface.dll
2025-07-03 07:35:34.924 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-03 07:35:34.924 [Information] PhoenixVocomAdapter: Loading APCI library dynamically
2025-07-03 07:35:34.988 [Error] PhoenixVocomAdapter: Failed to load APCI library. Error code: 193
2025-07-03 07:35:34.988 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-03 07:35:34.989 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-03 07:35:34.989 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-03 07:35:34.990 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-03 07:35:34.990 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-03 07:35:34.991 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-03 07:35:34.992 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-03 07:35:34.992 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:35:34.992 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-03 07:35:34.993 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-03 07:35:34.993 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-03 07:35:34.994 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-03 07:35:34.994 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-03 07:35:34.995 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-03 07:35:34.995 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-03 07:35:34.995 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-03 07:35:34.995 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-03 07:35:34.996 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-03 07:35:34.996 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-07-03 07:35:34.997 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-07-03 07:35:34.997 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-03 07:35:34.997 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-03 07:35:34.998 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-03 07:35:34.998 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:35:34.998 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-03 07:35:34.999 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-03 07:35:34.999 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-03 07:35:35.000 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-03 07:35:35.001 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-03 07:35:35.002 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-03 07:35:35.004 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-03 07:35:35.005 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-03 07:35:35.005 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:35:35.005 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:35:35.006 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-03 07:35:35.007 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:35.008 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:35.008 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-03 07:35:35.062 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:35.063 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:35.063 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-03 07:35:35.336 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:35.336 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-03 07:35:35.423 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:35.423 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-03 07:35:35.510 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:35.511 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:35.511 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-03 07:35:35.595 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:35.682 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:35.682 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-03 07:35:35.769 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:35:35.907 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:35:36.020 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:36.117 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:36.118 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-03 07:35:36.118 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:35:36.333 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:36.333 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-03 07:35:36.414 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:36.504 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:36.504 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-03 07:35:36.622 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:35:36.734 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:35:36.734 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-03 07:35:36.882 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:35:36.889 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:35:36.945 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:35:36.947 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-03 07:35:36.947 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:35:36.947 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-03 07:35:36.948 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-03 07:35:36.948 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-03 07:35:36.949 [Information] VocomDriver: Initializing Vocom driver
2025-07-03 07:35:36.950 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-03 07:35:36.952 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-03 07:35:36.952 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:35:36.953 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:35:36.953 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:35:36.953 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-03 07:35:36.955 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-03 07:35:36.956 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-03 07:35:36.956 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-03 07:35:36.957 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-03 07:35:36.957 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-03 07:35:36.958 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:35:36.959 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-03 07:35:36.960 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-03 07:35:36.962 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-03 07:35:36.962 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-03 07:35:36.962 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-03 07:35:36.963 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-03 07:35:36.964 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-03 07:35:36.964 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 07:35:36.964 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-03 07:35:36.964 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-03 07:35:36.964 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-03 07:35:36.965 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-03 07:35:36.965 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 07:35:36.965 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 07:35:36.965 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 07:35:36.965 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-03 07:35:36.966 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 07:35:36.966 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 07:35:36.966 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-03 07:35:36.966 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-03 07:35:36.966 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-03 07:35:36.967 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-03 07:35:36.967 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-03 07:35:36.967 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-03 07:35:36.967 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-03 07:35:36.967 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-03 07:35:36.968 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-03 07:35:36.968 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-03 07:35:36.968 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-03 07:35:36.968 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-03 07:35:36.968 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 07:35:36.968 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 07:35:36.969 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-03 07:35:36.969 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-03 07:35:36.969 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 07:35:36.969 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 07:35:36.969 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 07:35:36.969 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-03 07:35:36.970 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-03 07:35:36.971 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-03 07:35:36.971 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-03 07:35:36.971 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-03 07:35:36.971 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-03 07:35:36.972 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-03 07:35:36.972 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-03 07:35:36.973 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-03 07:35:36.973 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-03 07:35:36.973 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-03 07:35:36.973 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-03 07:35:36.973 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-03 07:35:36.974 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-03 07:35:36.976 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-03 07:35:36.977 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-03 07:35:36.978 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-03 07:35:36.979 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-03 07:35:37.017 [Information] WiFiCommunicationService: WiFi is available
2025-07-03 07:35:37.017 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-03 07:35:37.018 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-03 07:35:37.019 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-03 07:35:37.020 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-03 07:35:37.021 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-03 07:35:37.022 [Information] VocomService: Initializing Vocom service
2025-07-03 07:35:37.023 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:37.037 [Information] VocomService: PTT application is not running
2025-07-03 07:35:37.040 [Information] VocomService: Vocom service initialized successfully
2025-07-03 07:35:37.040 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-03 07:35:37.040 [Information] App: Initializing Vocom service
2025-07-03 07:35:37.040 [Information] VocomService: Initializing Vocom service
2025-07-03 07:35:37.041 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:37.054 [Information] VocomService: PTT application is not running
2025-07-03 07:35:37.054 [Information] VocomService: Vocom service initialized successfully
2025-07-03 07:35:37.056 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:35:37.056 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:35:37.057 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:35:37.060 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:35:37.063 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:35:37.339 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:35:37.342 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:35:37.343 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:35:37.345 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:35:37.345 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:35:37.345 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:35:37.797 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:35:37.797 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:35:37.800 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:35:37.801 [Information] App: Found 4 Vocom devices, attempting to connect to the first one
2025-07-03 07:35:37.803 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:35:37.803 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:37.813 [Information] VocomService: PTT application is not running
2025-07-03 07:35:37.815 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:35:37.815 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:35:37.816 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:37.825 [Information] VocomService: PTT application is not running
2025-07-03 07:35:37.825 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:35:37.846 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:35:37.847 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:35:37.848 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:35:37.849 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:35:37.850 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:35:37.850 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:35:37.850 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:37.851 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:37.851 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-03 07:35:37.852 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-03 07:35:37.854 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:37.855 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-03 07:35:37.856 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 07:35:37.858 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 07:35:37.858 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 07:35:37.860 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 07:35:37.861 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 07:35:37.864 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 07:35:37.866 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 07:35:37.868 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 07:35:37.873 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:35:37.874 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:35:37.875 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:35:37.877 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:35:37.877 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:35:37.877 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:35:37.878 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:35:37.879 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:35:37.879 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:35:37.880 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:35:37.881 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:35:37.881 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:35:37.882 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:35:37.882 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:35:37.883 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:35:37.883 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 07:35:37.885 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 07:35:37.886 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:37.886 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 07:35:37.887 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 07:35:37.887 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:37.887 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 07:35:37.888 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 07:35:37.888 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:37.888 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 07:35:37.888 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 07:35:37.889 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:37.889 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 07:35:37.889 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-03 07:35:37.889 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-03 07:35:37.891 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-03 07:35:37.891 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:35:37.891 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:35:37.892 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:35:37.892 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:35:37.892 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:35:38.077 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:35:38.078 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:35:38.078 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:35:38.078 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:35:38.079 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:35:38.079 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:35:38.079 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:35:38.079 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:35:38.080 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:35:38.081 [Information] VocomService: Attempting to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:38.081 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:35:38.081 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:38.095 [Information] VocomService: PTT application is not running
2025-07-03 07:35:38.096 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:35:38.096 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:35:38.096 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:38.109 [Information] VocomService: PTT application is not running
2025-07-03 07:35:38.109 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:35:38.110 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:35:38.110 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:35:38.110 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:35:38.110 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:35:38.111 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:35:38.111 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:35:38.111 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:38.112 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:38.112 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:38.112 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:38.113 [Warning] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:38.113 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-DRIVER via USB
2025-07-03 07:35:38.113 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-03 07:35:38.113 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:38.129 [Information] VocomService: PTT application is not running
2025-07-03 07:35:38.129 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-03 07:35:38.129 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-03 07:35:38.130 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:38.141 [Information] VocomService: PTT application is not running
2025-07-03 07:35:38.142 [Information] VocomService: Using USB communication service to connect to WUDFPuma Driver
2025-07-03 07:35:38.142 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:35:38.143 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-03 07:35:38.143 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:35:38.143 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:35:38.143 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:35:38.144 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:35:38.144 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:35:38.144 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:35:38.144 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:35:38.145 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:35:38.145 [Warning] VocomService: Failed to connect to alternative Vocom device 88890300-DRIVER via USB
2025-07-03 07:35:38.145 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:38.145 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:38.146 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:38.159 [Information] VocomService: PTT application is not running
2025-07-03 07:35:38.161 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:38.162 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-03 07:35:38.965 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:38.966 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:38.966 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-03 07:35:38.967 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:38.967 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 07:35:38.968 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-03 07:35:38.970 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-03 07:35:38.971 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-03 07:35:38.973 [Information] BackupService: Initializing backup service
2025-07-03 07:35:38.973 [Information] BackupService: Backup service initialized successfully
2025-07-03 07:35:38.973 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-03 07:35:38.974 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-03 07:35:38.975 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-03 07:35:38.997 [Information] BackupService: Compressing backup data
2025-07-03 07:35:39.002 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-03 07:35:39.003 [Information] BackupServiceFactory: Created template for category: Production
2025-07-03 07:35:39.003 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-03 07:35:39.003 [Information] BackupService: Compressing backup data
2025-07-03 07:35:39.004 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-03 07:35:39.005 [Information] BackupServiceFactory: Created template for category: Development
2025-07-03 07:35:39.005 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-03 07:35:39.005 [Information] BackupService: Compressing backup data
2025-07-03 07:35:39.006 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-03 07:35:39.006 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-03 07:35:39.006 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-03 07:35:39.006 [Information] BackupService: Compressing backup data
2025-07-03 07:35:39.007 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-03 07:35:39.007 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-03 07:35:39.007 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-03 07:35:39.008 [Information] BackupService: Compressing backup data
2025-07-03 07:35:39.009 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-07-03 07:35:39.009 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-03 07:35:39.010 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-03 07:35:39.010 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-03 07:35:39.010 [Information] BackupService: Compressing backup data
2025-07-03 07:35:39.011 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (509 bytes)
2025-07-03 07:35:39.012 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-03 07:35:39.012 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-03 07:35:39.013 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-03 07:35:39.015 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 07:35:39.016 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 07:35:39.062 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-03 07:35:39.063 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 07:35:39.063 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-03 07:35:39.064 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-03 07:35:39.064 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-03 07:35:39.065 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-03 07:35:39.065 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-03 07:35:39.068 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-03 07:35:39.068 [Information] App: Flash operation monitor service initialized successfully
2025-07-03 07:35:39.077 [Information] LicensingService: Initializing licensing service
2025-07-03 07:35:39.124 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-03 07:35:39.127 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-03 07:35:39.127 [Information] App: Licensing service initialized successfully
2025-07-03 07:35:39.127 [Information] App: License status: Trial
2025-07-03 07:35:39.127 [Information] App: Trial period: 30 days remaining
2025-07-03 07:35:39.128 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-03 07:35:39.300 [Information] VocomService: Initializing Vocom service
2025-07-03 07:35:39.301 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:39.309 [Information] VocomService: PTT application is not running
2025-07-03 07:35:39.310 [Information] VocomService: Vocom service initialized successfully
2025-07-03 07:35:39.361 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 07:35:39.361 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 07:35:39.361 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 07:35:39.361 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 07:35:39.361 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 07:35:39.362 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 07:35:39.363 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 07:35:39.363 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 07:35:39.364 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:35:39.364 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:35:39.374 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 07:35:39.375 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-03 07:35:39.375 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-03 07:35:39.376 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-03 07:35:39.376 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-03 07:35:39.376 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-03 07:35:39.377 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-03 07:35:39.377 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-03 07:35:39.377 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-03 07:35:39.378 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-03 07:35:39.379 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-03 07:35:39.379 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-03 07:35:39.379 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-03 07:35:39.380 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-03 07:35:39.380 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-03 07:35:39.380 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-03 07:35:39.380 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-03 07:35:39.383 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-03 07:35:39.390 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-03 07:35:39.391 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-03 07:35:39.394 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-03 07:35:39.395 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.402 [Information] CANRegisterAccess: Read value 0x50 from register 0x0141 (simulated)
2025-07-03 07:35:39.407 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.413 [Information] CANRegisterAccess: Read value 0x0A from register 0x0141 (simulated)
2025-07-03 07:35:39.419 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.425 [Information] CANRegisterAccess: Read value 0xEE from register 0x0141 (simulated)
2025-07-03 07:35:39.430 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.436 [Information] CANRegisterAccess: Read value 0x18 from register 0x0141 (simulated)
2025-07-03 07:35:39.443 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.449 [Information] CANRegisterAccess: Read value 0x7E from register 0x0141 (simulated)
2025-07-03 07:35:39.455 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.461 [Information] CANRegisterAccess: Read value 0x5D from register 0x0141 (simulated)
2025-07-03 07:35:39.462 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-03 07:35:39.463 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-03 07:35:39.463 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-03 07:35:39.469 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-03 07:35:39.470 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-03 07:35:39.476 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-03 07:35:39.477 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-03 07:35:39.477 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-03 07:35:39.483 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-03 07:35:39.484 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-03 07:35:39.484 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-03 07:35:39.490 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-03 07:35:39.491 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-03 07:35:39.496 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-03 07:35:39.497 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-03 07:35:39.503 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-03 07:35:39.503 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-03 07:35:39.509 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-03 07:35:39.510 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-03 07:35:39.515 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-03 07:35:39.515 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-03 07:35:39.521 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-03 07:35:39.521 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-03 07:35:39.527 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-03 07:35:39.527 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-03 07:35:39.533 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-03 07:35:39.533 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-03 07:35:39.538 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-03 07:35:39.538 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-03 07:35:39.544 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-03 07:35:39.544 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-03 07:35:39.550 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-03 07:35:39.550 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-03 07:35:39.556 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-03 07:35:39.557 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-03 07:35:39.563 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-03 07:35:39.563 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-03 07:35:39.570 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-03 07:35:39.570 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-03 07:35:39.576 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-03 07:35:39.577 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-03 07:35:39.582 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-03 07:35:39.583 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-03 07:35:39.588 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-03 07:35:39.589 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-03 07:35:39.589 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-03 07:35:39.595 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-03 07:35:39.596 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-03 07:35:39.596 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-03 07:35:39.596 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.602 [Information] CANRegisterAccess: Read value 0xA7 from register 0x0141 (simulated)
2025-07-03 07:35:39.609 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.615 [Information] CANRegisterAccess: Read value 0x2F from register 0x0141 (simulated)
2025-07-03 07:35:39.621 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:35:39.627 [Information] CANRegisterAccess: Read value 0x90 from register 0x0141 (simulated)
2025-07-03 07:35:39.628 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-03 07:35:39.629 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-03 07:35:39.629 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-03 07:35:39.629 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 07:35:39.636 [Information] CANRegisterAccess: Read value 0x4D from register 0x0140 (simulated)
2025-07-03 07:35:39.641 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 07:35:39.647 [Information] CANRegisterAccess: Read value 0xCD from register 0x0140 (simulated)
2025-07-03 07:35:39.653 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 07:35:39.659 [Information] CANRegisterAccess: Read value 0xF7 from register 0x0140 (simulated)
2025-07-03 07:35:39.660 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-03 07:35:39.660 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 07:35:39.661 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:35:39.661 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:35:39.671 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-03 07:35:39.672 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-03 07:35:39.673 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-03 07:35:39.678 [Information] VocomService: Sending data and waiting for response
2025-07-03 07:35:39.679 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-03 07:35:39.731 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-03 07:35:39.732 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-03 07:35:39.732 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-03 07:35:39.733 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:35:39.733 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:35:39.744 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 07:35:39.745 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-03 07:35:39.745 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-03 07:35:39.756 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-03 07:35:39.767 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-03 07:35:39.778 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-03 07:35:39.789 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-03 07:35:39.800 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 07:35:39.800 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:35:39.800 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:35:39.811 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 07:35:39.812 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-03 07:35:39.812 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-03 07:35:39.823 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-03 07:35:39.834 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-03 07:35:39.845 [Information] IICProtocolHandler: Enabling IIC module
2025-07-03 07:35:39.856 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-03 07:35:39.867 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-03 07:35:39.878 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 07:35:39.879 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:35:39.879 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:35:39.890 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 07:35:39.891 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-03 07:35:39.892 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-03 07:35:39.892 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-03 07:35:39.892 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-03 07:35:39.892 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-03 07:35:39.893 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-03 07:35:39.893 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-03 07:35:39.893 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-03 07:35:39.893 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-03 07:35:39.894 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-03 07:35:39.894 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-03 07:35:39.894 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-03 07:35:39.894 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-03 07:35:39.894 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-03 07:35:39.895 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-03 07:35:39.895 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-03 07:35:39.994 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 07:35:39.995 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 07:35:39.995 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 07:35:39.995 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:39.995 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 07:35:39.996 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 07:35:39.996 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:39.996 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 07:35:39.996 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 07:35:39.996 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:39.997 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 07:35:39.997 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 07:35:39.997 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:35:39.997 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 07:35:39.997 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 07:35:40.047 [Information] BackupService: Initializing backup service
2025-07-03 07:35:40.048 [Information] BackupService: Backup service initialized successfully
2025-07-03 07:35:40.098 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 07:35:40.099 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 07:35:40.100 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-03 07:35:40.100 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 07:35:40.152 [Information] BackupService: Getting predefined backup categories
2025-07-03 07:35:40.204 [Information] MainViewModel: Services initialized successfully
2025-07-03 07:35:40.206 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 07:35:40.207 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:35:40.207 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:35:40.207 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:35:40.207 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:35:40.208 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:35:40.408 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:35:40.409 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:35:40.409 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:35:40.409 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:35:40.409 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:35:40.410 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:35:40.410 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:35:40.410 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:35:40.411 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:35:40.412 [Information] MainViewModel: Found 4 Vocom device(s)
2025-07-03 07:35:47.185 [Information] MainViewModel: Connecting to Vocom device 88890300-USB
2025-07-03 07:35:47.186 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.187 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-03 07:35:47.187 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:47.599 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-03 07:35:47.600 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-03 07:35:47.600 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-03 07:35:47.603 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-03 07:35:47.603 [Information] ECUCommunicationService: No ECUs are connected
2025-07-03 07:35:47.603 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-03 07:35:47.604 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-03 07:35:47.604 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-03 07:35:47.604 [Information] ECUCommunicationService: No ECUs are connected
2025-07-03 07:35:47.605 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:47.614 [Information] VocomService: PTT application is not running
2025-07-03 07:35:47.614 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.614 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.615 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:35:47.623 [Information] VocomService: PTT application is not running
2025-07-03 07:35:47.623 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.623 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:35:47.623 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.624 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:35:47.624 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:35:47.624 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:35:47.624 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:35:47.625 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.625 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.625 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.625 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.626 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.626 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:35:47.626 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.626 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.627 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.627 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.627 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.627 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:35:47.628 [Error] MainViewModel: Failed to connect to Vocom device 88890300-USB
