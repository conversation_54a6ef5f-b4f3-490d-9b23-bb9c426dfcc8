# Simple PowerShell script to build a complete VolvoFlashWR application
param(
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\VolvoFlashWR_Complete_Integrated_Build"
)

Write-Host "=== VolvoFlashWR Complete Application Builder ===" -ForegroundColor Green
Write-Host "Building self-contained application with all dependencies" -ForegroundColor Yellow

# Check if running from the correct directory
if (-not (Test-Path "VolvoFlashWR.sln")) {
    Write-Host "ERROR: Please run this script from the solution root directory" -ForegroundColor Red
    exit 1
}

# Create output directory
if (Test-Path $OutputPath) {
    Write-Host "Cleaning existing output directory..." -ForegroundColor Yellow
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null

try {
    # Step 1: Build the solution
    Write-Host "`n1. Building solution..." -ForegroundColor Cyan
    dotnet build VolvoFlashWR.sln -c $Configuration --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✓ Solution built successfully" -ForegroundColor Green

    # Step 2: Publish the applications
    Write-Host "`n2. Publishing applications..." -ForegroundColor Cyan
    
    # Publish Launcher
    dotnet publish VolvoFlashWR.Launcher\VolvoFlashWR.Launcher.csproj -c $Configuration -o "$OutputPath\Application" --self-contained false --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Launcher publish failed"
    }
    
    # Publish UI
    dotnet publish VolvoFlashWR.UI\VolvoFlashWR.UI.csproj -c $Configuration -o "$OutputPath\Application" --self-contained false --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "UI publish failed"
    }
    
    Write-Host "✓ Applications published successfully" -ForegroundColor Green

    # Step 3: Copy Libraries folder
    Write-Host "`n3. Copying Libraries..." -ForegroundColor Cyan
    if (Test-Path "Libraries") {
        Copy-Item "Libraries" "$OutputPath\Application\Libraries" -Recurse -Force
        Write-Host "✓ Libraries copied successfully" -ForegroundColor Green
        
        # Count libraries
        $libraryCount = (Get-ChildItem "$OutputPath\Application\Libraries" -File -Recurse).Count
        Write-Host "  - Total libraries: $libraryCount" -ForegroundColor White
    } else {
        Write-Host "⚠ Libraries folder not found - creating empty folder" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path "$OutputPath\Application\Libraries" -Force | Out-Null
        $libraryCount = 0
    }

    # Step 4: Copy Drivers folder
    Write-Host "`n4. Copying Drivers..." -ForegroundColor Cyan
    if (Test-Path "Drivers") {
        Copy-Item "Drivers" "$OutputPath\Application\Drivers" -Recurse -Force
        Write-Host "✓ Drivers copied successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠ Drivers folder not found - creating empty folder" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path "$OutputPath\Application\Drivers\Vocom" -Force | Out-Null
    }

    # Step 5: Create startup scripts
    Write-Host "`n5. Creating startup scripts..." -ForegroundColor Cyan
    
    # Create batch file for normal mode
    $normalModeBat = '@echo off
echo Starting VolvoFlashWR in Normal Mode...
echo.
echo Setting up environment...
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set PATH=%~dp0Libraries;%~dp0Libraries\VCRedist;%~dp0Drivers\Vocom;%PATH%

echo Starting application...
"%~dp0VolvoFlashWR.Launcher.exe"

if errorlevel 1 (
    echo.
    echo Application exited with error code %errorlevel%
    echo Check the logs for more information.
    pause
)'
    
    $normalModeBat | Out-File "$OutputPath\Application\Run_Normal_Mode.bat" -Encoding ASCII
    
    Write-Host "✓ Startup scripts created" -ForegroundColor Green

    # Step 6: Create configuration files
    Write-Host "`n6. Creating configuration files..." -ForegroundColor Cyan
    
    # Create app.config content
    $configContent = '<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="Libraries;Libraries\VCRedist;Drivers\Vocom" />
    </assemblyBinding>
    <loadFromRemoteSources enabled="true" />
  </runtime>
  <appSettings>
    <add key="VocomDriverPath" value=".\Drivers\Vocom" />
    <add key="LibrariesPath" value=".\Libraries" />
    <add key="UseRealHardware" value="true" />
    <add key="VocomDriverDll" value="WUDFPuma.dll" />
    <add key="ApciDriverDll" value="apci.dll" />
    <add key="EnableVocomLogging" value="true" />
    <add key="UseIntegratedStartup" value="true" />
    <add key="AutoBundleDependencies" value="true" />
  </appSettings>
</configuration>'
    
    $configContent | Out-File "$OutputPath\Application\VolvoFlashWR.Launcher.exe.config" -Encoding UTF8
    $configContent | Out-File "$OutputPath\Application\VolvoFlashWR.UI.exe.config" -Encoding UTF8
    
    Write-Host "✓ Configuration files created" -ForegroundColor Green

    # Step 7: Create documentation
    Write-Host "`n7. Creating documentation..." -ForegroundColor Cyan
    
    $buildDate = Get-Date -Format 'yyyy-MM-dd HH:mm:ss'
    $readmeContent = "# VolvoFlashWR - Complete Application Package

This is a complete, self-contained package of VolvoFlashWR with all necessary libraries and dependencies for Vocom adapter communication.

## What's Included

### Core Application
- VolvoFlashWR.Launcher.exe - Main application launcher
- VolvoFlashWR.UI.exe - User interface application
- All .NET dependencies and libraries

### Libraries ($libraryCount files)
- All Vocom communication libraries
- Visual C++ Redistributables (automatically bundled)
- Phoenix Diag integration libraries
- Volvo-specific communication protocols

### Drivers
- Vocom adapter drivers
- USB communication drivers
- Protocol handlers

### Configuration
- Pre-configured for real hardware mode
- Automatic dependency resolution
- Enhanced logging enabled

## How to Run

### Method 1: Simple Startup (Recommended)
Double-click Run_Normal_Mode.bat

### Method 2: Direct Launch
Run VolvoFlashWR.Launcher.exe directly

## Requirements

- Windows 10/11 (x64)
- Vocom adapter connected via USB
- Vocom driver installed (CommunicationUnitInstaller-2.5.0.0.msi)

## Features

- Self-contained - no additional installations required
- Automatic dependency management
- Real hardware Vocom adapter support
- Enhanced device detection
- Comprehensive logging
- Fallback to dummy mode if hardware not available

## Build Information

- Build Date: $buildDate
- Configuration: $Configuration
- Libraries Included: $libraryCount

For support and updates, visit: https://github.com/SalmanSAH75/S.A.H.VolvoFlashWR.git"
    
    $readmeContent | Out-File "$OutputPath\README.md" -Encoding UTF8
    
    Write-Host "✓ Documentation created" -ForegroundColor Green

    # Step 8: Final summary
    Write-Host "`n=== Build Complete ===" -ForegroundColor Green
    Write-Host "Output location: $OutputPath" -ForegroundColor White
    Write-Host "Application files: $OutputPath\Application" -ForegroundColor White
    
    # Get size information
    $totalSize = (Get-ChildItem $OutputPath -Recurse | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    Write-Host "Total package size: $totalSizeMB MB" -ForegroundColor White
    
    # Count files
    $totalFiles = (Get-ChildItem $OutputPath -Recurse -File).Count
    Write-Host "Total files: $totalFiles" -ForegroundColor White
    
    Write-Host "`nTo test the application:" -ForegroundColor Yellow
    Write-Host "1. Navigate to: $OutputPath\Application" -ForegroundColor White
    Write-Host "2. Run: Run_Normal_Mode.bat" -ForegroundColor White
    
    Write-Host "`nComplete application package ready for deployment!" -ForegroundColor Green

} catch {
    Write-Host "`nBuild failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
