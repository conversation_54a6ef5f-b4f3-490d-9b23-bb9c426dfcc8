Log started at 6/4/2025 8:52:08 PM
2025-06-04 20:52:08.665 [Information] LoggingService: Logging service initialized
2025-06-04 20:52:08.679 [Information] App: Verbose logging enabled
2025-06-04 20:52:08.681 [Information] AppConfigurationService: Initializing configuration service
2025-06-04 20:52:08.682 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config
2025-06-04 20:52:08.682 [Information] AppConfigurationService: Configuration file not found, creating default
2025-06-04 20:52:08.686 [Warning] AppConfigurationService: Configuration service not initialized
2025-06-04 20:52:08.686 [Information] AppConfigurationService: Default configuration created
2025-06-04 20:52:08.687 [Information] AppConfigurationService: Configuration service initialized successfully
2025-06-04 20:52:08.687 [Information] App: Configuration service initialized successfully
2025-06-04 20:52:08.688 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-06-04 20:52:08.689 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-06-04 20:52:08.695 [Information] App: Environment variable exists: True, not 'false': False
2025-06-04 20:52:08.696 [Information] App: Final useDummyImplementations value: False
2025-06-04 20:52:08.696 [Information] App: Updating config to NOT use dummy implementations
2025-06-04 20:52:08.698 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-06-04 20:52:08.762 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-06-04 20:52:08.763 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-06-04 20:52:08.763 [Information] App: usePatchedImplementation flag is: True
2025-06-04 20:52:08.764 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-06-04 20:52:08.764 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\Libraries'
2025-06-04 20:52:08.764 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-06-04 20:52:08.765 [Information] App: verboseLogging flag is: True
2025-06-04 20:52:08.766 [Information] App: Verifying real hardware requirements...
2025-06-04 20:52:08.767 [Warning] App: ✗ Missing critical library: WUDFPuma.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\WUDFPuma.dll
2025-06-04 20:52:08.767 [Warning] App: ✗ Missing critical library: apci.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\apci.dll
2025-06-04 20:52:08.768 [Warning] App: ✗ Missing critical library: Volvo.ApciPlus.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlus.dll
2025-06-04 20:52:08.768 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlusData.dll
2025-06-04 20:52:08.768 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:52:08.769 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-06-04 20:52:08.769 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\config.json
2025-06-04 20:52:08.770 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-06-04 20:52:08.782 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-06-04 20:52:08.783 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-06-04 20:52:08.784 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-06-04 20:52:08.786 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-06-04 20:52:08.786 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Communication.dll
2025-06-04 20:52:08.818 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-06-04 20:52:08.819 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\patched_factory_created.txt
2025-06-04 20:52:08.820 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-06-04 20:52:08.820 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-06-04 20:52:08.820 [Information] App: Checking if PTT application is running before creating Vocom service
2025-06-04 20:52:08.882 [Information] App: Creating Vocom service (attempt 1/3)
2025-06-04 20:52:08.886 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-06-04 20:52:08.887 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-06-04 20:52:08.887 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-06-04 20:52:08.888 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-06-04 20:52:08.888 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-06-04 20:52:08.891 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-06-04 20:52:08.895 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-06-04 20:52:08.901 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-06-04 20:52:08.902 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-06-04 20:52:08.903 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-06-04 20:52:08.917 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\apci.dll
2025-06-04 20:52:08.919 [Information] PhoenixVocomAdapter: Copied apci.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-06-04 20:52:08.921 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\apcidb.dll
2025-06-04 20:52:08.923 [Information] PhoenixVocomAdapter: Copied apcidb.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apcidb.dll
2025-06-04 20:52:08.937 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-06-04 20:52:08.941 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlus.dll
2025-06-04 20:52:08.944 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-06-04 20:52:08.946 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlusData.dll
2025-06-04 20:52:08.947 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-06-04 20:52:08.948 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlusTea2Data.dll
2025-06-04 20:52:08.950 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-06-04 20:52:08.952 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.NAMS.AC.Services.Interface.dll
2025-06-04 20:52:08.954 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-06-04 20:52:08.956 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.NAMS.AC.Services.Interfaces.dll
2025-06-04 20:52:08.959 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-06-04 20:52:08.961 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.NVS.Core.dll
2025-06-04 20:52:08.962 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-06-04 20:52:08.964 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.NVS.Logging.dll
2025-06-04 20:52:08.966 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-06-04 20:52:08.968 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.NVS.Persistence.dll
2025-06-04 20:52:08.970 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-06-04 20:52:08.972 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.NVS.Persistence.NHibernate.dll
2025-06-04 20:52:08.974 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-06-04 20:52:08.975 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoIt.Baf.Utility.dll
2025-06-04 20:52:08.977 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-06-04 20:52:08.979 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-06-04 20:52:08.981 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-06-04 20:52:08.982 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoIt.Waf.ServiceContract.dll
2025-06-04 20:52:08.984 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-06-04 20:52:08.985 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoIt.Waf.Utility.dll
2025-06-04 20:52:08.987 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-06-04 20:52:08.988 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlus.dll.config
2025-06-04 20:52:08.989 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-06-04 20:52:08.991 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlusData.dll.config
2025-06-04 20:52:09.007 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-06-04 20:52:09.013 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\NHibernate.dll
2025-06-04 20:52:09.014 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-06-04 20:52:09.015 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\NHibernate.Caches.SysCache2.dll
2025-06-04 20:52:09.017 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-06-04 20:52:09.018 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Iesi.Collections.dll
2025-06-04 20:52:09.020 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-06-04 20:52:09.022 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Ionic.Zip.Reduced.dll
2025-06-04 20:52:09.024 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-06-04 20:52:09.030 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-06-04 20:52:09.031 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\DotNetZip.dll
2025-06-04 20:52:09.033 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-06-04 20:52:09.035 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\ICSharpCode.SharpZipLib.dll
2025-06-04 20:52:09.036 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-06-04 20:52:09.037 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Vodia.CommonDomain.Model.dll
2025-06-04 20:52:09.039 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-06-04 20:52:09.041 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Vodia.Contracts.Common.dll
2025-06-04 20:52:09.044 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-06-04 20:52:09.046 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Vodia.UtilityComponent.dll
2025-06-04 20:52:09.047 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\log4net.dll
2025-06-04 20:52:09.049 [Information] PhoenixVocomAdapter: Copied log4net.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\log4net.dll
2025-06-04 20:52:09.051 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-06-04 20:52:09.053 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-06-04 20:52:09.055 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\AutoMapper.dll
2025-06-04 20:52:09.056 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-06-04 20:52:09.058 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.AppContext.dll
2025-06-04 20:52:09.060 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.AppContext.dll
2025-06-04 20:52:09.061 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Buffers.dll
2025-06-04 20:52:09.063 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Buffers.dll
2025-06-04 20:52:09.064 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-06-04 20:52:09.066 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Collections.Concurrent.dll
2025-06-04 20:52:09.067 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Collections.dll
2025-06-04 20:52:09.068 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Collections.dll
2025-06-04 20:52:09.070 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-06-04 20:52:09.071 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Collections.NonGeneric.dll
2025-06-04 20:52:09.073 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-06-04 20:52:09.075 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Collections.Specialized.dll
2025-06-04 20:52:09.077 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.ComponentModel.dll
2025-06-04 20:52:09.079 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.ComponentModel.dll
2025-06-04 20:52:09.080 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-06-04 20:52:09.100 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.ComponentModel.EventBasedAsync.dll
2025-06-04 20:52:09.102 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-06-04 20:52:09.103 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.ComponentModel.Primitives.dll
2025-06-04 20:52:09.105 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-06-04 20:52:09.107 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.ComponentModel.TypeConverter.dll
2025-06-04 20:52:09.109 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Console.dll
2025-06-04 20:52:09.110 [Information] PhoenixVocomAdapter: Copied System.Console.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Console.dll
2025-06-04 20:52:09.112 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Data.Common.dll
2025-06-04 20:52:09.114 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Data.Common.dll
2025-06-04 20:52:09.115 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-06-04 20:52:09.117 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Data.SQLite.dll
2025-06-04 20:52:09.119 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-06-04 20:52:09.120 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Data.SqlServerCe.dll
2025-06-04 20:52:09.122 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-06-04 20:52:09.123 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.Contracts.dll
2025-06-04 20:52:09.124 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-06-04 20:52:09.126 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.Debug.dll
2025-06-04 20:52:09.128 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-06-04 20:52:09.129 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.FileVersionInfo.dll
2025-06-04 20:52:09.131 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-06-04 20:52:09.132 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.Process.dll
2025-06-04 20:52:09.133 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-06-04 20:52:09.134 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.StackTrace.dll
2025-06-04 20:52:09.136 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-06-04 20:52:09.137 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.TextWriterTraceListener.dll
2025-06-04 20:52:09.138 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-06-04 20:52:09.140 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.Tools.dll
2025-06-04 20:52:09.141 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-06-04 20:52:09.143 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.TraceSource.dll
2025-06-04 20:52:09.145 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-06-04 20:52:09.147 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Diagnostics.Tracing.dll
2025-06-04 20:52:09.148 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-06-04 20:52:09.149 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Drawing.Primitives.dll
2025-06-04 20:52:09.151 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-06-04 20:52:09.153 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Dynamic.Runtime.dll
2025-06-04 20:52:09.154 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-06-04 20:52:09.156 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Globalization.Calendars.dll
2025-06-04 20:52:09.157 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Globalization.dll
2025-06-04 20:52:09.159 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Globalization.dll
2025-06-04 20:52:09.161 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-06-04 20:52:09.162 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Globalization.Extensions.dll
2025-06-04 20:52:09.163 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.Compression.dll
2025-06-04 20:52:09.168 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.Compression.dll
2025-06-04 20:52:09.169 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-06-04 20:52:09.170 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.Compression.ZipFile.dll
2025-06-04 20:52:09.172 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.dll
2025-06-04 20:52:09.174 [Information] PhoenixVocomAdapter: Copied System.IO.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.dll
2025-06-04 20:52:09.175 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-06-04 20:52:09.177 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.FileSystem.dll
2025-06-04 20:52:09.178 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-06-04 20:52:09.180 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.FileSystem.DriveInfo.dll
2025-06-04 20:52:09.182 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-06-04 20:52:09.183 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.FileSystem.Primitives.dll
2025-06-04 20:52:09.184 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-06-04 20:52:09.186 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.FileSystem.Watcher.dll
2025-06-04 20:52:09.187 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-06-04 20:52:09.188 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.IsolatedStorage.dll
2025-06-04 20:52:09.190 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-06-04 20:52:09.192 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.MemoryMappedFiles.dll
2025-06-04 20:52:09.194 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-06-04 20:52:09.195 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.Pipes.dll
2025-06-04 20:52:09.197 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-06-04 20:52:09.200 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.IO.UnmanagedMemoryStream.dll
2025-06-04 20:52:09.202 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Linq.dll
2025-06-04 20:52:09.203 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Linq.dll
2025-06-04 20:52:09.204 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-06-04 20:52:09.206 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Linq.Expressions.dll
2025-06-04 20:52:09.207 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-06-04 20:52:09.209 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Linq.Parallel.dll
2025-06-04 20:52:09.211 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-06-04 20:52:09.212 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Linq.Queryable.dll
2025-06-04 20:52:09.214 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Memory.dll
2025-06-04 20:52:09.215 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Memory.dll
2025-06-04 20:52:09.217 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.Http.dll
2025-06-04 20:52:09.219 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.Http.dll
2025-06-04 20:52:09.220 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-06-04 20:52:09.221 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.NameResolution.dll
2025-06-04 20:52:09.223 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-06-04 20:52:09.224 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.NetworkInformation.dll
2025-06-04 20:52:09.226 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.Ping.dll
2025-06-04 20:52:09.228 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.Ping.dll
2025-06-04 20:52:09.229 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-06-04 20:52:09.230 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.Primitives.dll
2025-06-04 20:52:09.232 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.Requests.dll
2025-06-04 20:52:09.233 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.Requests.dll
2025-06-04 20:52:09.234 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.Security.dll
2025-06-04 20:52:09.236 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.Security.dll
2025-06-04 20:52:09.237 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-06-04 20:52:09.239 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.Sockets.dll
2025-06-04 20:52:09.240 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-06-04 20:52:09.242 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.WebHeaderCollection.dll
2025-06-04 20:52:09.243 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-06-04 20:52:09.245 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.WebSockets.Client.dll
2025-06-04 20:52:09.246 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-06-04 20:52:09.248 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Net.WebSockets.dll
2025-06-04 20:52:09.249 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-06-04 20:52:09.251 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Numerics.Vectors.dll
2025-06-04 20:52:09.252 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.ObjectModel.dll
2025-06-04 20:52:09.253 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.ObjectModel.dll
2025-06-04 20:52:09.255 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Reflection.dll
2025-06-04 20:52:09.256 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Reflection.dll
2025-06-04 20:52:09.258 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-06-04 20:52:09.260 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Reflection.Extensions.dll
2025-06-04 20:52:09.261 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-06-04 20:52:09.263 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Reflection.Primitives.dll
2025-06-04 20:52:09.264 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-06-04 20:52:09.266 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Resources.Reader.dll
2025-06-04 20:52:09.267 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-06-04 20:52:09.269 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Resources.ResourceManager.dll
2025-06-04 20:52:09.271 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-06-04 20:52:09.272 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Resources.Writer.dll
2025-06-04 20:52:09.274 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-06-04 20:52:09.275 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.CompilerServices.Unsafe.dll
2025-06-04 20:52:09.277 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-06-04 20:52:09.278 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.CompilerServices.VisualC.dll
2025-06-04 20:52:09.280 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.dll
2025-06-04 20:52:09.281 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.dll
2025-06-04 20:52:09.283 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-06-04 20:52:09.284 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Extensions.dll
2025-06-04 20:52:09.285 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-06-04 20:52:09.287 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Handles.dll
2025-06-04 20:52:09.288 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-06-04 20:52:09.289 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.InteropServices.dll
2025-06-04 20:52:09.290 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-06-04 20:52:09.292 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.InteropServices.RuntimeInformation.dll
2025-06-04 20:52:09.294 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-06-04 20:52:09.296 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Numerics.dll
2025-06-04 20:52:09.297 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-06-04 20:52:09.298 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Serialization.Formatters.dll
2025-06-04 20:52:09.300 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-06-04 20:52:09.302 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Serialization.Json.dll
2025-06-04 20:52:09.303 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-06-04 20:52:09.305 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Serialization.Primitives.dll
2025-06-04 20:52:09.306 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-06-04 20:52:09.308 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Runtime.Serialization.Xml.dll
2025-06-04 20:52:09.309 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Claims.dll
2025-06-04 20:52:09.311 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Claims.dll
2025-06-04 20:52:09.312 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-06-04 20:52:09.314 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Cryptography.Algorithms.dll
2025-06-04 20:52:09.315 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-06-04 20:52:09.316 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Cryptography.Csp.dll
2025-06-04 20:52:09.318 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-06-04 20:52:09.319 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Cryptography.Encoding.dll
2025-06-04 20:52:09.320 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-06-04 20:52:09.322 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Cryptography.Primitives.dll
2025-06-04 20:52:09.323 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-06-04 20:52:09.325 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Cryptography.X509Certificates.dll
2025-06-04 20:52:09.326 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.Principal.dll
2025-06-04 20:52:09.328 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.Principal.dll
2025-06-04 20:52:09.330 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-06-04 20:52:09.331 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Security.SecureString.dll
2025-06-04 20:52:09.333 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-06-04 20:52:09.334 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Text.Encoding.dll
2025-06-04 20:52:09.335 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-06-04 20:52:09.337 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Text.Encoding.Extensions.dll
2025-06-04 20:52:09.338 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-06-04 20:52:09.340 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Text.RegularExpressions.dll
2025-06-04 20:52:09.341 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.dll
2025-06-04 20:52:09.342 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.dll
2025-06-04 20:52:09.344 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-06-04 20:52:09.346 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.Overlapped.dll
2025-06-04 20:52:09.347 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-06-04 20:52:09.348 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.Tasks.dll
2025-06-04 20:52:09.350 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-06-04 20:52:09.351 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.Tasks.Extensions.dll
2025-06-04 20:52:09.352 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-06-04 20:52:09.354 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.Tasks.Parallel.dll
2025-06-04 20:52:09.355 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-06-04 20:52:09.356 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.Thread.dll
2025-06-04 20:52:09.358 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-06-04 20:52:09.359 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.ThreadPool.dll
2025-06-04 20:52:09.360 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-06-04 20:52:09.362 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Threading.Timer.dll
2025-06-04 20:52:09.363 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.ValueTuple.dll
2025-06-04 20:52:09.365 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.ValueTuple.dll
2025-06-04 20:52:09.366 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-06-04 20:52:09.368 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Xml.ReaderWriter.dll
2025-06-04 20:52:09.369 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-06-04 20:52:09.370 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Xml.XDocument.dll
2025-06-04 20:52:09.372 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-06-04 20:52:09.373 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Xml.XmlDocument.dll
2025-06-04 20:52:09.374 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-06-04 20:52:09.376 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Xml.XmlSerializer.dll
2025-06-04 20:52:09.377 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-06-04 20:52:09.379 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Xml.XPath.dll
2025-06-04 20:52:09.380 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-06-04 20:52:09.382 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\System.Xml.XPath.XDocument.dll
2025-06-04 20:52:09.383 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\System\SystemInterface.dll
2025-06-04 20:52:09.386 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\SystemInterface.dll
2025-06-04 20:52:09.386 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-06-04 20:52:09.391 [Information] PhoenixVocomAdapter: Loading APCI library dynamically
2025-06-04 20:52:09.528 [Error] PhoenixVocomAdapter: Failed to load APCI library. Error code: 193
2025-06-04 20:52:09.529 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-06-04 20:52:09.530 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-06-04 20:52:09.531 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-06-04 20:52:09.532 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-06-04 20:52:09.533 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-06-04 20:52:09.535 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-06-04 20:52:09.536 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-06-04 20:52:09.539 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:52:09.539 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-06-04 20:52:09.539 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-06-04 20:52:09.540 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-06-04 20:52:09.542 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-06-04 20:52:09.542 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-06-04 20:52:09.542 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-06-04 20:52:09.543 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-06-04 20:52:09.544 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-06-04 20:52:09.544 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-06-04 20:52:09.545 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-06-04 20:52:09.547 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-06-04 20:52:09.549 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-06-04 20:52:09.551 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-06-04 20:52:09.553 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-06-04 20:52:09.554 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-06-04 20:52:09.554 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 20:52:09.555 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-06-04 20:52:09.556 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-06-04 20:52:09.556 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-06-04 20:52:09.560 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-06-04 20:52:09.563 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-06-04 20:52:09.563 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-06-04 20:52:09.891 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-06-04 20:52:09.891 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-06-04 20:52:09.892 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-06-04 20:52:09.892 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-06-04 20:52:09.897 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-06-04 20:52:09.899 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:09.899 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-06-04 20:52:09.988 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:09.988 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-06-04 20:52:09.989 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-06-04 20:52:09.989 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-06-04 20:52:10.124 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:10.125 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-06-04 20:52:10.253 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:10.254 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-06-04 20:52:10.362 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-06-04 20:52:10.575 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-06-04 20:52:10.751 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:10.752 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-06-04 20:52:10.752 [Warning] VocomNativeInterop_Patch: Dependency PhoenixGeneral.dll not found in any search path
2025-06-04 20:52:10.753 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-06-04 20:52:10.871 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:10.871 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-06-04 20:52:11.055 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-06-04 20:52:11.056 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-06-04 20:52:11.220 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-06-04 20:52:11.422 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-06-04 20:52:11.488 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-06-04 20:52:11.492 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-06-04 20:52:11.493 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-06-04 20:52:11.493 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-06-04 20:52:11.494 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-06-04 20:52:11.495 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-06-04 20:52:11.497 [Information] VocomDriver: Initializing Vocom driver
2025-06-04 20:52:11.499 [Information] VocomNativeInterop: Initializing Vocom driver
2025-06-04 20:52:11.502 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-06-04 20:52:11.502 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:52:11.502 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:52:11.504 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-06-04 20:52:11.505 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-06-04 20:52:11.508 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-06-04 20:52:11.509 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-06-04 20:52:11.512 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-06-04 20:52:11.514 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-06-04 20:52:11.514 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-06-04 20:52:11.515 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-06-04 20:52:11.518 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-06-04 20:52:11.519 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-06-04 20:52:11.521 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-06-04 20:52:11.522 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-06-04 20:52:11.522 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-06-04 20:52:11.522 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-06-04 20:52:11.523 [Information] VocomDriver: Vocom driver initialized successfully
2025-06-04 20:52:11.528 [Information] VocomService: Initializing Vocom service with dependencies
2025-06-04 20:52:11.529 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-06-04 20:52:11.531 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-06-04 20:52:11.532 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-06-04 20:52:11.623 [Information] WiFiCommunicationService: WiFi is available
2025-06-04 20:52:11.624 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-06-04 20:52:11.628 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-06-04 20:52:11.630 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-06-04 20:52:11.632 [Information] BluetoothCommunicationService: Bluetooth is available
2025-06-04 20:52:11.633 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-06-04 20:52:11.634 [Information] VocomService: Initializing Vocom service
2025-06-04 20:52:11.636 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:52:11.652 [Information] VocomService: PTT application is not running
2025-06-04 20:52:11.654 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:52:11.656 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:52:11.657 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:52:11.657 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-06-04 20:52:11.657 [Information] App: Initializing Vocom service
2025-06-04 20:52:11.658 [Information] VocomService: Initializing Vocom service
2025-06-04 20:52:11.658 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:52:11.674 [Information] VocomService: PTT application is not running
2025-06-04 20:52:11.675 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:52:11.675 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:52:11.676 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:52:11.679 [Information] VocomService: Scanning for Vocom devices
2025-06-04 20:52:11.832 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 20:52:11.865 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 20:52:11.865 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:52:11.866 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:52:11.868 [Debug] VocomService: Checking if WiFi is available
2025-06-04 20:52:11.870 [Debug] VocomService: WiFi is available
2025-06-04 20:52:11.871 [Information] VocomService: Found 2 Vocom devices
2025-06-04 20:52:11.871 [Information] App: Found 2 Vocom devices, attempting to connect to the first one
2025-06-04 20:52:11.874 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:52:11.875 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:52:11.889 [Information] VocomService: PTT application is not running
2025-06-04 20:52:11.891 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:52:11.891 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:52:11.892 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:52:11.896 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-06-04 20:52:12.699 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:52:12.700 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-06-04 20:52:12.700 [Information] App: Connected to Vocom device Vocom - 88890300 (Bluetooth)
2025-06-04 20:52:12.704 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-06-04 20:52:12.707 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:12.708 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-06-04 20:52:12.730 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 20:52:12.733 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 20:52:12.733 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 20:52:12.737 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 20:52:12.738 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 20:52:12.752 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 20:52:12.754 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 20:52:12.757 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 20:52:12.765 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:52:12.768 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:52:12.779 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:52:12.780 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 20:52:12.781 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 20:52:12.782 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:52:12.782 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:52:12.782 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 20:52:12.782 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:52:12.783 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 20:52:12.783 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 20:52:12.789 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 20:52:12.789 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 20:52:12.790 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 20:52:12.790 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 20:52:12.790 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 20:52:12.790 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 20:52:12.791 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 20:52:12.791 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 20:52:12.795 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 20:52:12.802 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-06-04 20:52:12.803 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 20:52:12.808 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 20:52:12.811 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.818 [Information] CANRegisterAccess: Read value 0xF8 from register 0x0141 (simulated)
2025-06-04 20:52:12.827 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.834 [Information] CANRegisterAccess: Read value 0x3A from register 0x0141 (simulated)
2025-06-04 20:52:12.840 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.846 [Information] CANRegisterAccess: Read value 0x3E from register 0x0141 (simulated)
2025-06-04 20:52:12.852 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.858 [Information] CANRegisterAccess: Read value 0xAC from register 0x0141 (simulated)
2025-06-04 20:52:12.864 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.870 [Information] CANRegisterAccess: Read value 0x78 from register 0x0141 (simulated)
2025-06-04 20:52:12.876 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.882 [Information] CANRegisterAccess: Read value 0x64 from register 0x0141 (simulated)
2025-06-04 20:52:12.888 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.895 [Information] CANRegisterAccess: Read value 0x40 from register 0x0141 (simulated)
2025-06-04 20:52:12.901 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:12.907 [Information] CANRegisterAccess: Read value 0xBD from register 0x0141 (simulated)
2025-06-04 20:52:12.907 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-06-04 20:52:12.908 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-06-04 20:52:12.909 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-06-04 20:52:12.915 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-06-04 20:52:12.915 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-06-04 20:52:12.921 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-06-04 20:52:12.921 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-06-04 20:52:12.922 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-06-04 20:52:12.928 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-06-04 20:52:12.929 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-06-04 20:52:12.929 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-06-04 20:52:12.935 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-06-04 20:52:12.935 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-06-04 20:52:12.940 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-06-04 20:52:12.940 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-06-04 20:52:12.946 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-06-04 20:52:12.946 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-06-04 20:52:12.952 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-06-04 20:52:12.952 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-06-04 20:52:12.958 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-06-04 20:52:12.958 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-06-04 20:52:12.963 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-06-04 20:52:12.963 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-06-04 20:52:12.969 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-06-04 20:52:12.969 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-06-04 20:52:12.975 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-06-04 20:52:12.975 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-06-04 20:52:12.981 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-06-04 20:52:12.981 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-06-04 20:52:12.987 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-06-04 20:52:12.987 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-06-04 20:52:12.993 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-06-04 20:52:12.993 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-06-04 20:52:12.998 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-06-04 20:52:12.998 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-06-04 20:52:13.004 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-06-04 20:52:13.004 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-06-04 20:52:13.010 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-06-04 20:52:13.010 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-06-04 20:52:13.016 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-06-04 20:52:13.016 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-06-04 20:52:13.022 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-06-04 20:52:13.022 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-06-04 20:52:13.028 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-06-04 20:52:13.029 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-06-04 20:52:13.029 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-06-04 20:52:13.035 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-06-04 20:52:13.035 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-06-04 20:52:13.036 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:52:13.036 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:13.042 [Information] CANRegisterAccess: Read value 0x41 from register 0x0141 (simulated)
2025-06-04 20:52:13.048 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:13.054 [Information] CANRegisterAccess: Read value 0xEA from register 0x0141 (simulated)
2025-06-04 20:52:13.054 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-06-04 20:52:13.055 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-06-04 20:52:13.055 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-06-04 20:52:13.055 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:52:13.061 [Information] CANRegisterAccess: Read value 0x39 from register 0x0140 (simulated)
2025-06-04 20:52:13.062 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-06-04 20:52:13.063 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:52:13.066 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:52:13.067 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:52:13.078 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 20:52:13.079 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 20:52:13.079 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 20:52:13.084 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:52:13.085 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-06-04 20:52:13.136 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-06-04 20:52:13.137 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 20:52:13.138 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 20:52:13.140 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:52:13.140 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:52:13.151 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:52:13.152 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 20:52:13.152 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 20:52:13.163 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 20:52:13.174 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 20:52:13.185 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 20:52:13.196 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 20:52:13.207 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:52:13.209 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:52:13.210 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:52:13.221 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:52:13.222 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 20:52:13.222 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 20:52:13.233 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 20:52:13.244 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 20:52:13.256 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 20:52:13.267 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 20:52:13.278 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 20:52:13.290 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:52:13.292 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:52:13.292 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:52:13.303 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:52:13.304 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 20:52:13.305 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:52:13.305 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:52:13.305 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 20:52:13.305 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:52:13.306 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 20:52:13.306 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 20:52:13.306 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 20:52:13.307 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 20:52:13.307 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 20:52:13.307 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 20:52:13.307 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 20:52:13.308 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 20:52:13.308 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 20:52:13.308 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 20:52:13.308 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 20:52:13.409 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:52:13.414 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 20:52:13.417 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 20:52:13.419 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:13.419 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 20:52:13.419 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 20:52:13.420 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:13.420 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 20:52:13.421 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 20:52:13.421 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:13.421 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 20:52:13.422 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 20:52:13.422 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:13.423 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 20:52:13.423 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 20:52:13.424 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-06-04 20:52:13.425 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-06-04 20:52:13.425 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-06-04 20:52:13.429 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-06-04 20:52:13.431 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-06-04 20:52:13.436 [Information] BackupService: Initializing backup service
2025-06-04 20:52:13.437 [Information] BackupService: Created backup directory: Backups
2025-06-04 20:52:13.437 [Information] BackupService: Backup service initialized successfully
2025-06-04 20:52:13.438 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-06-04 20:52:13.438 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-06-04 20:52:13.441 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-06-04 20:52:13.478 [Information] BackupService: Compressing backup data
2025-06-04 20:52:13.488 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (448 bytes)
2025-06-04 20:52:13.489 [Information] BackupServiceFactory: Created template for category: Production
2025-06-04 20:52:13.490 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-06-04 20:52:13.491 [Information] BackupService: Compressing backup data
2025-06-04 20:52:13.492 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-06-04 20:52:13.493 [Information] BackupServiceFactory: Created template for category: Development
2025-06-04 20:52:13.493 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-06-04 20:52:13.494 [Information] BackupService: Compressing backup data
2025-06-04 20:52:13.498 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (449 bytes)
2025-06-04 20:52:13.498 [Information] BackupServiceFactory: Created template for category: Testing
2025-06-04 20:52:13.499 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-06-04 20:52:13.500 [Information] BackupService: Compressing backup data
2025-06-04 20:52:13.501 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (450 bytes)
2025-06-04 20:52:13.501 [Information] BackupServiceFactory: Created template for category: Archived
2025-06-04 20:52:13.502 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-06-04 20:52:13.502 [Information] BackupService: Compressing backup data
2025-06-04 20:52:13.503 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-06-04 20:52:13.504 [Information] BackupServiceFactory: Created template for category: Critical
2025-06-04 20:52:13.504 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-06-04 20:52:13.504 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-06-04 20:52:13.505 [Information] BackupService: Compressing backup data
2025-06-04 20:52:13.507 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-06-04 20:52:13.507 [Information] BackupServiceFactory: Created template with predefined tags
2025-06-04 20:52:13.507 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-06-04 20:52:13.509 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-06-04 20:52:13.513 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 20:52:13.514 [Information] BackupSchedulerService: Created schedules directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules
2025-06-04 20:52:13.516 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 20:52:13.517 [Information] BackupSchedulerService: Schedules file not found: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-06-04 20:52:13.517 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 20:52:13.518 [Information] BackupSchedulerService: Starting backup scheduler
2025-06-04 20:52:13.518 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-06-04 20:52:13.519 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-06-04 20:52:13.520 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-06-04 20:52:13.521 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-06-04 20:52:13.526 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-06-04 20:52:13.526 [Information] App: Flash operation monitor service initialized successfully
2025-06-04 20:52:13.540 [Information] LicensingService: Initializing licensing service
2025-06-04 20:52:13.614 [Information] LicensingService: License information saved successfully
2025-06-04 20:52:13.617 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-06-04 20:52:13.618 [Information] App: Licensing service initialized successfully
2025-06-04 20:52:13.618 [Information] App: License status: Trial
2025-06-04 20:52:13.618 [Information] App: Trial period: 30 days remaining
2025-06-04 20:52:13.619 [Information] BackupSchedulerService: Getting all backup schedules
2025-06-04 20:52:13.620 [Information] App: Creating default backup schedules
2025-06-04 20:52:13.625 [Information] ECUCommunicationService: Scanning for ECUs
2025-06-04 20:52:13.626 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-06-04 20:52:14.127 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-06-04 20:52:14.430 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-06-04 20:52:14.732 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-06-04 20:52:15.034 [Information] ECUCommunicationService: Found 10 ECUs
2025-06-04 20:52:15.037 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-06-04 20:52:15.040 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-06-04 20:52:15.089 [Information] BackupSchedulerService: Saved 1 backup schedules to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-06-04 20:52:15.090 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-06-04 20:52:15.090 [Information] App: Created daily backup schedule for EMS
2025-06-04 20:52:15.091 [Information] BackupSchedulerService: Creating backup schedule for ECU EMS
2025-06-04 20:52:15.092 [Information] BackupSchedulerService: Saving backup schedules to disk
2025-06-04 20:52:15.106 [Information] BackupSchedulerService: Saved 2 backup schedules to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-06-04 20:52:15.106 [Information] BackupSchedulerService: Backup schedule created for ECU EMS
2025-06-04 20:52:15.106 [Information] App: Created weekly backup schedule for EMS
2025-06-04 20:52:15.140 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-06-04 20:52:15.304 [Information] VocomService: Initializing Vocom service
2025-06-04 20:52:15.304 [Information] VocomService: Checking if PTT application is running
2025-06-04 20:52:15.317 [Information] VocomService: PTT application is not running
2025-06-04 20:52:15.317 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:52:15.318 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:52:15.319 [Information] VocomService: Vocom service initialized successfully
2025-06-04 20:52:15.369 [Information] ECUCommunicationService: Initializing ECU communication service
2025-06-04 20:52:15.370 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-06-04 20:52:15.370 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-06-04 20:52:15.371 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-06-04 20:52:15.371 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-06-04 20:52:15.372 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-06-04 20:52:15.373 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-06-04 20:52:15.374 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-06-04 20:52:15.375 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:52:15.375 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-06-04 20:52:15.386 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:52:15.387 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-06-04 20:52:15.387 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-06-04 20:52:15.388 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:52:15.388 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:52:15.388 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-06-04 20:52:15.388 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:52:15.389 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-06-04 20:52:15.389 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-06-04 20:52:15.389 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-06-04 20:52:15.389 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-06-04 20:52:15.390 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-06-04 20:52:15.390 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-06-04 20:52:15.390 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-06-04 20:52:15.390 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-06-04 20:52:15.391 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-06-04 20:52:15.391 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-06-04 20:52:15.391 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-06-04 20:52:15.397 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-06-04 20:52:15.398 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-06-04 20:52:15.399 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-06-04 20:52:15.399 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:15.405 [Information] CANRegisterAccess: Read value 0xC6 from register 0x0141 (simulated)
2025-06-04 20:52:15.412 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:15.418 [Information] CANRegisterAccess: Read value 0xCD from register 0x0141 (simulated)
2025-06-04 20:52:15.419 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-06-04 20:52:15.419 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-06-04 20:52:15.420 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-06-04 20:52:15.425 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-06-04 20:52:15.426 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-06-04 20:52:15.431 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-06-04 20:52:15.432 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-06-04 20:52:15.432 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-06-04 20:52:15.438 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-06-04 20:52:15.439 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-06-04 20:52:15.439 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-06-04 20:52:15.446 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-06-04 20:52:15.447 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-06-04 20:52:15.453 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-06-04 20:52:15.454 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-06-04 20:52:15.460 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-06-04 20:52:15.461 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-06-04 20:52:15.467 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-06-04 20:52:15.468 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-06-04 20:52:15.474 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-06-04 20:52:15.475 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-06-04 20:52:15.481 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-06-04 20:52:15.482 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-06-04 20:52:15.488 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-06-04 20:52:15.489 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-06-04 20:52:15.496 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-06-04 20:52:15.496 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-06-04 20:52:15.502 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-06-04 20:52:15.503 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-06-04 20:52:15.509 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-06-04 20:52:15.510 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-06-04 20:52:15.516 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-06-04 20:52:15.517 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-06-04 20:52:15.523 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-06-04 20:52:15.524 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-06-04 20:52:15.530 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-06-04 20:52:15.531 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-06-04 20:52:15.537 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-06-04 20:52:15.538 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-06-04 20:52:15.544 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-06-04 20:52:15.545 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-06-04 20:52:15.551 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-06-04 20:52:15.552 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-06-04 20:52:15.558 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-06-04 20:52:15.559 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-06-04 20:52:15.559 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-06-04 20:52:15.565 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-06-04 20:52:15.566 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-06-04 20:52:15.566 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-06-04 20:52:15.566 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-06-04 20:52:15.572 [Information] CANRegisterAccess: Read value 0xB0 from register 0x0141 (simulated)
2025-06-04 20:52:15.573 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-06-04 20:52:15.573 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-06-04 20:52:15.573 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-06-04 20:52:15.574 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-06-04 20:52:15.580 [Information] CANRegisterAccess: Read value 0x73 from register 0x0140 (simulated)
2025-06-04 20:52:15.581 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-06-04 20:52:15.581 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-06-04 20:52:15.582 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:52:15.582 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-06-04 20:52:15.593 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-06-04 20:52:15.594 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-06-04 20:52:15.594 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-06-04 20:52:15.594 [Information] VocomService: Sending data and waiting for response
2025-06-04 20:52:15.595 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-06-04 20:52:15.644 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-06-04 20:52:15.645 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-06-04 20:52:15.646 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-06-04 20:52:15.646 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:52:15.647 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-06-04 20:52:15.658 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:52:15.659 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-06-04 20:52:15.659 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-06-04 20:52:15.670 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-06-04 20:52:15.681 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-06-04 20:52:15.692 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-06-04 20:52:15.703 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-06-04 20:52:15.714 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-06-04 20:52:15.715 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:52:15.715 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-06-04 20:52:15.726 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:52:15.727 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-06-04 20:52:15.727 [Information] IICProtocolHandler: Checking IIC bus status
2025-06-04 20:52:15.738 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-06-04 20:52:15.749 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-06-04 20:52:15.760 [Information] IICProtocolHandler: Enabling IIC module
2025-06-04 20:52:15.771 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-06-04 20:52:15.782 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-06-04 20:52:15.793 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-06-04 20:52:15.794 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:52:15.794 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-06-04 20:52:15.805 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:52:15.806 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-06-04 20:52:15.806 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-06-04 20:52:15.806 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-06-04 20:52:15.807 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-06-04 20:52:15.807 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-06-04 20:52:15.807 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-06-04 20:52:15.808 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-06-04 20:52:15.808 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-06-04 20:52:15.808 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-06-04 20:52:15.809 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-06-04 20:52:15.809 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-06-04 20:52:15.809 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-06-04 20:52:15.809 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-06-04 20:52:15.810 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-06-04 20:52:15.810 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-06-04 20:52:15.810 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-06-04 20:52:15.910 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-06-04 20:52:15.911 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-06-04 20:52:15.911 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-06-04 20:52:15.912 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:15.912 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-06-04 20:52:15.913 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-06-04 20:52:15.913 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:15.913 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-06-04 20:52:15.914 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-06-04 20:52:15.914 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:15.914 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-06-04 20:52:15.915 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-06-04 20:52:15.915 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-06-04 20:52:15.915 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-06-04 20:52:15.916 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-06-04 20:52:15.966 [Information] BackupService: Initializing backup service
2025-06-04 20:52:15.967 [Information] BackupService: Backup service initialized successfully
2025-06-04 20:52:16.017 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-06-04 20:52:16.018 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-06-04 20:52:16.039 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-06-04 20:52:16.040 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-06-04 20:52:16.092 [Information] BackupService: Getting predefined backup categories
2025-06-04 20:52:16.143 [Information] MainViewModel: Services initialized successfully
2025-06-04 20:52:16.147 [Information] MainViewModel: Scanning for Vocom devices
2025-06-04 20:52:16.149 [Information] VocomService: Scanning for Vocom devices
2025-06-04 20:52:16.149 [Information] ModernUSBCommunicationService: Detecting Vocom devices using modern USB service
2025-06-04 20:52:16.150 [Information] ModernUSBCommunicationService: Found 0 Vocom devices
2025-06-04 20:52:16.150 [Debug] VocomService: Checking if Bluetooth is enabled
2025-06-04 20:52:16.152 [Debug] VocomService: Bluetooth is enabled
2025-06-04 20:52:16.153 [Debug] VocomService: Checking if WiFi is available
2025-06-04 20:52:16.154 [Debug] VocomService: WiFi is available
2025-06-04 20:52:16.154 [Information] VocomService: Found 2 Vocom devices
2025-06-04 20:52:16.155 [Information] MainViewModel: Found 2 Vocom device(s)
