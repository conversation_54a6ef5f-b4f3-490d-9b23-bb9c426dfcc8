# PowerShell script to build a complete, self-contained VolvoFlashWR application
# Includes all necessary libraries, tools, and dependencies for Vocom adapter communication

param(
    [string]$Configuration = "Release",
    [string]$OutputPath = ".\VolvoFlashWR_Complete_Build",
    [switch]$IncludeDebugSymbols = $false,
    [switch]$CreateInstaller = $false
)

Write-Host "=== VolvoFlashWR Complete Application Builder ===" -ForegroundColor Green
Write-Host "Building self-contained application with all dependencies" -ForegroundColor Yellow

# Check if running from the correct directory
if (-not (Test-Path "VolvoFlashWR.sln")) {
    Write-Host "ERROR: Please run this script from the solution root directory" -ForegroundColor Red
    exit 1
}

# Create output directory
if (Test-Path $OutputPath) {
    Write-Host "Cleaning existing output directory..." -ForegroundColor Yellow
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null

try {
    # Step 1: Build the solution
    Write-Host "`n1. Building solution..." -ForegroundColor Cyan
    dotnet build VolvoFlashWR.sln -c $Configuration --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Build failed"
    }
    Write-Host "✓ Solution built successfully" -ForegroundColor Green

    # Step 2: Publish the applications
    Write-Host "`n2. Publishing applications..." -ForegroundColor Cyan
    
    # Publish Launcher
    dotnet publish VolvoFlashWR.Launcher\VolvoFlashWR.Launcher.csproj -c $Configuration -o "$OutputPath\Application" --self-contained false --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "Launcher publish failed"
    }
    
    # Publish UI
    dotnet publish VolvoFlashWR.UI\VolvoFlashWR.UI.csproj -c $Configuration -o "$OutputPath\Application" --self-contained false --verbosity minimal
    if ($LASTEXITCODE -ne 0) {
        throw "UI publish failed"
    }
    
    Write-Host "✓ Applications published successfully" -ForegroundColor Green

    # Step 3: Copy Libraries folder
    Write-Host "`n3. Copying Libraries..." -ForegroundColor Cyan
    if (Test-Path "Libraries") {
        Copy-Item "Libraries" "$OutputPath\Application\Libraries" -Recurse -Force
        Write-Host "✓ Libraries copied successfully" -ForegroundColor Green
        
        # Count libraries
        $libraryCount = (Get-ChildItem "$OutputPath\Application\Libraries" -File -Recurse).Count
        Write-Host "  - Total libraries: $libraryCount" -ForegroundColor White
    } else {
        Write-Host "⚠ Libraries folder not found - creating empty folder" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path "$OutputPath\Application\Libraries" -Force | Out-Null
    }

    # Step 4: Copy Drivers folder
    Write-Host "`n4. Copying Drivers..." -ForegroundColor Cyan
    if (Test-Path "Drivers") {
        Copy-Item "Drivers" "$OutputPath\Application\Drivers" -Recurse -Force
        Write-Host "✓ Drivers copied successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠ Drivers folder not found - creating empty folder" -ForegroundColor Yellow
        New-Item -ItemType Directory -Path "$OutputPath\Application\Drivers\Vocom" -Force | Out-Null
    }

    # Step 5: Copy additional Visual C++ Redistributables
    Write-Host "`n5. Adding Visual C++ Redistributables..." -ForegroundColor Cyan
    $vcRedistPath = "$OutputPath\Application\Libraries\VCRedist"
    New-Item -ItemType Directory -Path $vcRedistPath -Force | Out-Null

    # Copy system VC++ redistributables
    $systemPaths = @(
        "$env:SystemRoot\System32",
        "$env:SystemRoot\SysWOW64"
    )

    $vcLibraries = @(
        "msvcr120.dll",
        "msvcp120.dll", 
        "msvcr140.dll",
        "msvcp140.dll",
        "vcruntime140.dll",
        "api-ms-win-crt-runtime-l1-1-0.dll",
        "api-ms-win-crt-heap-l1-1-0.dll",
        "api-ms-win-crt-string-l1-1-0.dll"
    )

    $copiedVCLibs = 0
    foreach ($lib in $vcLibraries) {
        foreach ($systemPath in $systemPaths) {
            $sourcePath = Join-Path $systemPath $lib
            if (Test-Path $sourcePath) {
                $targetPath = Join-Path $vcRedistPath $lib
                try {
                    Copy-Item $sourcePath $targetPath -Force
                    $copiedVCLibs++
                    Write-Host "  - Copied: $lib" -ForegroundColor White
                    break
                } catch {
                    Write-Host "  - Failed to copy: $lib" -ForegroundColor Yellow
                }
            }
        }
    }
    Write-Host "✓ Copied $copiedVCLibs Visual C++ libraries" -ForegroundColor Green

    # Step 6: Create configuration files
    Write-Host "`n6. Creating configuration files..." -ForegroundColor Cyan
    
    # Create app.config for Launcher
    $configContent = '<?xml version="1.0" encoding="utf-8"?>' + "`n"
    $configContent += '<configuration>' + "`n"
    $configContent += '  <runtime>' + "`n"
    $configContent += '    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">' + "`n"
    $configContent += '      <probing privatePath="Libraries;Libraries\VCRedist;Drivers\Vocom" />' + "`n"
    $configContent += '    </assemblyBinding>' + "`n"
    $configContent += '    <loadFromRemoteSources enabled="true" />' + "`n"
    $configContent += '  </runtime>' + "`n"
    $configContent += '  <appSettings>' + "`n"
    $configContent += '    <add key="VocomDriverPath" value=".\Drivers\Vocom" />' + "`n"
    $configContent += '    <add key="LibrariesPath" value=".\Libraries" />' + "`n"
    $configContent += '    <add key="UseRealHardware" value="true" />' + "`n"
    $configContent += '    <add key="VocomDriverDll" value="WUDFPuma.dll" />' + "`n"
    $configContent += '    <add key="ApciDriverDll" value="apci.dll" />' + "`n"
    $configContent += '    <add key="EnableVocomLogging" value="true" />' + "`n"
    $configContent += '    <add key="UseIntegratedStartup" value="true" />' + "`n"
    $configContent += '    <add key="AutoBundleDependencies" value="true" />' + "`n"
    $configContent += '  </appSettings>' + "`n"
    $configContent += '</configuration>'

    $configContent | Out-File "$OutputPath\Application\VolvoFlashWR.Launcher.exe.config" -Encoding UTF8

    # Create app.config for UI
    $configContent | Out-File "$OutputPath\Application\VolvoFlashWR.UI.exe.config" -Encoding UTF8
    
    Write-Host "✓ Configuration files created" -ForegroundColor Green

    # Step 7: Create startup scripts
    Write-Host "`n7. Creating startup scripts..." -ForegroundColor Cyan
    
    # Create batch file for normal mode
    $normalModeBat = @"
@echo off
echo Starting VolvoFlashWR in Normal Mode...
echo.
echo Setting up environment...
set USE_PATCHED_IMPLEMENTATION=true
set PHOENIX_VOCOM_ENABLED=true
set VERBOSE_LOGGING=true
set PATH=%~dp0Libraries;%~dp0Libraries\VCRedist;%~dp0Drivers\Vocom;%PATH%

echo Starting application...
"%~dp0VolvoFlashWR.Launcher.exe"

if errorlevel 1 (
    echo.
    echo Application exited with error code %errorlevel%
    echo Check the logs for more information.
    pause
)
"@
    
    $normalModeBat | Out-File "$OutputPath\Application\Run_Normal_Mode.bat" -Encoding ASCII
    
    # Create PowerShell script for advanced startup
    $advancedStartupPs1 = @"
# Advanced startup script for VolvoFlashWR
Write-Host "=== VolvoFlashWR Advanced Startup ===" -ForegroundColor Green

# Set environment variables
`$env:USE_PATCHED_IMPLEMENTATION = "true"
`$env:PHOENIX_VOCOM_ENABLED = "true"
`$env:VERBOSE_LOGGING = "true"
`$env:USE_INTEGRATED_STARTUP = "true"

# Add library paths to PATH
`$currentPath = `$env:PATH
`$librariesPath = Join-Path `$PSScriptRoot "Libraries"
`$vcRedistPath = Join-Path `$PSScriptRoot "Libraries\VCRedist"
`$driversPath = Join-Path `$PSScriptRoot "Drivers\Vocom"
`$env:PATH = "`$librariesPath;`$vcRedistPath;`$driversPath;`$currentPath"

Write-Host "Environment configured for real hardware mode" -ForegroundColor Yellow
Write-Host "Starting VolvoFlashWR..." -ForegroundColor Cyan

# Start the application
`$launcherPath = Join-Path `$PSScriptRoot "VolvoFlashWR.Launcher.exe"
Start-Process -FilePath `$launcherPath -Wait

Write-Host "Application closed" -ForegroundColor Green
"@
    
    $advancedStartupPs1 | Out-File "$OutputPath\Application\Start_Advanced.ps1" -Encoding UTF8
    
    Write-Host "✓ Startup scripts created" -ForegroundColor Green

    # Step 8: Create documentation
    Write-Host "`n8. Creating documentation..." -ForegroundColor Cyan
    
    # Create README content
    $readmeContent = "# VolvoFlashWR - Complete Application Package`n`n"
    $readmeContent += "This is a complete, self-contained package of VolvoFlashWR with all necessary libraries and dependencies for Vocom adapter communication.`n`n"
    $readmeContent += "## What's Included`n`n"
    $readmeContent += "### Core Application`n"
    $readmeContent += "- VolvoFlashWR.Launcher.exe - Main application launcher`n"
    $readmeContent += "- VolvoFlashWR.UI.exe - User interface application`n"
    $readmeContent += "- All .NET dependencies and libraries`n`n"
    $readmeContent += "### Libraries ($libraryCount files)`n"
    $readmeContent += "- All Vocom communication libraries`n"
    $readmeContent += "- Visual C++ Redistributables (automatically bundled)`n"
    $readmeContent += "- Phoenix Diag integration libraries`n"
    $readmeContent += "- Volvo-specific communication protocols`n`n"
    $readmeContent += "### Drivers`n"
    $readmeContent += "- Vocom adapter drivers`n"
    $readmeContent += "- USB communication drivers`n"
    $readmeContent += "- Protocol handlers`n`n"
    $readmeContent += "### Configuration`n"
    $readmeContent += "- Pre-configured for real hardware mode`n"
    $readmeContent += "- Automatic dependency resolution`n"
    $readmeContent += "- Enhanced logging enabled`n`n"
    $readmeContent += "## How to Run`n`n"
    $readmeContent += "### Method 1: Simple Startup (Recommended)`n"
    $readmeContent += "Double-click Run_Normal_Mode.bat`n`n"
    $readmeContent += "### Method 2: Advanced Startup`n"
    $readmeContent += "Right-click Start_Advanced.ps1 and select Run with PowerShell`n`n"
    $readmeContent += "### Method 3: Direct Launch`n"
    $readmeContent += "Run VolvoFlashWR.Launcher.exe directly`n`n"
    $readmeContent += "## Requirements`n`n"
    $readmeContent += "- Windows 10/11 (x64)`n"
    $readmeContent += "- Vocom adapter connected via USB`n"
    $readmeContent += "- Vocom driver installed (CommunicationUnitInstaller-2.5.0.0.msi)`n`n"
    $readmeContent += "## Features`n`n"
    $readmeContent += "- Self-contained - no additional installations required`n"
    $readmeContent += "- Automatic dependency management`n"
    $readmeContent += "- Real hardware Vocom adapter support`n"
    $readmeContent += "- Enhanced device detection`n"
    $readmeContent += "- Comprehensive logging`n"
    $readmeContent += "- Fallback to dummy mode if hardware not available`n`n"
    $readmeContent += "## Troubleshooting`n`n"
    $readmeContent += "1. Connection Issues: Check that Vocom adapter is connected and drivers are installed`n"
    $readmeContent += "2. Library Errors: All required libraries are included - check logs for details`n"
    $readmeContent += "3. Permission Issues: Run as Administrator if needed`n"
    $readmeContent += "4. Logs: Check the Logs folder for detailed diagnostic information`n`n"
    $readmeContent += "## Build Information`n`n"
    $readmeContent += "- Build Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')`n"
    $readmeContent += "- Configuration: $Configuration`n"
    $readmeContent += "- Libraries Included: $libraryCount`n"
    $readmeContent += "- VC++ Libraries: $copiedVCLibs`n`n"
    $readmeContent += "For support and updates, visit: https://github.com/SalmanSAH75/S.A.H.VolvoFlashWR.git`n"
    
    $readmeContent | Out-File "$OutputPath\README.md" -Encoding UTF8
    
    Write-Host "✓ Documentation created" -ForegroundColor Green

    # Step 9: Create verification script
    Write-Host "`n9. Creating verification script..." -ForegroundColor Cyan
    
    $verifyScript = @"
# Verification script for VolvoFlashWR installation
Write-Host "=== VolvoFlashWR Installation Verification ===" -ForegroundColor Green

`$errors = @()
`$warnings = @()

# Check core files
`$coreFiles = @(
    "VolvoFlashWR.Launcher.exe",
    "VolvoFlashWR.UI.exe",
    "VolvoFlashWR.Core.dll",
    "VolvoFlashWR.Communication.dll"
)

foreach (`$file in `$coreFiles) {
    if (Test-Path `$file) {
        Write-Host "✓ Found: `$file" -ForegroundColor Green
    } else {
        `$errors += "Missing core file: `$file"
        Write-Host "✗ Missing: `$file" -ForegroundColor Red
    }
}

# Check directories
`$requiredDirs = @("Libraries", "Drivers", "Logs")
foreach (`$dir in `$requiredDirs) {
    if (Test-Path `$dir) {
        `$fileCount = (Get-ChildItem `$dir -Recurse -File).Count
        Write-Host "✓ Directory `$dir exists (`$fileCount files)" -ForegroundColor Green
    } else {
        `$warnings += "Missing directory: `$dir"
        Write-Host "⚠ Missing: `$dir" -ForegroundColor Yellow
    }
}

# Summary
Write-Host "`n=== Verification Summary ===" -ForegroundColor Cyan
if (`$errors.Count -eq 0) {
    Write-Host "✓ All critical components found" -ForegroundColor Green
} else {
    Write-Host "✗ `$(`$errors.Count) critical errors found:" -ForegroundColor Red
    `$errors | ForEach-Object { Write-Host "  - `$_" -ForegroundColor Red }
}

if (`$warnings.Count -gt 0) {
    Write-Host "⚠ `$(`$warnings.Count) warnings:" -ForegroundColor Yellow
    `$warnings | ForEach-Object { Write-Host "  - `$_" -ForegroundColor Yellow }
}

Write-Host "`nInstallation ready for use!" -ForegroundColor Green
"@
    
    $verifyScript | Out-File "$OutputPath\Application\Verify_Installation.ps1" -Encoding UTF8
    
    Write-Host "✓ Verification script created" -ForegroundColor Green

    # Step 10: Final summary
    Write-Host "`n=== Build Complete ===" -ForegroundColor Green
    Write-Host "Output location: $OutputPath" -ForegroundColor White
    Write-Host "Application files: $OutputPath\Application" -ForegroundColor White
    
    # Get size information
    $totalSize = (Get-ChildItem $OutputPath -Recurse | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    Write-Host "Total package size: $totalSizeMB MB" -ForegroundColor White
    
    # Count files
    $totalFiles = (Get-ChildItem $OutputPath -Recurse -File).Count
    Write-Host "Total files: $totalFiles" -ForegroundColor White
    
    Write-Host "`nTo test the application:" -ForegroundColor Yellow
    Write-Host "1. Navigate to: $OutputPath\Application" -ForegroundColor White
    Write-Host "2. Run: Run_Normal_Mode.bat" -ForegroundColor White
    Write-Host "3. Or run: Verify_Installation.ps1 (to check installation)" -ForegroundColor White
    
    Write-Host "`n✅ Complete application package ready for deployment!" -ForegroundColor Green

} catch {
    Write-Host "`n❌ Build failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
