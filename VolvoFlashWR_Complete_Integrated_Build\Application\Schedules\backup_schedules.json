[{"Id": "053733b1-058c-4e7c-a2dd-79d156bd71df", "Name": "Daily Backup - EMS", "Description": "Automatic daily backup at 3:00 AM", "ECUId": "6dece09a-82c8-4ca2-85c0-86ac78fe0182", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 1, "Frequency": 1, "Interval": 1, "TimeOfDay": "03:00:00", "StartHour": 3, "StartMinute": 0, "DaysOfWeek": [1], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-07-03T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-07-04T03:00:00+03:00", "Category": "Automated", "Tags": ["Daily", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 7, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}, {"Id": "b8de5a43-7420-4235-b958-1e7ba354cefa", "Name": "Weekly Backup - EMS", "Description": "Automatic weekly backup on Sunday at 4:00 AM", "ECUId": "6dece09a-82c8-4ca2-85c0-86ac78fe0182", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 2, "Frequency": 2, "Interval": 1, "TimeOfDay": "04:00:00", "StartHour": 4, "StartMinute": 0, "DaysOfWeek": [0], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-07-03T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-07-06T04:00:00+03:00", "Category": "Automated", "Tags": ["Weekly", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 4, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}]