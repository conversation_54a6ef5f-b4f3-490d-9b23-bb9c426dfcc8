[{"Id": "3cfe2a82-e033-411b-b452-65b89eda43ac", "Name": "Daily Backup - EMS", "Description": "Automatic daily backup at 3:00 AM", "ECUId": "877bc21e-58e4-4382-a7d7-a18d17f6c9f8", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 1, "Frequency": 1, "Interval": 1, "TimeOfDay": "03:00:00", "StartHour": 3, "StartMinute": 0, "DaysOfWeek": [1], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-06-04T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-06-05T03:00:00+03:00", "Category": "Automated", "Tags": ["Daily", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 7, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}, {"Id": "50ef1edf-2225-4e69-98ca-4a186da97012", "Name": "Weekly Backup - EMS", "Description": "Automatic weekly backup on Sunday at 4:00 AM", "ECUId": "877bc21e-58e4-4382-a7d7-a18d17f6c9f8", "ECUName": "EMS", "IsEnabled": true, "FrequencyType": 2, "Frequency": 2, "Interval": 1, "TimeOfDay": "04:00:00", "StartHour": 4, "StartMinute": 0, "DaysOfWeek": [0], "StartDayOfWeek": 1, "DayOfMonth": 1, "StartDate": "2025-06-04T00:00:00+03:00", "EndDate": null, "LastExecutionTime": null, "NextExecutionTime": "2025-06-08T04:00:00+03:00", "Category": "Automated", "Tags": ["Weekly", "Automated"], "IncludeEEPROM": true, "IncludeMicrocontrollerCode": true, "IncludeParameters": true, "MaxBackupsToKeep": 4, "MaxBackupAge": 30, "RetryCount": 3, "CustomIntervalDays": 1, "ScheduleType": "Standard", "CreatedBackupIds": []}]