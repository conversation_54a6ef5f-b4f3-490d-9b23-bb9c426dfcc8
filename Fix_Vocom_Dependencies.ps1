# PowerShell script to fix Vocom adapter dependencies and connection issues
# Run this script as Administrator on the target system

Write-Host "=== Vocom Adapter Dependency Fix Script ===" -ForegroundColor Green
Write-Host "This script will attempt to fix common Vocom connection issues" -ForegroundColor Yellow

# Check if running as administrator
if (-NOT ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Right-click PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    pause
    exit 1
}

# Function to check if a file exists
function Test-FileExists {
    param([string]$Path, [string]$Description)
    
    if (Test-Path $Path) {
        Write-Host "✓ Found: $Description at $Path" -ForegroundColor Green
        return $true
    } else {
        Write-Host "✗ Missing: $Description at $Path" -ForegroundColor Red
        return $false
    }
}

# Function to download and install Visual C++ Redistributables
function Install-VCRedist {
    param([string]$Url, [string]$FileName, [string]$Description)
    
    Write-Host "Downloading $Description..." -ForegroundColor Yellow
    
    try {
        $tempPath = "$env:TEMP\$FileName"
        Invoke-WebRequest -Uri $Url -OutFile $tempPath -UseBasicParsing
        
        Write-Host "Installing $Description..." -ForegroundColor Yellow
        Start-Process -FilePath $tempPath -ArgumentList "/quiet" -Wait
        
        Write-Host "✓ Installed: $Description" -ForegroundColor Green
        Remove-Item $tempPath -Force
    } catch {
        Write-Host "✗ Failed to install $Description: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`n1. Checking Vocom Driver Installation..." -ForegroundColor Cyan

# Check for Vocom driver
$vocomDriverPath = "${env:ProgramFiles(x86)}\88890020 Adapter\UMDF\WUDFPuma.dll"
$vocomDriverExists = Test-FileExists $vocomDriverPath "Vocom Driver (WUDFPuma.dll)"

if (-not $vocomDriverExists) {
    Write-Host "WARNING: Vocom driver not found. Please install CommunicationUnitInstaller-*******.msi" -ForegroundColor Yellow
}

Write-Host "`n2. Checking Visual C++ Redistributables..." -ForegroundColor Cyan

# Check for required Visual C++ Redistributables
$vcRedistPaths = @(
    @{Path = "${env:SystemRoot}\System32\msvcr140.dll"; Description = "Visual C++ 2015-2022 Redistributable (x64)"},
    @{Path = "${env:SystemRoot}\SysWOW64\msvcr140.dll"; Description = "Visual C++ 2015-2022 Redistributable (x86)"},
    @{Path = "${env:SystemRoot}\System32\msvcr120.dll"; Description = "Visual C++ 2013 Redistributable (x64)"},
    @{Path = "${env:SystemRoot}\SysWOW64\msvcr120.dll"; Description = "Visual C++ 2013 Redistributable (x86)"}
)

$missingRedist = @()
foreach ($redist in $vcRedistPaths) {
    if (-not (Test-FileExists $redist.Path $redist.Description)) {
        $missingRedist += $redist
    }
}

if ($missingRedist.Count -gt 0) {
    Write-Host "`nInstalling missing Visual C++ Redistributables..." -ForegroundColor Yellow
    
    # Install Visual C++ 2015-2022 Redistributable (x64)
    Install-VCRedist "https://aka.ms/vs/17/release/vc_redist.x64.exe" "vc_redist.x64.exe" "Visual C++ 2015-2022 Redistributable (x64)"
    
    # Install Visual C++ 2015-2022 Redistributable (x86)
    Install-VCRedist "https://aka.ms/vs/17/release/vc_redist.x86.exe" "vc_redist.x86.exe" "Visual C++ 2015-2022 Redistributable (x86)"
    
    # Install Visual C++ 2013 Redistributable (x86)
    Install-VCRedist "https://download.microsoft.com/download/2/E/6/2E61CFA4-993B-4DD4-91DA-3737CD5CD6E3/vcredist_x86.exe" "vcredist_x86_2013.exe" "Visual C++ 2013 Redistributable (x86)"
}

Write-Host "`n3. Checking USB Device Detection..." -ForegroundColor Cyan

# Check for connected USB devices that might be Vocom adapters
try {
    $usbDevices = Get-WmiObject -Class Win32_PnPEntity | Where-Object { 
        $_.PNPDeviceID -like "*VID_1A12*" -or 
        $_.Caption -like "*Vocom*" -or 
        $_.Caption -like "*88890300*" -or
        $_.Caption -like "*Communication Unit*"
    }
    
    if ($usbDevices) {
        Write-Host "Found potential Vocom devices:" -ForegroundColor Green
        foreach ($device in $usbDevices) {
            Write-Host "  - $($device.Caption) ($($device.PNPDeviceID))" -ForegroundColor White
        }
    } else {
        Write-Host "No Vocom devices detected via USB" -ForegroundColor Yellow
        Write-Host "Please ensure the Vocom adapter is connected and drivers are installed" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error checking USB devices: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n4. Checking COM Ports..." -ForegroundColor Cyan

# Check for COM ports
try {
    $comPorts = Get-WmiObject -Class Win32_SerialPort
    if ($comPorts) {
        Write-Host "Available COM ports:" -ForegroundColor Green
        foreach ($port in $comPorts) {
            Write-Host "  - $($port.Caption) ($($port.DeviceID))" -ForegroundColor White
        }
    } else {
        Write-Host "No COM ports detected" -ForegroundColor Yellow
    }
} catch {
    Write-Host "Error checking COM ports: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host "`n5. System Information..." -ForegroundColor Cyan
Write-Host "OS Architecture: $($env:PROCESSOR_ARCHITECTURE)" -ForegroundColor White
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host ".NET Framework Versions:" -ForegroundColor White

# Check .NET Framework versions
try {
    $netVersions = Get-ChildItem "HKLM:\SOFTWARE\Microsoft\NET Framework Setup\NDP" -Recurse |
        Get-ItemProperty -Name Version, Release -ErrorAction SilentlyContinue |
        Where-Object { $_.PSChildName -match '^(?!S)\p{L}' } |
        Select-Object PSChildName, Version, Release
    
    foreach ($version in $netVersions) {
        Write-Host "  - $($version.PSChildName): $($version.Version)" -ForegroundColor White
    }
} catch {
    Write-Host "  Could not retrieve .NET Framework versions" -ForegroundColor Yellow
}

Write-Host "`n=== Fix Script Complete ===" -ForegroundColor Green
Write-Host "Please restart your computer and test the Vocom connection again." -ForegroundColor Yellow
Write-Host "If issues persist, check the application logs for detailed error information." -ForegroundColor Yellow

pause
