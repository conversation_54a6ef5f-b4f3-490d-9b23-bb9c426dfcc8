param(
    [string]$OutputPath = ".\VolvoFlashWR_Complete_Integrated_Build"
)

Write-Host "Building VolvoFlashWR Complete Package..." -ForegroundColor Green

# Clean output directory
if (Test-Path $OutputPath) {
    Remove-Item $OutputPath -Recurse -Force
}
New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null

# Build solution
Write-Host "Building solution..." -ForegroundColor Cyan
dotnet build VolvoFlashWR.sln -c Release --verbosity minimal

# Publish applications
Write-Host "Publishing applications..." -ForegroundColor Cyan
dotnet publish VolvoFlashWR.Launcher\VolvoFlashWR.Launcher.csproj -c Release -o "$OutputPath\Application" --self-contained false --verbosity minimal
dotnet publish VolvoFlashWR.UI\VolvoFlashWR.UI.csproj -c Release -o "$OutputPath\Application" --self-contained false --verbosity minimal

# Copy Libraries and Drivers
Write-Host "Copying dependencies..." -ForegroundColor Cyan
if (Test-Path "Libraries") {
    Copy-Item "Libraries" "$OutputPath\Application\Libraries" -Recurse -Force
}
if (Test-Path "Drivers") {
    Copy-Item "Drivers" "$OutputPath\Application\Drivers" -Recurse -Force
}

# Create startup script
Write-Host "Creating startup script..." -ForegroundColor Cyan
$batContent = '@echo off
echo Starting VolvoFlashWR...
set PATH=%~dp0Libraries;%~dp0Drivers\Vocom;%PATH%
"%~dp0VolvoFlashWR.Launcher.exe"
pause'

$batContent | Out-File "$OutputPath\Application\Run_Normal_Mode.bat" -Encoding ASCII

# Create config files
Write-Host "Creating configuration..." -ForegroundColor Cyan
$configXml = '<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <probing privatePath="Libraries;Drivers\Vocom" />
    </assemblyBinding>
  </runtime>
  <appSettings>
    <add key="UseRealHardware" value="true" />
    <add key="UseIntegratedStartup" value="true" />
  </appSettings>
</configuration>'

$configXml | Out-File "$OutputPath\Application\VolvoFlashWR.Launcher.exe.config" -Encoding UTF8
$configXml | Out-File "$OutputPath\Application\VolvoFlashWR.UI.exe.config" -Encoding UTF8

# Create README
$readme = "# VolvoFlashWR Complete Package

## How to Run
Double-click Run_Normal_Mode.bat

## Requirements
- Windows 10/11 (x64)
- Vocom adapter connected
- Vocom driver installed

Build Date: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')"

$readme | Out-File "$OutputPath\README.md" -Encoding UTF8

Write-Host "Build complete! Output: $OutputPath" -ForegroundColor Green
Write-Host "To run: $OutputPath\Application\Run_Normal_Mode.bat" -ForegroundColor Yellow
