using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Threading.Tasks;
using VolvoFlashWR.Core.Interfaces;
using VolvoFlashWR.Core.Models;

namespace VolvoFlashWR.Communication.Vocom
{
    /// <summary>
    /// Enhanced Vocom device detector that addresses real hardware connection issues
    /// </summary>
    public class EnhancedVocomDeviceDetector
    {
        private readonly ILoggingService _logger;

        // Windows API imports for device detection
        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr LoadLibrary(string lpFileName);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern bool FreeLibrary(IntPtr hModule);

        [DllImport("kernel32.dll", SetLastError = true)]
        private static extern IntPtr GetProcAddress(IntPtr hModule, string lpProcName);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern IntPtr SetupDiGetClassDevs(ref Guid classGuid, string enumerator, IntPtr hwndParent, uint flags);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiEnumDeviceInfo(IntPtr deviceInfoSet, uint memberIndex, ref SP_DEVINFO_DATA deviceInfoData);

        [DllImport("setupapi.dll", SetLastError = true)]
        private static extern bool SetupDiDestroyDeviceInfoList(IntPtr deviceInfoSet);

        [DllImport("setupapi.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern bool SetupDiGetDeviceRegistryProperty(IntPtr deviceInfoSet, ref SP_DEVINFO_DATA deviceInfoData,
            uint property, out uint propertyRegDataType, byte[] propertyBuffer, uint propertyBufferSize, out uint requiredSize);

        // Constants for device detection
        private const uint DIGCF_PRESENT = 0x00000002;
        private const uint DIGCF_DEVICEINTERFACE = 0x00000010;
        private const uint SPDRP_DEVICEDESC = 0x00000000;
        private const uint SPDRP_HARDWAREID = 0x00000001;
        private const uint SPDRP_FRIENDLYNAME = 0x0000000C;

        // Vocom device identifiers
        private static readonly Guid USB_DEVICE_CLASS_GUID = new Guid("A5DCBF10-6530-11D2-901F-00C04FB951ED");
        
        private static readonly string[] VocomHardwareIds = new[]
        {
            "USB\\VID_1A12&PID_0001", // Primary Vocom 1 identifier
            "USB\\VID_0BDA&PID_8150", // Alternative identifier
            "USB\\VID_04B4&PID_1004", // Cypress based
            "USB\\VID_0403&PID_6001", // FTDI based
        };

        private static readonly string[] VocomDeviceNames = new[]
        {
            "Vocom - 88890300",
            "88890300",
            "Vocom",
            "Communication Unit",
            "Bluetooth Adapter",
            "CSR8510"
        };

        [StructLayout(LayoutKind.Sequential)]
        private struct SP_DEVINFO_DATA
        {
            public uint cbSize;
            public Guid classGuid;
            public uint devInst;
            public IntPtr reserved;
        }

        public EnhancedVocomDeviceDetector(ILoggingService logger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Detects real Vocom devices connected to the system
        /// </summary>
        public async Task<List<VocomDevice>> DetectRealVocomDevicesAsync()
        {
            var devices = new List<VocomDevice>();

            try
            {
                _logger.LogInformation("Starting enhanced Vocom device detection", "EnhancedVocomDeviceDetector");

                // Method 1: Direct USB device enumeration
                await DetectUSBDevicesAsync(devices);

                // Method 2: WMI-based detection (Windows only)
                if (OperatingSystem.IsWindows())
                {
                    await DetectWMIDevicesAsync(devices);
                }

                // Method 3: Serial port enumeration
                await DetectSerialPortDevicesAsync(devices);

                // Method 4: Registry-based detection
                await DetectRegistryDevicesAsync(devices);

                // Method 5: Driver-based detection
                await DetectDriverBasedDevicesAsync(devices);

                _logger.LogInformation($"Enhanced detection found {devices.Count} Vocom devices", "EnhancedVocomDeviceDetector");
                return devices;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in enhanced Vocom device detection: {ex.Message}", "EnhancedVocomDeviceDetector", ex);
                return devices;
            }
        }

        private async Task DetectUSBDevicesAsync(List<VocomDevice> devices)
        {
            try
            {
                _logger.LogInformation("Detecting USB Vocom devices", "EnhancedVocomDeviceDetector");

                var usbClassGuid = USB_DEVICE_CLASS_GUID;
                IntPtr deviceInfoSet = SetupDiGetClassDevs(ref usbClassGuid, null, IntPtr.Zero, DIGCF_PRESENT);
                if (deviceInfoSet == IntPtr.Zero)
                {
                    _logger.LogWarning("Failed to get USB device class", "EnhancedVocomDeviceDetector");
                    return;
                }

                try
                {
                    SP_DEVINFO_DATA deviceInfoData = new SP_DEVINFO_DATA();
                    deviceInfoData.cbSize = (uint)Marshal.SizeOf(deviceInfoData);

                    uint deviceIndex = 0;
                    while (SetupDiEnumDeviceInfo(deviceInfoSet, deviceIndex, ref deviceInfoData))
                    {
                        string hardwareId = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_HARDWAREID);
                        string friendlyName = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_FRIENDLYNAME);
                        string deviceDesc = GetDeviceProperty(deviceInfoSet, deviceInfoData, SPDRP_DEVICEDESC);

                        // Check if this is a Vocom device
                        bool isVocomDevice = false;
                        
                        // Check hardware ID
                        if (!string.IsNullOrEmpty(hardwareId))
                        {
                            foreach (string vocomId in VocomHardwareIds)
                            {
                                if (hardwareId.Contains(vocomId, StringComparison.OrdinalIgnoreCase))
                                {
                                    isVocomDevice = true;
                                    break;
                                }
                            }
                        }

                        // Check friendly name and description
                        if (!isVocomDevice)
                        {
                            string[] namesToCheck = { friendlyName, deviceDesc };
                            foreach (string name in namesToCheck)
                            {
                                if (!string.IsNullOrEmpty(name))
                                {
                                    foreach (string vocomName in VocomDeviceNames)
                                    {
                                        if (name.Contains(vocomName, StringComparison.OrdinalIgnoreCase))
                                        {
                                            isVocomDevice = true;
                                            break;
                                        }
                                    }
                                    if (isVocomDevice) break;
                                }
                            }
                        }

                        if (isVocomDevice)
                        {
                            var device = new VocomDevice
                            {
                                Id = Guid.NewGuid().ToString(),
                                Name = !string.IsNullOrEmpty(friendlyName) ? friendlyName : "Vocom - 88890300 (USB)",
                                SerialNumber = ExtractSerialNumber(hardwareId) ?? "88890300-USB",
                                ConnectionType = VocomConnectionType.USB,
                                ConnectionStatus = VocomConnectionStatus.Disconnected,
                                USBPortInfo = hardwareId ?? "USB"
                            };

                            if (!devices.Any(d => d.SerialNumber == device.SerialNumber))
                            {
                                devices.Add(device);
                                _logger.LogInformation($"Found USB Vocom device: {device.Name} ({device.SerialNumber})", "EnhancedVocomDeviceDetector");
                            }
                        }

                        deviceIndex++;
                    }
                }
                finally
                {
                    SetupDiDestroyDeviceInfoList(deviceInfoSet);
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting USB devices: {ex.Message}", "EnhancedVocomDeviceDetector");
            }

            await Task.CompletedTask;
        }

        private string GetDeviceProperty(IntPtr deviceInfoSet, SP_DEVINFO_DATA deviceInfoData, uint property)
        {
            try
            {
                uint requiredSize;
                SetupDiGetDeviceRegistryProperty(deviceInfoSet, ref deviceInfoData, property, out _, null, 0, out requiredSize);

                if (requiredSize > 0)
                {
                    byte[] buffer = new byte[requiredSize];
                    if (SetupDiGetDeviceRegistryProperty(deviceInfoSet, ref deviceInfoData, property, out _, buffer, requiredSize, out _))
                    {
                        return System.Text.Encoding.Unicode.GetString(buffer).TrimEnd('\0');
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogDebug($"Error getting device property {property}: {ex.Message}", "EnhancedVocomDeviceDetector");
            }

            return string.Empty;
        }

        private string? ExtractSerialNumber(string hardwareId)
        {
            if (string.IsNullOrEmpty(hardwareId))
                return null;

            // Try to extract serial number from hardware ID
            var parts = hardwareId.Split('&', '\\');
            foreach (var part in parts)
            {
                if (part.StartsWith("88890300", StringComparison.OrdinalIgnoreCase))
                {
                    return part;
                }
            }

            return "88890300-USB";
        }

        private async Task DetectWMIDevicesAsync(List<VocomDevice> devices)
        {
            try
            {
                _logger.LogInformation("Detecting WMI Vocom devices", "EnhancedVocomDeviceDetector");

#if WINDOWS
                using var searcher = new System.Management.ManagementObjectSearcher(
                    "SELECT * FROM Win32_PnPEntity WHERE " +
                    "(PNPDeviceID LIKE '%VID_1A12%' AND PNPDeviceID LIKE '%PID_0001%') OR " +
                    "Caption LIKE '%Vocom%' OR Caption LIKE '%88890300%' OR " +
                    "Caption LIKE '%Communication Unit%' OR Caption LIKE '%CSR8510%'");

                foreach (System.Management.ManagementObject queryObj in searcher.Get())
                {
                    if (queryObj["Caption"] != null && queryObj["PNPDeviceID"] != null)
                    {
                        string caption = queryObj["Caption"].ToString();
                        string deviceId = queryObj["PNPDeviceID"].ToString();

                        var device = new VocomDevice
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = caption,
                            SerialNumber = ExtractSerialNumber(deviceId) ?? "88890300-WMI",
                            ConnectionType = VocomConnectionType.USB,
                            ConnectionStatus = VocomConnectionStatus.Disconnected,
                            USBPortInfo = deviceId
                        };

                        if (!devices.Any(d => d.SerialNumber == device.SerialNumber))
                        {
                            devices.Add(device);
                            _logger.LogInformation($"Found WMI Vocom device: {caption} ({deviceId})", "EnhancedVocomDeviceDetector");
                        }
                    }
                }
#endif
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting WMI devices: {ex.Message}", "EnhancedVocomDeviceDetector");
            }

            await Task.CompletedTask;
        }

        private async Task DetectSerialPortDevicesAsync(List<VocomDevice> devices)
        {
            try
            {
                _logger.LogInformation("Detecting Serial Port Vocom devices", "EnhancedVocomDeviceDetector");

                string[] portNames = System.IO.Ports.SerialPort.GetPortNames();

                foreach (string portName in portNames)
                {
                    try
                    {
#if WINDOWS
                        using var searcher = new System.Management.ManagementObjectSearcher(
                            $"SELECT * FROM Win32_PnPEntity WHERE Caption LIKE '%{portName}%'");

                        foreach (System.Management.ManagementObject queryObj in searcher.Get())
                        {
                            if (queryObj["Caption"] != null)
                            {
                                string caption = queryObj["Caption"].ToString();

                                foreach (string vocomName in VocomDeviceNames)
                                {
                                    if (caption.Contains(vocomName, StringComparison.OrdinalIgnoreCase))
                                    {
                                        var device = new VocomDevice
                                        {
                                            Id = Guid.NewGuid().ToString(),
                                            Name = caption,
                                            SerialNumber = $"88890300-{portName}",
                                            ConnectionType = VocomConnectionType.USB,
                                            ConnectionStatus = VocomConnectionStatus.Disconnected,
                                            USBPortInfo = portName
                                        };

                                        if (!devices.Any(d => d.SerialNumber == device.SerialNumber))
                                        {
                                            devices.Add(device);
                                            _logger.LogInformation($"Found Serial Port Vocom device: {caption} on {portName}", "EnhancedVocomDeviceDetector");
                                        }
                                        break;
                                    }
                                }
                            }
                        }
#else
                        // For non-Windows platforms, add all COM ports as potential Vocom devices
                        var device = new VocomDevice
                        {
                            Id = Guid.NewGuid().ToString(),
                            Name = $"Potential Vocom Device ({portName})",
                            SerialNumber = $"88890300-{portName}",
                            ConnectionType = VocomConnectionType.USB,
                            ConnectionStatus = VocomConnectionStatus.Disconnected,
                            USBPortInfo = portName
                        };

                        if (!devices.Any(d => d.SerialNumber == device.SerialNumber))
                        {
                            devices.Add(device);
                            _logger.LogInformation($"Found potential Vocom device on {portName}", "EnhancedVocomDeviceDetector");
                        }
#endif
                    }
                    catch (Exception ex)
                    {
                        _logger.LogDebug($"Error checking COM port {portName}: {ex.Message}", "EnhancedVocomDeviceDetector");
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting serial port devices: {ex.Message}", "EnhancedVocomDeviceDetector");
            }

            await Task.CompletedTask;
        }

        private async Task DetectRegistryDevicesAsync(List<VocomDevice> devices)
        {
            try
            {
                _logger.LogInformation("Detecting Registry-based Vocom devices", "EnhancedVocomDeviceDetector");

#if WINDOWS
                // Check USB registry entries
                using var usbKey = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(@"SYSTEM\CurrentControlSet\Enum\USB");
                if (usbKey != null)
                {
                    foreach (string subKeyName in usbKey.GetSubKeyNames())
                    {
                        if (VocomHardwareIds.Any(id => subKeyName.Contains(id.Replace("USB\\", ""), StringComparison.OrdinalIgnoreCase)))
                        {
                            using var deviceKey = usbKey.OpenSubKey(subKeyName);
                            if (deviceKey != null)
                            {
                                foreach (string instanceName in deviceKey.GetSubKeyNames())
                                {
                                    using var instanceKey = deviceKey.OpenSubKey(instanceName);
                                    if (instanceKey != null)
                                    {
                                        string friendlyName = instanceKey.GetValue("FriendlyName")?.ToString() ?? "Vocom Device";
                                        string deviceDesc = instanceKey.GetValue("DeviceDesc")?.ToString() ?? "";

                                        var device = new VocomDevice
                                        {
                                            Id = Guid.NewGuid().ToString(),
                                            Name = friendlyName,
                                            SerialNumber = instanceName.Contains("88890300") ? instanceName : $"88890300-{instanceName}",
                                            ConnectionType = VocomConnectionType.USB,
                                            ConnectionStatus = VocomConnectionStatus.Disconnected,
                                            USBPortInfo = $"{subKeyName}\\{instanceName}"
                                        };

                                        if (!devices.Any(d => d.SerialNumber == device.SerialNumber))
                                        {
                                            devices.Add(device);
                                            _logger.LogInformation($"Found Registry Vocom device: {friendlyName}", "EnhancedVocomDeviceDetector");
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
#endif
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting registry devices: {ex.Message}", "EnhancedVocomDeviceDetector");
            }

            await Task.CompletedTask;
        }

        private async Task DetectDriverBasedDevicesAsync(List<VocomDevice> devices)
        {
            try
            {
                _logger.LogInformation("Detecting Driver-based Vocom devices", "EnhancedVocomDeviceDetector");

                // Check if Vocom driver is installed
                string vocomDriverPath = Path.Combine(
                    Environment.GetFolderPath(Environment.SpecialFolder.ProgramFilesX86),
                    "88890020 Adapter", "UMDF", "WUDFPuma.dll");

                if (File.Exists(vocomDriverPath))
                {
                    _logger.LogInformation("Vocom driver found, checking for connected devices", "EnhancedVocomDeviceDetector");

                    // Try to load the driver and check for devices
                    IntPtr driverHandle = LoadLibrary(vocomDriverPath);
                    if (driverHandle != IntPtr.Zero)
                    {
                        try
                        {
                            // If we can load the driver, assume there might be a device
                            // In a real implementation, we would call driver functions to enumerate devices
                            var device = new VocomDevice
                            {
                                Id = Guid.NewGuid().ToString(),
                                Name = "Vocom - 88890300 (Driver)",
                                SerialNumber = "88890300-DRIVER",
                                ConnectionType = VocomConnectionType.USB,
                                ConnectionStatus = VocomConnectionStatus.Disconnected,
                                USBPortInfo = "WUDFPuma Driver"
                            };

                            if (!devices.Any(d => d.SerialNumber == device.SerialNumber))
                            {
                                devices.Add(device);
                                _logger.LogInformation("Added Vocom driver-based device entry", "EnhancedVocomDeviceDetector");
                            }
                        }
                        finally
                        {
                            FreeLibrary(driverHandle);
                        }
                    }
                }
                else
                {
                    _logger.LogWarning("Vocom driver not found at expected location", "EnhancedVocomDeviceDetector");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning($"Error detecting driver-based devices: {ex.Message}", "EnhancedVocomDeviceDetector");
            }

            await Task.CompletedTask;
        }
    }
}
