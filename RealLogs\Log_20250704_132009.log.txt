Log started at 7/4/2025 1:20:10 PM
2025-07-04 13:20:10.013 [Information] LoggingService: Logging service initialized
2025-07-04 13:20:10.023 [Information] App: Starting integrated application initialization
2025-07-04 13:20:10.024 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-04 13:20:10.025 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-04 13:20:10.026 [Information] IntegratedStartupService: Setting up application environment
2025-07-04 13:20:10.027 [Information] IntegratedStartupService: Application environment setup completed
2025-07-04 13:20:10.030 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-04 13:20:10.033 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-04 13:20:10.035 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-04 13:20:10.104 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-04 13:20:10.185 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-04 13:20:10.187 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-04 13:20:10.188 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-04 13:20:10.188 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-04 13:20:10.190 [Debug] VCRedistBundler: Successfully loaded and verified: msvcr120.dll
2025-07-04 13:20:10.190 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-04 13:20:10.196 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp120.dll
2025-07-04 13:20:10.197 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-04 13:20:10.198 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-04 13:20:10.201 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-04 13:20:10.202 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-04 13:20:10.203 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-04 13:20:10.203 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:20:10.204 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:20:10.204 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:20:10.204 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:20:10.205 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:20:10.205 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:20:10.208 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-04 13:20:10.209 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-04 13:20:10.212 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-04 13:20:10.214 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-04 13:20:10.218 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-04 13:20:10.219 [Information] LibraryExtractor: Starting library extraction process
2025-07-04 13:20:10.221 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-04 13:20:10.223 [Information] LibraryExtractor: Copying system libraries
2025-07-04 13:20:10.225 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-04 13:20:10.231 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-04 13:20:23.637 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-04 13:20:43.955 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-04 13:21:13.053 [Information] LibraryExtractor: Verifying library extraction
2025-07-04 13:21:13.054 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-04 13:21:13.054 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-04 13:21:13.055 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-04 13:21:13.055 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-04 13:21:13.055 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-04 13:21:13.057 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-04 13:21:13.059 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-04 13:21:13.059 [Information] DependencyManager: Initializing dependency manager
2025-07-04 13:21:13.060 [Information] DependencyManager: Setting up library search paths
2025-07-04 13:21:13.061 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:21:13.061 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:21:13.061 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-04 13:21:13.062 [Information] DependencyManager: Updated PATH environment variable
2025-07-04 13:21:13.063 [Information] DependencyManager: Verifying required directories
2025-07-04 13:21:13.063 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:21:13.063 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:21:13.064 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-04 13:21:13.064 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-04 13:21:13.065 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-04 13:21:13.070 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-04 13:21:13.070 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-04 13:21:13.072 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-04 13:21:13.073 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-04 13:21:13.073 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-04 13:21:13.074 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-04 13:21:13.074 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-04 13:21:13.076 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 13:21:13.077 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 13:21:13.077 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:21:13.078 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:21:13.078 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:21:13.079 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-04 13:21:13.081 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-04 13:21:13.082 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:21:13.082 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-04 13:21:13.082 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:21:13.083 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:21:13.084 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-04 13:21:13.085 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-04 13:21:13.086 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-04 13:21:13.086 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-04 13:21:13.087 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-04 13:21:13.087 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-04 13:21:13.088 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-04 13:21:13.088 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-04 13:21:13.089 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-04 13:21:13.089 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-04 13:21:13.090 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-04 13:21:13.091 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-04 13:21:13.091 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-04 13:21:13.091 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-04 13:21:13.092 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-04 13:21:13.092 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-04 13:21:13.093 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-04 13:21:13.093 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-04 13:21:13.094 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-04 13:21:13.094 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-04 13:21:13.095 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-04 13:21:13.095 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-04 13:21:13.096 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-04 13:21:13.096 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-04 13:21:13.097 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-04 13:21:13.097 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-04 13:21:13.098 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 13:21:13.098 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 13:21:13.099 [Information] DependencyManager: Setting up environment variables
2025-07-04 13:21:13.099 [Information] DependencyManager: Environment variables configured
2025-07-04 13:21:13.101 [Information] DependencyManager: Verifying library loading status
2025-07-04 13:21:14.743 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-04 13:21:14.744 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-04 13:21:14.744 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-04 13:21:14.746 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-04 13:21:14.747 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-04 13:21:14.749 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-04 13:21:14.750 [Information] IntegratedStartupService: Verifying system readiness
2025-07-04 13:21:14.750 [Information] IntegratedStartupService: System readiness verification passed
2025-07-04 13:21:14.751 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-04 13:21:14.752 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-04 13:21:14.752 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-04 13:21:14.752 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:21:14.752 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:21:14.753 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-04 13:21:14.753 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-04 13:21:14.753 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-04 13:21:14.753 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:21:14.754 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-04 13:21:14.754 [Information] App: Integrated startup completed successfully
2025-07-04 13:21:14.755 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-04 13:21:14.765 [Information] App: Initializing application services
2025-07-04 13:21:14.766 [Information] AppConfigurationService: Initializing configuration service
2025-07-04 13:21:14.766 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-04 13:21:14.793 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-04 13:21:14.794 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-04 13:21:14.795 [Information] App: Configuration service initialized successfully
2025-07-04 13:21:14.796 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-04 13:21:14.796 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-04 13:21:14.802 [Information] App: Environment variable exists: True, not 'false': False
2025-07-04 13:21:14.802 [Information] App: Final useDummyImplementations value: False
2025-07-04 13:21:14.803 [Information] App: Updating config to NOT use dummy implementations
2025-07-04 13:21:14.804 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-04 13:21:14.815 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-04 13:21:14.816 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-04 13:21:14.816 [Information] App: usePatchedImplementation flag is: True
2025-07-04 13:21:14.817 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-04 13:21:14.817 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-04 13:21:14.817 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-04 13:21:14.817 [Information] App: verboseLogging flag is: True
2025-07-04 13:21:14.819 [Information] App: Verifying real hardware requirements...
2025-07-04 13:21:14.819 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-04 13:21:14.819 [Information] App: ✓ Found critical library: apci.dll
2025-07-04 13:21:14.820 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-04 13:21:14.820 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-04 13:21:14.820 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:21:14.821 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-04 13:21:14.821 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-04 13:21:14.821 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-04 13:21:14.832 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-04 13:21:14.832 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-04 13:21:14.833 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-04 13:21:14.834 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-04 13:21:14.834 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-04 13:21:14.848 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-04 13:21:14.849 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-04 13:21:14.849 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-04 13:21:14.849 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-04 13:21:14.850 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-04 13:21:14.880 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-04 13:21:14.882 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-04 13:21:14.882 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-04 13:21:14.882 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-04 13:21:14.883 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-04 13:21:14.883 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-04 13:21:14.884 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-04 13:21:14.886 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-04 13:21:14.888 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-04 13:21:14.890 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 13:21:14.890 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-04 13:21:14.899 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 13:21:14.913 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-04 13:21:14.914 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-04 13:21:14.915 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-04 13:21:14.916 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:21:14.916 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-04 13:21:14.916 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:21:14.917 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-04 13:21:14.918 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:21:14.918 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-04 13:21:14.919 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-04 13:21:14.919 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-04 13:21:14.920 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-04 13:21:14.921 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-04 13:21:14.921 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-04 13:21:14.921 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-04 13:21:14.923 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-04 13:21:14.924 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-04 13:21:14.924 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:21:14.925 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-04 13:21:14.925 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-04 13:21:14.926 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-04 13:21:14.927 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-04 13:21:14.927 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-04 13:21:14.928 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-04 13:21:14.928 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-04 13:21:14.928 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-04 13:21:14.929 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-04 13:21:14.929 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-04 13:21:14.931 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-04 13:21:14.932 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-04 13:21:14.933 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-04 13:21:14.934 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-04 13:21:14.934 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-04 13:21:14.934 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:21:14.934 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-04 13:21:14.935 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-04 13:21:14.936 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-04 13:21:14.937 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-04 13:21:14.939 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-04 13:21:14.939 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-04 13:21:14.943 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-04 13:21:14.943 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-04 13:21:14.944 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:21:14.944 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:21:14.946 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-04 13:21:14.947 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.947 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.948 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-04 13:21:14.948 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.949 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.949 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-04 13:21:14.950 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.951 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-04 13:21:14.952 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.952 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-04 13:21:14.953 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.953 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.954 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-04 13:21:14.954 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.955 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.955 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-04 13:21:14.956 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:21:14.957 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:21:14.957 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.958 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.958 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-04 13:21:14.959 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:21:14.960 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.960 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-04 13:21:14.961 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.961 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.962 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-04 13:21:14.963 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:21:14.963 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:21:14.964 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-04 13:21:14.964 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:21:14.965 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:21:14.966 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:21:14.968 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-04 13:21:14.968 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:21:14.969 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-04 13:21:14.969 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-04 13:21:14.970 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-04 13:21:14.970 [Information] VocomDriver: Initializing Vocom driver
2025-07-04 13:21:14.971 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-04 13:21:14.973 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-04 13:21:14.974 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:21:14.974 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:21:14.975 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:21:14.975 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-04 13:21:14.977 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from msvcr120.dll
2025-07-04 13:21:14.978 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from msvcp120.dll
2025-07-04 13:21:14.979 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-04 13:21:14.980 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-04 13:21:14.980 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-04 13:21:14.980 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:21:14.981 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-04 13:21:14.982 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-04 13:21:14.983 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-04 13:21:14.983 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-04 13:21:14.984 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-04 13:21:14.984 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-04 13:21:14.985 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-04 13:21:14.986 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-04 13:21:14.986 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-04 13:21:14.986 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-04 13:21:14.986 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-04 13:21:14.987 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-04 13:21:14.987 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-04 13:21:14.987 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-04 13:21:14.987 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-04 13:21:14.988 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-04 13:21:14.988 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-04 13:21:14.988 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-04 13:21:14.988 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-04 13:21:14.989 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-04 13:21:14.989 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-04 13:21:14.989 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-04 13:21:14.989 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-04 13:21:14.990 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-04 13:21:14.990 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-04 13:21:14.990 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-04 13:21:14.990 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-04 13:21:14.990 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-04 13:21:14.991 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-04 13:21:14.991 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-04 13:21:14.991 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-04 13:21:14.991 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-04 13:21:14.991 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-04 13:21:14.992 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-04 13:21:14.992 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-04 13:21:14.992 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-04 13:21:14.992 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-04 13:21:14.992 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-04 13:21:14.993 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-04 13:21:14.993 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-04 13:21:14.993 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-04 13:21:14.993 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-04 13:21:14.993 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-04 13:21:14.994 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-04 13:21:14.994 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-04 13:21:14.994 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-04 13:21:14.994 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-04 13:21:14.995 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-04 13:21:14.995 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-04 13:21:14.995 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-04 13:21:14.996 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-04 13:21:14.997 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-04 13:21:14.997 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-04 13:21:14.997 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-04 13:21:14.997 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-04 13:21:14.998 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-04 13:21:14.998 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-04 13:21:15.000 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-04 13:21:15.000 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-04 13:21:15.002 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-04 13:21:15.003 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-04 13:21:15.040 [Information] WiFiCommunicationService: WiFi is available
2025-07-04 13:21:15.041 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-04 13:21:15.042 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-04 13:21:15.043 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-04 13:21:15.044 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-04 13:21:15.044 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-04 13:21:15.045 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:21:15.046 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:21:15.047 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:21:15.048 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:21:15.053 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:21:15.053 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:21:15.054 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:21:15.054 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:21:15.055 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:21:15.055 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:21:15.057 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:15.066 [Information] VocomService: PTT application is not running
2025-07-04 13:21:15.067 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:21:15.068 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:21:15.069 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:21:15.069 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-04 13:21:15.070 [Information] App: Initializing Vocom service
2025-07-04 13:21:15.070 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:21:15.070 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:21:15.071 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:21:15.071 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:21:15.072 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:21:15.072 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:21:15.072 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:21:15.073 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:21:15.073 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:21:15.073 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:21:15.073 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:15.083 [Information] VocomService: PTT application is not running
2025-07-04 13:21:15.084 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:21:15.085 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:21:15.085 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:21:15.088 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:21:15.089 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:21:15.090 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:21:15.091 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:21:15.379 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:21:15.379 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:21:15.380 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:21:15.381 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:21:15.383 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:21:15.485 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:21:15.486 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:21:15.486 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:21:15.487 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-04 13:21:15.490 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:21:15.490 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:15.500 [Information] VocomService: PTT application is not running
2025-07-04 13:21:15.503 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:21:15.503 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.504 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:15.512 [Information] VocomService: PTT application is not running
2025-07-04 13:21:15.513 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.514 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.515 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:21:15.515 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:21:15.515 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.539 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:21:15.540 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.540 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:21:15.542 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:21:15.542 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:21:15.543 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:21:15.543 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:21:15.543 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:21:15.543 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:15.543 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-04 13:21:15.545 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-04 13:21:15.547 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:15.547 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-04 13:21:15.549 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:21:15.551 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:21:15.551 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:21:15.553 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:21:15.554 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:21:15.556 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:21:15.557 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:21:15.559 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:21:15.563 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:21:15.565 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:21:15.565 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:15.567 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:21:15.567 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:21:15.567 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:15.569 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:21:15.569 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:21:15.569 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:15.570 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:21:15.571 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:21:15.571 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:15.572 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:21:15.572 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:21:15.572 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:15.572 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:21:15.574 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:21:15.575 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:15.575 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:21:15.575 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:21:15.576 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:15.576 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:21:15.576 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:21:15.577 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:15.577 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:21:15.577 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:21:15.577 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:15.578 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:21:15.578 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:21:15.578 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:21:15.580 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:21:15.580 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:21:15.580 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:21:15.580 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:21:15.581 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:21:15.777 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:21:15.777 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:21:15.777 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:21:15.778 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:21:15.778 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:21:15.879 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:21:15.879 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:21:15.880 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:21:15.880 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:21:15.880 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:21:15.881 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:15.890 [Information] VocomService: PTT application is not running
2025-07-04 13:21:15.890 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:21:15.890 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.890 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:15.900 [Information] VocomService: PTT application is not running
2025-07-04 13:21:15.900 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.900 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.901 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:21:15.901 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:21:15.901 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.901 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:21:15.902 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:15.902 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:21:16.612 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:21:16.613 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:21:16.613 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:21:16.613 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:21:16.614 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:21:16.614 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:16.614 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:16.615 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:16.615 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:16.615 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:21:16.615 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:21:16.616 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:21:16.617 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-04 13:21:17.617 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-04 13:21:17.618 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:21:17.619 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:21:17.619 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:21:17.619 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:21:17.620 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:21:17.621 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:21:17.621 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:21:17.622 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:21:17.622 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:21:17.622 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:21:17.622 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:17.623 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:21:17.623 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:21:17.623 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:17.624 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:21:17.624 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:21:17.624 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:17.624 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:21:17.624 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:21:17.625 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:17.625 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:21:17.625 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:21:17.625 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:17.625 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:21:17.626 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:21:17.626 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:17.626 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:21:17.627 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:21:17.627 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:17.627 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:21:17.627 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:21:17.628 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:17.628 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:21:17.628 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:21:17.628 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:17.628 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:21:17.629 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:21:17.629 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:21:17.629 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:21:17.629 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:21:17.630 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:21:17.630 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:21:17.630 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:21:17.800 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:21:17.801 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:21:17.801 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:21:17.801 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:21:17.801 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:21:17.902 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:21:17.903 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:21:17.903 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:21:17.903 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:21:17.904 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:21:17.904 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:17.916 [Information] VocomService: PTT application is not running
2025-07-04 13:21:17.916 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:21:17.916 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:17.916 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:17.929 [Information] VocomService: PTT application is not running
2025-07-04 13:21:17.929 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:17.930 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:17.930 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:21:17.930 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:21:17.930 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:17.931 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:21:17.931 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:17.931 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:21:17.931 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:21:17.932 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:21:17.932 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:21:17.932 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:21:17.932 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:21:17.932 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:17.933 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:17.933 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:17.933 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:17.933 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:17.933 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:17.934 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:21:17.934 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:21:17.934 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:21:17.934 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:21:17.934 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-04 13:21:19.935 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-04 13:21:19.935 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:21:19.936 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:21:19.936 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:21:19.936 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:21:19.936 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:21:19.937 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:21:19.937 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:21:19.938 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:21:19.938 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:21:19.938 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:21:19.939 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:19.939 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:21:19.939 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:21:19.939 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:19.940 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:21:19.940 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:21:19.940 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:19.940 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:21:19.941 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:21:19.941 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:19.941 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:21:19.941 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:21:19.941 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:21:19.942 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:21:19.942 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:21:19.942 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:19.942 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:21:19.942 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:21:19.942 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:19.943 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:21:19.943 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:21:19.943 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:19.943 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:21:19.943 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:21:19.944 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:21:19.944 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:21:19.944 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:21:19.944 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:21:19.944 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:21:19.945 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:21:19.945 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:21:19.945 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:21:19.945 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:21:20.113 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:21:20.113 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:21:20.114 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:21:20.114 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:21:20.114 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:21:20.215 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:21:20.215 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:21:20.216 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:21:20.216 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:21:20.216 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:21:20.216 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:20.226 [Information] VocomService: PTT application is not running
2025-07-04 13:21:20.226 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:21:20.226 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:20.227 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:20.236 [Information] VocomService: PTT application is not running
2025-07-04 13:21:20.237 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:20.237 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:20.237 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:21:20.237 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:21:20.238 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:20.238 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:21:20.238 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:20.238 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:21:20.238 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:21:20.239 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:21:20.239 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:21:20.239 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:21:20.239 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:21:20.239 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:20.240 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:20.240 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:20.240 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:20.240 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:20.241 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:20.241 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:20.241 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:20.241 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:21:20.241 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:21:20.242 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:21:20.242 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:21:20.242 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:21:20.242 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-04 13:21:23.242 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-04 13:21:23.243 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-04 13:21:23.243 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-04 13:21:23.244 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-04 13:21:23.745 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-04 13:21:23.745 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-04 13:21:23.746 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-04 13:21:23.746 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-04 13:21:23.748 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-04 13:21:23.749 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-04 13:21:23.751 [Information] BackupService: Initializing backup service
2025-07-04 13:21:23.751 [Information] BackupService: Backup service initialized successfully
2025-07-04 13:21:23.751 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-04 13:21:23.752 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-04 13:21:23.753 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-04 13:21:23.774 [Information] BackupService: Compressing backup data
2025-07-04 13:21:23.778 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (447 bytes)
2025-07-04 13:21:23.779 [Information] BackupServiceFactory: Created template for category: Production
2025-07-04 13:21:23.779 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-04 13:21:23.780 [Information] BackupService: Compressing backup data
2025-07-04 13:21:23.780 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (449 bytes)
2025-07-04 13:21:23.781 [Information] BackupServiceFactory: Created template for category: Development
2025-07-04 13:21:23.781 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-04 13:21:23.781 [Information] BackupService: Compressing backup data
2025-07-04 13:21:23.782 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (446 bytes)
2025-07-04 13:21:23.782 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-04 13:21:23.782 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-04 13:21:23.783 [Information] BackupService: Compressing backup data
2025-07-04 13:21:23.783 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (449 bytes)
2025-07-04 13:21:23.783 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-04 13:21:23.784 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-04 13:21:23.784 [Information] BackupService: Compressing backup data
2025-07-04 13:21:23.785 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (447 bytes)
2025-07-04 13:21:23.786 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-04 13:21:23.786 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-04 13:21:23.786 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-04 13:21:23.787 [Information] BackupService: Compressing backup data
2025-07-04 13:21:23.788 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (513 bytes)
2025-07-04 13:21:23.788 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-04 13:21:23.788 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-04 13:21:23.789 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-04 13:21:23.792 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 13:21:23.794 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 13:21:23.836 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-04 13:21:23.837 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 13:21:23.838 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-04 13:21:23.838 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-04 13:21:23.838 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-04 13:21:23.839 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-04 13:21:23.839 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-04 13:21:23.842 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-04 13:21:23.842 [Information] App: Flash operation monitor service initialized successfully
2025-07-04 13:21:23.847 [Information] LicensingService: Initializing licensing service
2025-07-04 13:21:23.879 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-04 13:21:23.881 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-04 13:21:23.882 [Information] App: Licensing service initialized successfully
2025-07-04 13:21:23.882 [Information] App: License status: Trial
2025-07-04 13:21:23.882 [Information] App: Trial period: 30 days remaining
2025-07-04 13:21:23.883 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-04 13:21:23.899 [Debug] AppConfigurationService: Configuration key 'Vocom.UseWiFiFallback' not found, returning default value
2025-07-04 13:21:24.030 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:21:24.031 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:21:24.031 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:21:24.031 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:21:24.032 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:21:24.032 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:21:24.032 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:21:24.033 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:21:24.033 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:21:24.033 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:21:24.033 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:24.042 [Information] VocomService: PTT application is not running
2025-07-04 13:21:24.043 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:21:24.043 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:21:24.044 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:21:24.094 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-04 13:21:24.594 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-04 13:21:24.645 [Information] BackupService: Initializing backup service
2025-07-04 13:21:24.645 [Information] BackupService: Backup service initialized successfully
2025-07-04 13:21:24.695 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 13:21:24.695 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 13:21:24.696 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-04 13:21:24.696 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 13:21:24.748 [Information] BackupService: Getting predefined backup categories
2025-07-04 13:21:24.800 [Information] MainViewModel: Services initialized successfully
2025-07-04 13:21:24.803 [Information] MainViewModel: Scanning for Vocom devices
2025-07-04 13:21:24.804 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:21:24.805 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:21:24.805 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:21:24.805 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:21:24.961 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:21:24.961 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:21:24.962 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:21:24.962 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:21:24.962 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:21:25.068 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:21:25.068 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:21:25.068 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:21:25.069 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-04 13:21:34.688 [Information] MainViewModel: Connecting to Vocom device 
2025-07-04 13:21:34.689 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:21:34.689 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:34.697 [Information] VocomService: PTT application is not running
2025-07-04 13:21:34.697 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:21:34.697 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:34.698 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:21:34.706 [Information] VocomService: PTT application is not running
2025-07-04 13:21:34.706 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:34.706 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:34.707 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:21:34.707 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:21:34.707 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:34.707 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:21:34.707 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:21:34.708 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:21:34.708 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:21:34.708 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:21:34.709 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:21:34.709 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:21:34.709 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:21:34.709 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:34.710 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:34.710 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:34.710 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 
2025-07-04 13:21:34.711 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:21:34.711 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:34.711 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:34.711 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:34.711 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:21:34.712 [Error] MainViewModel: Failed to connect to Vocom device 
