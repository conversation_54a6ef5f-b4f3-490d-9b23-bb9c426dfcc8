{"microcontroller": {"name": "MC9S12XEP100", "family": "S12X", "architecture": "16-bit", "endianness": "big", "clockSpeed": "40MHz", "flashSize": "1024KB", "ramSize": "64KB", "eepromSize": "4KB"}, "memory": {"flash": {"baseAddress": "0x400000", "size": "0x100000", "pageSize": "0x400", "sectorSize": "0x1000", "writeBlockSize": "0x200", "eraseBlockSize": "0x1000"}, "ram": {"baseAddress": "0x001000", "size": "0x010000"}, "eeprom": {"baseAddress": "0x100000", "size": "0x001000", "pageSize": "0x4", "writeBlockSize": "0x4"}, "registers": {"baseAddress": "0x000000", "size": "0x001000"}}, "communication": {"protocols": ["CAN", "SCI", "SPI", "IIC"], "can": {"controllers": 5, "baseAddresses": ["0x0140", "0x0180", "0x01C0", "0x0200", "0x0280"], "baudRates": [125000, 250000, 500000, 1000000], "defaultBaudRate": 500000}, "sci": {"controllers": 2, "baseAddresses": ["0x00C8", "0x00D0"], "baudRates": [9600, 19200, 38400, 57600, 115200], "defaultBaudRate": 115200}, "spi": {"controllers": 3, "baseAddresses": ["0x00D8", "0x00F0", "0x00F8"], "clockRates": [1000000, 2000000, 4000000, 8000000], "defaultClockRate": 4000000}, "iic": {"controllers": 1, "baseAddresses": ["0x00E0"], "clockRates": [100000, 400000], "defaultClockRate": 400000}}, "security": {"backdoorKeys": {"enabled": true, "keySize": 8, "defaultKey": "FFFFFFFFFFFFFFFF"}, "securityAccess": {"enabled": true, "levels": [1, 3, 5, 7], "seedKeyAlgorithm": "proprietary"}, "flashProtection": {"enabled": true, "protectedSectors": []}}, "diagnostics": {"dtcSupport": true, "freezeFrameSupport": true, "liveDataSupport": true, "ecuReset": {"hardReset": true, "softReset": true, "keyOffOnReset": true}}, "timing": {"flashWrite": {"timeoutMs": 5000, "retryCount": 3, "delayBetweenRetriesMs": 100}, "flashErase": {"timeoutMs": 10000, "retryCount": 3, "delayBetweenRetriesMs": 500}, "communication": {"responseTimeoutMs": 1000, "retryCount": 3, "delayBetweenRetriesMs": 50}}, "features": {"xgate": {"enabled": true, "coreCount": 1, "sharedMemory": true}, "pim": {"enabled": true, "portCount": 16}, "adc": {"enabled": true, "channels": 16, "resolution": 12}, "timer": {"enabled": true, "channels": 8, "resolution": 16}, "pwm": {"enabled": true, "channels": 8, "resolution": 16}}}