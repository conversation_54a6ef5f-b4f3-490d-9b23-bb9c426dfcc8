#!/usr/bin/env pwsh

Write-Host "========================================"
Write-Host "VolvoFlashWR Real Hardware Mode"
Write-Host "========================================"
Write-Host ""
Write-Host "Starting application for real Vocom hardware testing..."
Write-Host ""

# Change to application directory
$scriptPath = Split-Path -Parent $MyInvocation.MyCommand.Path
$appPath = Join-Path $scriptPath "..\Application"
Set-Location $appPath

# Set environment variables for real hardware mode
$env:PHOENIX_VOCOM_ENABLED = "true"
$env:USE_PATCHED_IMPLEMENTATION = "true"
$env:VOCOM_DRIVER_MODE = "REAL_HARDWARE"
$env:VERBOSE_LOGGING = "true"
$env:USE_DUMMY_IMPLEMENTATIONS = "false"
$env:APCI_LIBRARY_PATH = "$PWD\Libraries"
$env:USE_LOCAL_LIBRARIES = "true"

# Add libraries to PATH
$env:PATH = "$PWD\Libraries;$env:PATH"

Write-Host "Environment variables set for real hardware mode:"
Write-Host "PHOENIX_VOCOM_ENABLED=$env:PHOENIX_VOCOM_ENABLED"
Write-Host "USE_PATCHED_IMPLEMENTATION=$env:USE_PATCHED_IMPLEMENTATION"
Write-Host "VOCOM_DRIVER_MODE=$env:VOCOM_DRIVER_MODE"
Write-Host "VERBOSE_LOGGING=$env:VERBOSE_LOGGING"
Write-Host "USE_DUMMY_IMPLEMENTATIONS=$env:USE_DUMMY_IMPLEMENTATIONS"
Write-Host ""

# Check for critical libraries
Write-Host "Checking for critical libraries..."
if (Test-Path "Libraries\WUDFPuma.dll") {
    Write-Host "✓ WUDFPuma.dll found"
} else {
    Write-Host "✗ WUDFPuma.dll missing - CRITICAL"
}

if (Test-Path "Libraries\apci.dll") {
    Write-Host "✓ apci.dll found"
} else {
    Write-Host "✗ apci.dll missing - CRITICAL"
}

if (Test-Path "Libraries\Volvo.ApciPlus.dll") {
    Write-Host "✓ Volvo.ApciPlus.dll found"
} else {
    Write-Host "✗ Volvo.ApciPlus.dll missing - CRITICAL"
}

if (Test-Path "Libraries\Volvo.ApciPlusData.dll") {
    Write-Host "✓ Volvo.ApciPlusData.dll found"
} else {
    Write-Host "✗ Volvo.ApciPlusData.dll missing - CRITICAL"
}

Write-Host ""
Write-Host "IMPORTANT NOTES:"
Write-Host "- The application requires a REAL Vocom 1 adapter physically connected"
Write-Host "- Ensure Vocom drivers are installed on this system"
Write-Host "- The application will fall back to simulation mode if no real hardware is detected"
Write-Host "- Check the log files in the Logs folder for detailed diagnostics"
Write-Host ""

# Start the application
if (Test-Path "VolvoFlashWR.Launcher.exe") {
    Write-Host "Starting VolvoFlashWR for real hardware..."
    Start-Process "VolvoFlashWR.Launcher.exe" -ArgumentList "--mode=normal", "--hardware=real"
    Write-Host "Application started successfully"
    Write-Host "Check the application window for connection status."
    Write-Host ""
    Write-Host "If the application shows 'Dummy mode' or simulation responses:"
    Write-Host "1. Ensure a real Vocom 1 adapter is connected via USB"
    Write-Host "2. Verify Vocom drivers are properly installed"
    Write-Host "3. Check that no other applications are using the Vocom adapter"
    Write-Host "4. Review the log files for detailed error information"
} else {
    Write-Host "ERROR: VolvoFlashWR.Launcher.exe not found"
    Write-Host "Please ensure the export was completed successfully."
    Read-Host "Press Enter to exit"
}

Write-Host ""
Write-Host "Press any key to exit..."
Read-Host
