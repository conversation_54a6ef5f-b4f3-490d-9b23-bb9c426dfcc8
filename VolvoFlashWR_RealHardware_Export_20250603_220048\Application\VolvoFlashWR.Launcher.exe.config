<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- Force loading of local libraries -->
      <probing privatePath="Libraries;Libraries\System" />
      
      <!-- Redirect to local Visual C++ runtime libraries -->
      <dependentAssembly>
        <assemblyIdentity name="msvcr120" />
        <codeBase href="Libraries\msvcr120.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="msvcp120" />
        <codeBase href="Libraries\msvcp120.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="msvcr140" />
        <codeBase href="Libraries\msvcr140.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="msvcp140" />
        <codeBase href="Libraries\msvcp140.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="vcruntime140" />
        <codeBase href="Libraries\vcruntime140.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="vcruntime140_1" />
        <codeBase href="Libraries\vcruntime140_1.dll" />
      </dependentAssembly>
      
      <!-- Vocom driver libraries -->
      <dependentAssembly>
        <assemblyIdentity name="WUDFPuma" />
        <codeBase href="Libraries\WUDFPuma.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="apci" />
        <codeBase href="Libraries\apci.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Volvo.ApciPlus" />
        <codeBase href="Libraries\Volvo.ApciPlus.dll" />
      </dependentAssembly>
      
      <dependentAssembly>
        <assemblyIdentity name="Volvo.ApciPlusData" />
        <codeBase href="Libraries\Volvo.ApciPlusData.dll" />
      </dependentAssembly>
    </assemblyBinding>
    
    <!-- Enable loading of mixed-mode assemblies -->
    <loadFromRemoteSources enabled="true" />
    <useLegacyV2RuntimeActivationPolicy enabled="true" />
  </runtime>
  
  <appSettings>
    <!-- Force use of local libraries -->
    <add key="UseLocalLibraries" value="true" />
    <add key="LibrariesPath" value="Libraries" />
    
    <!-- Vocom configuration -->
    <add key="VocomDriverPath" value="Libraries\WUDFPuma.dll" />
    <add key="ApciLibraryPath" value="Libraries\apci.dll" />
    
    <!-- Enable verbose logging for debugging -->
    <add key="VerboseLogging" value="true" />
  </appSettings>
</configuration>
