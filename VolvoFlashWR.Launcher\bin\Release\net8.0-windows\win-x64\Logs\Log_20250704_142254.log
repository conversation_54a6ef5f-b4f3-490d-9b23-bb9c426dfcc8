Log started at 7/4/2025 2:22:54 PM
2025-07-04 14:22:55.009 [Information] LoggingService: Logging service initialized
2025-07-04 14:22:55.024 [Information] App: Starting integrated application initialization
2025-07-04 14:22:55.026 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-04 14:22:55.029 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-04 14:22:55.031 [Information] IntegratedStartupService: Setting up application environment
2025-07-04 14:22:55.032 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries
2025-07-04 14:22:55.033 [Information] IntegratedStartupService: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Temp
2025-07-04 14:22:55.034 [Information] IntegratedStartupService: Application environment setup completed
2025-07-04 14:22:55.035 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-04 14:22:55.037 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-04 14:22:55.038 [Information] VCRedistBundler: Created VCRedist directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\VCRedist
2025-07-04 14:22:55.042 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-04 14:22:55.055 [Information] VCRedistBundler: Copied msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-04 14:22:55.061 [Information] VCRedistBundler: Copied msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-04 14:22:55.086 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-04 14:22:55.091 [Information] VCRedistBundler: Copied msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-04 14:22:55.096 [Information] VCRedistBundler: Copied vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-04 14:22:55.104 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:22:55.114 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 14:22:55.120 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 14:22:55.122 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-04 14:22:55.124 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-04 14:22:55.125 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-04 14:22:55.375 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-04 14:22:55.472 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-04 14:22:55.473 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-04 14:22:55.565 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-04 14:22:55.567 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:22:55.567 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 14:22:55.567 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 14:22:55.573 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-04 14:22:55.574 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-04 14:22:55.574 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-04 14:22:55.580 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-04 14:22:55.581 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-04 14:22:55.582 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-04 14:22:55.584 [Information] LibraryExtractor: Starting library extraction process
2025-07-04 14:22:55.585 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\System
2025-07-04 14:22:55.586 [Information] LibraryExtractor: Created directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\Backup
2025-07-04 14:22:55.588 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-04 14:22:55.590 [Information] LibraryExtractor: Copying system libraries
2025-07-04 14:22:55.597 [Information] LibraryExtractor: Copied system library: WUDFPuma.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 14:22:55.603 [Information] LibraryExtractor: Copied system library: apci.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\apci.dll
2025-07-04 14:22:55.613 [Information] LibraryExtractor: Copied system library: Volvo.ApciPlus.dll from C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021\Volvo.ApciPlus.dll
2025-07-04 14:22:55.617 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-04 14:22:55.625 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-04 14:23:29.103 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-04 14:24:14.753 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-04 14:25:31.076 [Information] LibraryExtractor: Verifying library extraction
2025-07-04 14:25:31.077 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-04 14:25:31.077 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-04 14:25:31.078 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-04 14:25:31.078 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-04 14:25:31.078 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-04 14:25:31.095 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-04 14:25:31.099 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-04 14:25:31.101 [Information] DependencyManager: Initializing dependency manager
2025-07-04 14:25:31.102 [Information] DependencyManager: Setting up library search paths
2025-07-04 14:25:31.103 [Information] DependencyManager: Added library path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries
2025-07-04 14:25:31.103 [Information] DependencyManager: Added driver path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-07-04 14:25:31.104 [Information] DependencyManager: Added application path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64
2025-07-04 14:25:31.104 [Information] DependencyManager: Updated PATH environment variable
2025-07-04 14:25:31.106 [Information] DependencyManager: Verifying required directories
2025-07-04 14:25:31.106 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries
2025-07-04 14:25:31.106 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-07-04 14:25:31.107 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\System
2025-07-04 14:25:31.107 [Information] DependencyManager: ✓ Directory exists: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config
2025-07-04 14:25:31.109 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-04 14:25:31.117 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-04 14:25:31.122 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-04 14:25:31.126 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-04 14:25:31.129 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 14:25:31.131 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 14:25:31.149 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:25:31.149 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 14:25:31.150 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 14:25:31.152 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-04 14:25:31.153 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\WUDFPuma.dll (x64)
2025-07-04 14:25:31.346 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\apci.dll
2025-07-04 14:25:31.689 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-07-04 14:25:31.691 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-04 14:25:31.973 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apcidb.dll
2025-07-04 14:25:31.977 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-04 14:25:32.494 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlus.dll
2025-07-04 14:25:33.768 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlus.dll
2025-07-04 14:25:33.772 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-04 14:25:34.274 [Warning] DependencyManager: Architecture incompatible library skipped: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Volvo.ApciPlusData.dll
2025-07-04 14:25:34.281 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-04 14:25:34.356 [Warning] DependencyManager: Critical library not found: PhoenixGeneral.dll
2025-07-04 14:25:34.358 [Information] DependencyManager: ✓ Loaded Critical library: msvcr120.dll from C:\Windows\system32\msvcr120.dll (x64)
2025-07-04 14:25:34.360 [Information] DependencyManager: ✓ Loaded Critical library: msvcp120.dll from C:\Windows\system32\msvcp120.dll (x64)
2025-07-04 14:25:34.361 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-04 14:25:34.362 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 14:25:34.363 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 14:25:34.364 [Information] DependencyManager: Setting up environment variables
2025-07-04 14:25:34.365 [Information] DependencyManager: Environment variables configured
2025-07-04 14:25:34.367 [Information] DependencyManager: Verifying library loading status
2025-07-04 14:25:35.001 [Information] DependencyManager: Library loading verification: 5/11 (45.5%) critical libraries loaded
2025-07-04 14:25:35.016 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-04 14:25:35.016 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-04 14:25:35.020 [Information] IntegratedStartupService: Dependency status: 5 found, 6 missing
2025-07-04 14:25:35.022 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-04 14:25:35.026 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-04 14:25:35.028 [Information] IntegratedStartupService: Verifying system readiness
2025-07-04 14:25:35.029 [Information] IntegratedStartupService: System readiness verification passed
2025-07-04 14:25:35.029 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-04 14:25:35.031 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-04 14:25:35.044 [Information] IntegratedStartupService: Application Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64
2025-07-04 14:25:35.046 [Information] IntegratedStartupService: Libraries Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries
2025-07-04 14:25:35.050 [Information] IntegratedStartupService: Drivers Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom
2025-07-04 14:25:35.053 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-04 14:25:35.054 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-04 14:25:35.054 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-04 14:25:35.054 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries
2025-07-04 14:25:35.055 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-04 14:25:35.055 [Information] App: Integrated startup completed successfully
2025-07-04 14:25:35.059 [Information] App: System Status - Libraries: 3 available, Dependencies: 5 loaded
2025-07-04 14:25:35.414 [Information] App: Initializing application services
2025-07-04 14:25:35.431 [Information] AppConfigurationService: Initializing configuration service
2025-07-04 14:25:35.439 [Information] AppConfigurationService: Created configuration directory: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config
2025-07-04 14:25:35.628 [Information] AppConfigurationService: Configuration loaded from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-07-04 14:25:35.629 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-04 14:25:35.630 [Information] App: Configuration service initialized successfully
2025-07-04 14:25:35.632 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-04 14:25:35.632 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-04 14:25:35.641 [Information] App: Environment variable exists: True, not 'false': False
2025-07-04 14:25:35.642 [Information] App: Final useDummyImplementations value: False
2025-07-04 14:25:35.642 [Information] App: Updating config to NOT use dummy implementations
2025-07-04 14:25:35.676 [Information] AppConfigurationService: Configuration saved to D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Config\app_config.json
2025-07-04 14:25:35.677 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-04 14:25:35.677 [Information] App: usePatchedImplementation flag is: True
2025-07-04 14:25:35.678 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-04 14:25:35.678 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries'
2025-07-04 14:25:35.678 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-04 14:25:35.678 [Information] App: verboseLogging flag is: True
2025-07-04 14:25:35.681 [Information] App: Verifying real hardware requirements...
2025-07-04 14:25:35.681 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-04 14:25:35.689 [Information] App: ✓ Found critical library: apci.dll
2025-07-04 14:25:35.689 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-04 14:25:35.694 [Warning] App: ✗ Missing critical library: Volvo.ApciPlusData.dll at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\Volvo.ApciPlusData.dll
2025-07-04 14:25:35.697 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 14:25:35.700 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-04 14:25:35.702 [Information] App: ✓ Found Vocom driver config: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Vocom\config.json
2025-07-04 14:25:35.702 [Warning] App: ⚠ Some real hardware requirements are missing - application may fall back to dummy mode
2025-07-04 14:25:35.737 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-04 14:25:35.739 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-04 14:25:35.740 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-04 14:25:35.741 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-04 14:25:35.742 [Information] PatchedVocomServiceFactory: Assembly location: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\VolvoFlashWR.Communication.dll
2025-07-04 14:25:35.788 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-04 14:25:35.792 [Information] PatchedVocomServiceFactory: Created marker file at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\patched_factory_created.txt
2025-07-04 14:25:35.793 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-04 14:25:35.793 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-04 14:25:35.793 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-04 14:25:35.849 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-04 14:25:35.854 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-04 14:25:35.854 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-04 14:25:35.855 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-04 14:25:35.855 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-04 14:25:35.855 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-04 14:25:35.857 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-04 14:25:35.861 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-04 14:25:35.882 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-04 14:25:35.883 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 14:25:35.884 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-04 14:25:35.910 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 14:25:35.958 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-04 14:25:35.959 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-04 14:25:35.959 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-04 14:25:35.961 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 14:25:35.961 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll has incompatible architecture
2025-07-04 14:25:35.961 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 14:25:35.962 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries\apci.dll has incompatible architecture
2025-07-04 14:25:36.085 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 14:25:36.087 [Warning] PhoenixVocomAdapter: APCI library at D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-04 14:25:36.088 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-04 14:25:36.088 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-04 14:25:36.089 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-04 14:25:36.090 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-04 14:25:36.090 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-04 14:25:36.091 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 4:58:52 AM
2025-07-04 14:25:36.093 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-04 14:25:36.094 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-04 14:25:36.095 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 14:25:36.095 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-04 14:25:36.096 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-04 14:25:36.097 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-04 14:25:36.098 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-04 14:25:36.099 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-04 14:25:36.102 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-04 14:25:36.102 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-04 14:25:36.103 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-04 14:25:36.103 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-04 14:25:36.104 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-04 14:25:36.104 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-04 14:25:36.104 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-04 14:25:36.106 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-04 14:25:36.107 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-04 14:25:36.107 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-04 14:25:36.107 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:25:36.108 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-04 14:25:36.109 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-04 14:25:36.109 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-04 14:25:36.111 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-04 14:25:36.113 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-04 14:25:36.114 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-04 14:25:36.520 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-04 14:25:36.521 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-04 14:25:36.521 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-07-04 14:25:36.522 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-07-04 14:25:36.525 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-04 14:25:36.526 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:36.527 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Libraries, error: 0
2025-07-04 14:25:36.528 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-04 14:25:36.528 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:36.529 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-04 14:25:36.529 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-04 14:25:36.529 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-04 14:25:36.530 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:36.530 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-04 14:25:36.718 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:36.719 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-04 14:25:36.862 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-07-04 14:25:37.092 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-07-04 14:25:37.242 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:37.243 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-04 14:25:37.243 [Warning] VocomNativeInterop_Patch: Dependency PhoenixGeneral.dll not found in any search path
2025-07-04 14:25:37.243 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-04 14:25:37.359 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:37.360 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-04 14:25:37.562 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\, error: 0
2025-07-04 14:25:37.563 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-04 14:25:37.750 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-07-04 14:25:37.942 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-07-04 14:25:38.009 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\
2025-07-04 14:25:38.013 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-04 14:25:38.015 [Error] VocomNativeInterop_Patch: DLL Path: D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\apci.dll
2025-07-04 14:25:38.015 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 4:58:52 AM
2025-07-04 14:25:38.019 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-04 14:25:38.019 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-04 14:25:38.021 [Information] VocomDriver: Initializing Vocom driver
2025-07-04 14:25:38.023 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-04 14:25:38.027 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-04 14:25:38.028 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 14:25:38.028 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 14:25:38.029 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 14:25:38.030 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-04 14:25:38.032 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from C:\Windows\system32\msvcr120.dll
2025-07-04 14:25:38.035 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from C:\Windows\system32\msvcp120.dll
2025-07-04 14:25:38.038 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-04 14:25:38.038 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-04 14:25:38.039 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-04 14:25:38.039 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:25:38.044 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-04 14:25:38.047 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-04 14:25:38.050 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-04 14:25:38.052 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-04 14:25:38.052 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-04 14:25:38.053 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-04 14:25:38.054 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-04 14:25:38.055 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-04 14:25:38.055 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-04 14:25:38.055 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-04 14:25:38.056 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-04 14:25:38.056 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-04 14:25:38.056 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-04 14:25:38.056 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-04 14:25:38.056 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-04 14:25:38.057 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-04 14:25:38.057 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-04 14:25:38.057 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-04 14:25:38.057 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-04 14:25:38.057 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-04 14:25:38.058 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-04 14:25:38.058 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-04 14:25:38.058 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-04 14:25:38.058 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-04 14:25:38.058 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-04 14:25:38.059 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-04 14:25:38.059 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-04 14:25:38.059 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-04 14:25:38.059 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-04 14:25:38.060 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-04 14:25:38.060 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-04 14:25:38.060 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-04 14:25:38.060 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-04 14:25:38.061 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-04 14:25:38.061 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-04 14:25:38.061 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-04 14:25:38.061 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-04 14:25:38.062 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-04 14:25:38.062 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-04 14:25:38.062 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-04 14:25:38.062 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-04 14:25:38.063 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-04 14:25:38.063 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-04 14:25:38.063 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-04 14:25:38.063 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-04 14:25:38.064 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-04 14:25:38.064 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-04 14:25:38.064 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-04 14:25:38.064 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-04 14:25:38.066 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-04 14:25:38.067 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-04 14:25:38.069 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-04 14:25:38.069 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-04 14:25:38.069 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-04 14:25:38.070 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-04 14:25:38.071 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-04 14:25:38.071 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-04 14:25:38.075 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-04 14:25:38.076 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-04 14:25:38.078 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-04 14:25:38.080 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-04 14:25:38.172 [Information] WiFiCommunicationService: WiFi is available
2025-07-04 14:25:38.173 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-04 14:25:38.176 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-04 14:25:38.177 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-04 14:25:38.179 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-04 14:25:38.181 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-04 14:25:38.184 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 14:25:38.186 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 14:25:38.188 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 14:25:38.190 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 14:25:38.195 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 14:25:38.196 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 14:25:38.196 [Information] VocomService: Native USB communication service initialized
2025-07-04 14:25:38.196 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 14:25:38.197 [Information] VocomService: Connection recovery service initialized
2025-07-04 14:25:38.197 [Information] VocomService: Enhanced services initialization completed
2025-07-04 14:25:38.202 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:38.221 [Information] VocomService: PTT application is not running
2025-07-04 14:25:38.224 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 14:25:38.225 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-04 14:25:38.225 [Information] App: Initializing Vocom service
2025-07-04 14:25:38.225 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 14:25:38.226 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 14:25:38.226 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 14:25:38.226 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 14:25:38.227 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 14:25:38.227 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 14:25:38.227 [Information] VocomService: Native USB communication service initialized
2025-07-04 14:25:38.228 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 14:25:38.228 [Information] VocomService: Connection recovery service initialized
2025-07-04 14:25:38.228 [Information] VocomService: Enhanced services initialization completed
2025-07-04 14:25:38.228 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:38.246 [Information] VocomService: PTT application is not running
2025-07-04 14:25:38.247 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 14:25:38.251 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 14:25:38.252 [Information] VocomService: Using new enhanced device detection service
2025-07-04 14:25:38.254 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 14:25:38.256 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 14:25:38.712 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-04 14:25:38.714 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 14:25:38.715 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 14:25:38.718 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-04 14:25:38.719 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-04 14:25:38.721 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-04 14:25:38.723 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-04 14:25:38.726 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-04 14:25:39.243 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-04 14:25:39.246 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-04 14:25:39.249 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-04 14:25:39.250 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-04 14:25:39.253 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-04 14:25:39.254 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-04 14:25:39.254 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-04 14:25:39.258 [Information] VocomService: Found 3 Vocom devices
2025-07-04 14:25:39.259 [Information] App: Found 3 Vocom devices, attempting to connect to the first one
2025-07-04 14:25:39.263 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:39.263 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:39.282 [Information] VocomService: PTT application is not running
2025-07-04 14:25:39.289 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-04 14:25:39.290 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-04 14:25:39.290 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:39.309 [Information] VocomService: PTT application is not running
2025-07-04 14:25:39.310 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-04 14:25:39.312 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-04 14:25:39.313 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-04 14:25:39.313 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 14:25:39.314 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-04 14:25:39.527 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 14:25:39.528 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-04 14:25:39.530 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 14:25:39.532 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 14:25:39.533 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 14:25:39.534 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 14:25:39.534 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-04 14:25:39.534 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:39.535 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:39.536 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-04 14:25:39.540 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-04 14:25:39.543 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:39.544 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-04 14:25:39.548 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 14:25:39.552 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 14:25:39.552 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 14:25:39.555 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 14:25:39.557 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 14:25:39.564 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 14:25:39.567 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 14:25:39.595 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 14:25:39.605 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 14:25:39.608 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 14:25:39.608 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 14:25:39.612 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 14:25:39.613 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 14:25:39.613 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 14:25:39.615 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 14:25:39.616 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 14:25:39.616 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 14:25:39.620 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 14:25:39.620 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 14:25:39.621 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 14:25:39.623 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 14:25:39.623 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 14:25:39.624 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 14:25:39.624 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 14:25:39.628 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 14:25:39.630 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:39.630 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 14:25:39.630 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 14:25:39.631 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:39.631 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 14:25:39.631 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 14:25:39.632 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:39.632 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 14:25:39.632 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 14:25:39.633 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:39.633 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 14:25:39.633 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 14:25:39.634 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 14:25:39.637 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 14:25:39.637 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 14:25:39.638 [Information] VocomService: Using new enhanced device detection service
2025-07-04 14:25:39.638 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 14:25:39.638 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 14:25:39.954 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-04 14:25:39.955 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 14:25:39.955 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 14:25:39.955 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-04 14:25:39.956 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-04 14:25:39.956 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-04 14:25:39.956 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-04 14:25:39.957 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-04 14:25:40.250 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-04 14:25:40.251 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-04 14:25:40.253 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-04 14:25:40.253 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-04 14:25:40.254 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-04 14:25:40.254 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-04 14:25:40.254 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-04 14:25:40.256 [Information] VocomService: Found 3 Vocom devices
2025-07-04 14:25:40.257 [Information] VocomService: Attempting to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:40.258 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:40.258 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:40.274 [Information] VocomService: PTT application is not running
2025-07-04 14:25:40.275 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-04 14:25:40.275 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-04 14:25:40.275 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:40.291 [Information] VocomService: PTT application is not running
2025-07-04 14:25:40.292 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-04 14:25:40.292 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-04 14:25:40.292 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-04 14:25:40.293 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 14:25:40.293 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-04 14:25:40.294 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 14:25:40.294 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-04 14:25:40.294 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 14:25:40.295 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 14:25:40.295 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 14:25:40.295 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 14:25:40.295 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-04 14:25:40.296 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:40.296 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:40.297 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:40.297 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:40.297 [Warning] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:40.298 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:40.298 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:40.298 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:40.316 [Information] VocomService: PTT application is not running
2025-07-04 14:25:40.320 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:40.322 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-04 14:25:41.126 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:41.127 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:41.128 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-04 14:25:41.129 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:41.129 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-04 14:25:41.131 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-04 14:25:41.138 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-04 14:25:41.141 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-04 14:25:41.144 [Information] BackupService: Initializing backup service
2025-07-04 14:25:41.144 [Information] BackupService: Backup service initialized successfully
2025-07-04 14:25:41.145 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-04 14:25:41.145 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-04 14:25:41.148 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-04 14:25:41.196 [Information] BackupService: Compressing backup data
2025-07-04 14:25:41.207 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (451 bytes)
2025-07-04 14:25:41.208 [Information] BackupServiceFactory: Created template for category: Production
2025-07-04 14:25:41.208 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-04 14:25:41.209 [Information] BackupService: Compressing backup data
2025-07-04 14:25:41.210 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-04 14:25:41.211 [Information] BackupServiceFactory: Created template for category: Development
2025-07-04 14:25:41.211 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-04 14:25:41.212 [Information] BackupService: Compressing backup data
2025-07-04 14:25:41.213 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-04 14:25:41.213 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-04 14:25:41.214 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-04 14:25:41.214 [Information] BackupService: Compressing backup data
2025-07-04 14:25:41.215 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-04 14:25:41.215 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-04 14:25:41.216 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-04 14:25:41.216 [Information] BackupService: Compressing backup data
2025-07-04 14:25:41.217 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (451 bytes)
2025-07-04 14:25:41.218 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-04 14:25:41.220 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-04 14:25:41.222 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-04 14:25:41.223 [Information] BackupService: Compressing backup data
2025-07-04 14:25:41.224 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (512 bytes)
2025-07-04 14:25:41.224 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-04 14:25:41.225 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-04 14:25:41.226 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-04 14:25:41.230 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 14:25:41.247 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 14:25:41.345 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-07-04 14:25:41.346 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 14:25:41.348 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-04 14:25:41.348 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-04 14:25:41.348 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-04 14:25:41.350 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-04 14:25:41.351 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-04 14:25:41.357 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-04 14:25:41.358 [Information] App: Flash operation monitor service initialized successfully
2025-07-04 14:25:41.372 [Information] LicensingService: Initializing licensing service
2025-07-04 14:25:41.447 [Information] LicensingService: License information loaded successfully
2025-07-04 14:25:41.455 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-04 14:25:41.455 [Information] App: Licensing service initialized successfully
2025-07-04 14:25:41.456 [Information] App: License status: Trial
2025-07-04 14:25:41.456 [Information] App: Trial period: 1 days remaining
2025-07-04 14:25:41.457 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-04 14:25:41.651 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 14:25:41.652 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 14:25:41.652 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 14:25:41.653 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 14:25:41.653 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 14:25:41.654 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 14:25:41.654 [Information] VocomService: Native USB communication service initialized
2025-07-04 14:25:41.654 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 14:25:41.654 [Information] VocomService: Connection recovery service initialized
2025-07-04 14:25:41.655 [Information] VocomService: Enhanced services initialization completed
2025-07-04 14:25:41.655 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:41.670 [Information] VocomService: PTT application is not running
2025-07-04 14:25:41.671 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 14:25:41.722 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 14:25:41.722 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 14:25:41.722 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 14:25:41.723 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 14:25:41.723 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 14:25:41.725 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 14:25:41.725 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 14:25:41.727 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 14:25:41.727 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 14:25:41.728 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 14:25:41.739 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-04 14:25:41.741 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-04 14:25:41.741 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-04 14:25:41.742 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-04 14:25:41.742 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-04 14:25:41.742 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-04 14:25:41.742 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-04 14:25:41.743 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-04 14:25:41.743 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-04 14:25:41.745 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-04 14:25:41.745 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-04 14:25:41.745 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-04 14:25:41.746 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-04 14:25:41.746 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-04 14:25:41.746 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-04 14:25:41.746 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-04 14:25:41.747 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-04 14:25:41.750 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-04 14:25:41.756 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-04 14:25:41.758 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-04 14:25:41.762 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-04 14:25:41.764 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-04 14:25:41.772 [Information] CANRegisterAccess: Read value 0x06 from register 0x0141 (simulated)
2025-07-04 14:25:41.780 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-04 14:25:41.786 [Information] CANRegisterAccess: Read value 0xCC from register 0x0141 (simulated)
2025-07-04 14:25:41.792 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-04 14:25:41.798 [Information] CANRegisterAccess: Read value 0x09 from register 0x0141 (simulated)
2025-07-04 14:25:41.798 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-04 14:25:41.799 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-04 14:25:41.800 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-04 14:25:41.806 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-04 14:25:41.806 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-04 14:25:41.812 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-04 14:25:41.812 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-04 14:25:41.812 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-04 14:25:41.820 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-04 14:25:41.820 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-04 14:25:41.821 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-04 14:25:41.827 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-04 14:25:41.827 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-04 14:25:41.834 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-04 14:25:41.834 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-04 14:25:41.841 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-04 14:25:41.841 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-04 14:25:41.847 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-04 14:25:41.847 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-04 14:25:41.854 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-04 14:25:41.854 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-04 14:25:41.860 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-04 14:25:41.860 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-04 14:25:41.866 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-04 14:25:41.866 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-04 14:25:41.872 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-04 14:25:41.872 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-04 14:25:41.878 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-04 14:25:41.878 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-04 14:25:41.884 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-04 14:25:41.884 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-04 14:25:41.891 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-04 14:25:41.891 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-04 14:25:41.897 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-04 14:25:41.897 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-04 14:25:41.903 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-04 14:25:41.904 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-04 14:25:41.910 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-04 14:25:41.910 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-04 14:25:41.917 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-04 14:25:41.917 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-04 14:25:41.924 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-04 14:25:41.924 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-04 14:25:41.931 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-04 14:25:41.931 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-04 14:25:41.931 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-04 14:25:41.938 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-04 14:25:41.938 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-04 14:25:41.938 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-04 14:25:41.939 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-04 14:25:41.945 [Information] CANRegisterAccess: Read value 0xEF from register 0x0141 (simulated)
2025-07-04 14:25:41.954 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-04 14:25:41.960 [Information] CANRegisterAccess: Read value 0xA4 from register 0x0141 (simulated)
2025-07-04 14:25:41.960 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-04 14:25:41.960 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-04 14:25:41.961 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-04 14:25:41.961 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-04 14:25:41.967 [Information] CANRegisterAccess: Read value 0x5D from register 0x0140 (simulated)
2025-07-04 14:25:41.967 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-04 14:25:41.968 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-04 14:25:41.969 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 14:25:41.969 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 14:25:41.981 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-04 14:25:41.982 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-04 14:25:41.982 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-04 14:25:41.989 [Information] VocomService: Sending data and waiting for response
2025-07-04 14:25:41.989 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-04 14:25:42.041 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-04 14:25:42.044 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-04 14:25:42.044 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-04 14:25:42.045 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 14:25:42.045 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 14:25:42.056 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-04 14:25:42.057 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-04 14:25:42.058 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-04 14:25:42.071 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-04 14:25:42.082 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-04 14:25:42.093 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-04 14:25:42.104 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-04 14:25:42.115 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-04 14:25:42.116 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 14:25:42.117 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 14:25:42.127 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-04 14:25:42.128 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-04 14:25:42.129 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-04 14:25:42.139 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-04 14:25:42.150 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-04 14:25:42.161 [Information] IICProtocolHandler: Enabling IIC module
2025-07-04 14:25:42.172 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-04 14:25:42.183 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-04 14:25:42.194 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-04 14:25:42.195 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 14:25:42.195 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 14:25:42.206 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-04 14:25:42.208 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-04 14:25:42.208 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-04 14:25:42.209 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-04 14:25:42.209 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-04 14:25:42.209 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-04 14:25:42.209 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-04 14:25:42.210 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-04 14:25:42.210 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-04 14:25:42.210 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-04 14:25:42.210 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-04 14:25:42.211 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-04 14:25:42.211 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-04 14:25:42.211 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-04 14:25:42.211 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-04 14:25:42.212 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-04 14:25:42.212 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-04 14:25:42.313 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-04 14:25:42.314 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 14:25:42.314 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 14:25:42.315 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:42.315 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 14:25:42.315 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 14:25:42.315 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:42.316 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 14:25:42.316 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 14:25:42.316 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:42.317 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 14:25:42.317 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 14:25:42.317 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 14:25:42.318 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 14:25:42.318 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-04 14:25:42.369 [Information] BackupService: Initializing backup service
2025-07-04 14:25:42.370 [Information] BackupService: Backup service initialized successfully
2025-07-04 14:25:42.421 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 14:25:42.421 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 14:25:42.423 [Information] BackupSchedulerService: Loaded 2 backup schedules from D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Launcher\bin\Release\net8.0-windows\win-x64\Schedules\backup_schedules.json
2025-07-04 14:25:42.423 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 14:25:42.476 [Information] BackupService: Getting predefined backup categories
2025-07-04 14:25:42.528 [Information] MainViewModel: Services initialized successfully
2025-07-04 14:25:42.531 [Information] MainViewModel: Scanning for Vocom devices
2025-07-04 14:25:42.532 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 14:25:42.533 [Information] VocomService: Using new enhanced device detection service
2025-07-04 14:25:42.533 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 14:25:42.533 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 14:25:42.823 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-04 14:25:42.823 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 14:25:42.823 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 14:25:42.824 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-04 14:25:42.824 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-04 14:25:42.824 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-04 14:25:42.824 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-04 14:25:42.825 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-04 14:25:43.189 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-04 14:25:43.190 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-04 14:25:43.190 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-04 14:25:43.191 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-04 14:25:43.191 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-04 14:25:43.191 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-04 14:25:43.192 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-04 14:25:43.221 [Information] VocomService: Found 3 Vocom devices
2025-07-04 14:25:43.222 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-04 14:25:58.397 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-04 14:25:58.397 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.399 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-04 14:25:58.401 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:58.807 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-04 14:25:58.809 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-04 14:25:58.809 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-04 14:25:58.813 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-04 14:25:58.814 [Information] ECUCommunicationService: No ECUs are connected
2025-07-04 14:25:58.814 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-04 14:25:58.815 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-04 14:25:58.815 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-04 14:25:58.815 [Information] ECUCommunicationService: No ECUs are connected
2025-07-04 14:25:58.816 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:58.833 [Information] VocomService: PTT application is not running
2025-07-04 14:25:58.833 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB with enhanced capabilities
2025-07-04 14:25:58.833 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-04 14:25:58.833 [Information] VocomService: Checking if PTT application is running
2025-07-04 14:25:58.848 [Information] VocomService: PTT application is not running
2025-07-04 14:25:58.848 [Information] VocomService: Attempting connection with native USB service to WUDFPuma Driver
2025-07-04 14:25:58.849 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: WUDFPuma Driver
2025-07-04 14:25:58.849 [Error] NativeVocomUSBCommunication: Failed to open device \\.\WUDFPuma Driver. Error: 2
2025-07-04 14:25:58.849 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 14:25:58.850 [Information] VocomService: Using standard USB communication service to connect to WUDFPuma Driver
2025-07-04 14:25:58.850 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 14:25:58.850 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-04 14:25:58.850 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 14:25:58.851 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 14:25:58.851 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 14:25:58.851 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 14:25:58.851 [Error] VocomService: Standard USB connection failed for device 88890300-DRIVER
2025-07-04 14:25:58.852 [Error] VocomService: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:58.852 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:58.852 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:58.853 [Error] MainViewModel: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:58.853 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:58.854 [Error] MainViewModel: ECU error: Vocom error: All USB connection methods failed for device 88890300-DRIVER
2025-07-04 14:25:58.854 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.854 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.854 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.855 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.855 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.855 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-04 14:25:58.857 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
