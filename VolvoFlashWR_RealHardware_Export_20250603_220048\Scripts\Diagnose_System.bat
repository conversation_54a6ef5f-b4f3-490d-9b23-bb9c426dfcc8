@echo off
echo ========================================
echo VolvoFlashWR System Diagnostics
echo ========================================
echo.

REM Change to application directory
cd /d "%~dp0..\Application"

echo Checking VolvoFlashWR Application Files...
echo.

REM Check main executables
if exist "VolvoFlashWR.Launcher.exe" (
    echo ✓ VolvoFlashWR.Launcher.exe found
) else (
    echo ✗ VolvoFlashWR.Launcher.exe missing - CRITICAL
)

if exist "VolvoFlashWR.UI.exe" (
    echo ✓ VolvoFlashWR.UI.exe found
) else (
    echo ✗ VolvoFlashWR.UI.exe missing - CRITICAL
)

echo.
echo Checking Critical Libraries...
echo.

REM Check critical libraries
if exist "Libraries\WUDFPuma.dll" (
    echo ✓ WUDFPuma.dll found
) else (
    echo ✗ WUDFPuma.dll missing - CRITICAL
)

if exist "Libraries\apci.dll" (
    echo ✓ apci.dll found
) else (
    echo ✗ apci.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlus.dll" (
    echo ✓ Volvo.ApciPlus.dll found
) else (
    echo ✗ Volvo.ApciPlus.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlusData.dll" (
    echo ✓ Volvo.ApciPlusData.dll found
) else (
    echo ✗ Volvo.ApciPlusData.dll missing - CRITICAL
)

if exist "Libraries\apcidb.dll" (
    echo ✓ apcidb.dll found
) else (
    echo ✗ apcidb.dll missing - IMPORTANT
)

echo.
echo Checking System Vocom Drivers...
echo.

REM Check system Vocom drivers
if exist "C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll" (
    echo ✓ System Vocom driver found
) else (
    echo ✗ System Vocom driver missing - Install CommunicationUnitInstaller-*******.msi
)

if exist "C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021" (
    echo ✓ Phoenix Diag installation found
) else (
    echo ⚠ Phoenix Diag not found - Optional but recommended
)

echo.
echo Checking Configuration Files...
echo.

if exist "Drivers\Vocom\config.json" (
    echo ✓ Vocom configuration found
) else (
    echo ✗ Vocom configuration missing
)

if exist "Config" (
    echo ✓ Config directory found
) else (
    echo ✗ Config directory missing
)

echo.
echo Checking Visual C++ Redistributables (Local Libraries)...
echo.

REM Check for local Visual C++ 2013 redistributable
if exist "Libraries\msvcr120.dll" (
    echo ✓ Visual C++ 2013 Runtime found (Local)
) else (
    echo ✗ Visual C++ 2013 Runtime missing (Local)
)

if exist "Libraries\msvcp120.dll" (
    echo ✓ Visual C++ 2013 C++ Runtime found (Local)
) else (
    echo ✗ Visual C++ 2013 C++ Runtime missing (Local)
)

REM Check for local Visual C++ 2015-2022 redistributable
if exist "Libraries\vcruntime140.dll" (
    echo ✓ Visual C++ 2015-2022 Runtime found (Local)
) else (
    echo ✗ Visual C++ 2015-2022 Runtime missing (Local)
)

if exist "Libraries\msvcp140.dll" (
    echo ✓ Visual C++ 2015-2022 C++ Runtime found (Local)
) else (
    echo ✗ Visual C++ 2015-2022 C++ Runtime missing (Local)
)

REM Check for additional runtime libraries
if exist "Libraries\vcruntime140_1.dll" (
    echo ✓ Visual C++ 2015-2022 Runtime (v1) found (Local)
) else (
    echo ⚠ Visual C++ 2015-2022 Runtime (v1) missing (Local) - Optional
)

if exist "Libraries\msvcp140_1.dll" (
    echo ✓ Visual C++ 2015-2022 C++ Runtime (v1) found (Local)
) else (
    echo ⚠ Visual C++ 2015-2022 C++ Runtime (v1) missing (Local) - Optional
)

echo.
echo Checking USB Devices (looking for Vocom adapters)...
echo.

REM Use PowerShell to check for USB devices
powershell -Command "Get-WmiObject -Class Win32_USBControllerDevice | ForEach-Object { [wmi]($_.Dependent) } | Where-Object { $_.Description -like '*Vocom*' -or $_.Description -like '*88890*' -or $_.Description -like '*Volvo*' } | Select-Object Description, DeviceID | Format-Table -AutoSize"

if %ERRORLEVEL% NEQ 0 (
    echo ✗ No Vocom USB devices detected - CRITICAL ISSUE
    echo.
    echo HARDWARE PROBLEM: No real Vocom adapter found!
    echo This explains why the application runs in simulation mode.
    echo.
    echo To check manually:
    echo 1. Open Device Manager
    echo 2. Look for "88890020 Adapter" or similar Vocom device
    echo 3. Ensure no warning symbols are present
    echo 4. Verify the adapter is connected via USB
) else (
    echo ✓ Vocom USB device(s) detected
)

echo.
echo ========================================
echo DIAGNOSTIC SUMMARY
echo ========================================
echo.
echo CURRENT STATUS ANALYSIS:
echo.
echo SOFTWARE REQUIREMENTS:
echo - Critical libraries: Should all show ✓
echo - System Vocom drivers: Should show ✓
echo - Visual C++ redistributables: Should show ✓
echo.
echo HARDWARE REQUIREMENTS:
echo - USB Vocom adapter detection: CRITICAL - Must show ✓
echo - Real ECU connected to adapter: Required for actual communication
echo - Proper power and wiring: Required for ECU communication
echo.
echo ========================================
echo TROUBLESHOOTING ACTIONS:
echo ========================================
echo.
echo If Visual C++ redistributables show ✗:
echo   → The required libraries should now be included locally
echo   → No system-wide installation needed
echo.
echo If USB Vocom devices show ✗:
echo   → This is the MAIN ISSUE causing simulation mode
echo   → Connect a real Vocom 1 adapter via USB
echo   → Check Device Manager for adapter recognition
echo   → Ensure adapter drivers are properly installed
echo.
echo If all items show ✓ but application still runs in dummy mode:
echo   → Hardware is detected but no ECU is responding
echo   → Check ECU power, wiring, and communication setup
echo.
echo For detailed guidance, see:
echo   Documentation\REAL_HARDWARE_TROUBLESHOOTING.md
echo.

pause
