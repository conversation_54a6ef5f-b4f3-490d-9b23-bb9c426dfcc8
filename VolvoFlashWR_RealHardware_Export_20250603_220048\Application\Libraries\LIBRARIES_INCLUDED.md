# Libraries Included in VolvoFlashWR Export

## Visual C++ Runtime Libraries (Local)

The following Visual C++ runtime libraries have been included locally to eliminate the need for system-wide installation:

### Visual C++ 2013 Redistributable
- **msvcr120.dll** (941.2 KB) - Microsoft Visual C++ 2013 Runtime
- **msvcp120.dll** (644.7 KB) - Microsoft Visual C++ 2013 C++ Runtime

### Visual C++ 2015-2022 Redistributable  
- **vcruntime140.dll** (121.6 KB) - Visual C++ 2015-2022 Runtime
- **vcruntime140_1.dll** (48.6 KB) - Visual C++ 2015-2022 Runtime (v1)
- **msvcp140.dll** (544.6 KB) - Visual C++ 2015-2022 C++ Runtime
- **msvcp140_1.dll** (35.1 KB) - Visual C++ 2015-2022 C++ Runtime (v1)
- **msvcp140_2.dll** (273.6 KB) - Visual C++ 2015-2022 C++ Runtime (v2)

## Critical Vocom Driver Libraries

### Core Vocom Libraries
- **WUDFPuma.dll** - Primary Vocom 1 adapter driver
- **WUDFUpdate_01009.dll** - Windows User-Mode Driver Framework Update
- **WdfCoInstaller01009.dll** - Windows Driver Framework Co-installer
- **winusbcoinstaller2.dll** - WinUSB Co-installer

### APCI Communication Libraries
- **apci.dll** - APCI communication library
- **apcidb.dll** - APCI database library

### Volvo-Specific Libraries
- **Volvo.ApciPlus.dll** - Volvo APCI Plus communication
- **Volvo.ApciPlusData.dll** - Volvo APCI Plus data handling
- **Volvo.ApciPlusTea2Data.dll** - Volvo APCI Plus TEA2 data
- **Volvo.ApciPlus.Simulator.dll.config** - Simulator configuration

## Phoenix Diagnostic Libraries
- **PhoenixESW.dll** - Phoenix Electronic Service Workshop
- **PhoenixGeneral.dll** - Phoenix General utilities
- **PhoenixProducInformation.dll** - Phoenix Product Information

## Volvo IT Framework Libraries
- **VolvoIt.Baf.Utility.dll** - Volvo IT BAF Utility
- **VolvoIt.Fido.Agent.Gateway.Contract.dll** - Volvo IT FIDO Agent Gateway
- **VolvoIt.Waf.ServiceContract.dll** - Volvo IT WAF Service Contract
- **VolvoIt.Waf.Utility.dll** - Volvo IT WAF Utility

## Volvo NVS (Network Vehicle Services) Libraries
- **Volvo.NVS.Core.dll** - Volvo NVS Core
- **Volvo.NVS.Logging.dll** - Volvo NVS Logging
- **Volvo.NVS.Persistence.dll** - Volvo NVS Persistence
- **Volvo.NVS.Persistence.NHibernate.dll** - Volvo NVS NHibernate Persistence

## Vodia Platform Libraries
- **Vodia.CommonDomain.Model.dll** - Vodia Common Domain Model
- **Vodia.Contracts.Common.dll** - Vodia Common Contracts
- **Vodia.UtilityComponent.dll** - Vodia Utility Component

## Additional Communication Libraries
- **Pc2.dll** - PC2 communication protocol
- **Rpci.dll** - RPCI communication protocol
- **VCError.dll** - VC Error handling
- **VCUtil.Security.dll** - VC Security utilities

## Compression and Archive Libraries
- **DotNetZip.dll** - .NET ZIP compression
- **Ionic.Zip.Reduced.dll** - Ionic ZIP (reduced version)
- **SharpCompress.dll** - Sharp compression library

## File Transfer Libraries
- **WinSCP.exe** - WinSCP executable
- **WinSCPnet.dll** - WinSCP .NET library

## Data and ORM Libraries
- **NHibernate.dll** - NHibernate ORM
- **AutoMapper.dll** - Object-to-object mapping
- **Newtonsoft.Json.dll** - JSON.NET serialization
- **log4net.dll** - Logging framework

## System Libraries
The `System` subfolder contains numerous .NET Framework system libraries for compatibility.

## Configuration Files
- **VolvoFlashWR.Launcher.exe.config** - Launcher configuration with local library paths
- **VolvoFlashWR.UI.exe.config** - UI application configuration with local library paths
- **Volvo.ApciPlus.dll.config** - APCI Plus configuration
- **Volvo.ApciPlusData.dll.config** - APCI Plus Data configuration

## Driver Installation Files
- **wudfpuma.inf** - Vocom driver installation information
- **wudfpuma.PNF** - Precompiled driver information
- **WUDFPuma.cat** - Driver catalog file

## Benefits of Local Libraries

1. **No System Dependencies**: Application runs without requiring system-wide Visual C++ redistributable installation
2. **Version Control**: Ensures specific library versions are used
3. **Portability**: Application can run on systems without pre-installed redistributables
4. **Isolation**: Prevents conflicts with other applications
5. **Self-Contained**: Complete package with all dependencies

## Usage

The application is configured to automatically use these local libraries through:
- Environment variable settings in startup scripts
- Application configuration files
- DLL search path prioritization

No additional installation steps are required - the application will automatically use the included libraries.
