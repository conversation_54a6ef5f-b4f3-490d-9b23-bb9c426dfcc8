{"format": 1, "restore": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.KeyGenerator\\VolvoFlashWR.KeyGenerator.csproj": {}}, "projects": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj", "projectName": "VolvoFlashWR.Core", "projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Newtonsoft.Json": {"target": "Package", "version": "[13.0.3, )"}, "System.Buffers": {"target": "Package", "version": "[4.5.1, )"}, "System.Collections.Immutable": {"target": "Package", "version": "[8.0.0, )"}, "System.Management": {"target": "Package", "version": "[9.0.5, )"}, "System.Memory": {"target": "Package", "version": "[4.5.5, )"}, "System.Threading.Tasks.Extensions": {"target": "Package", "version": "[4.5.4, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-x64": {"#import": []}}}, "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.KeyGenerator\\VolvoFlashWR.KeyGenerator.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.KeyGenerator\\VolvoFlashWR.KeyGenerator.csproj", "projectName": "VolvoFlashWR.KeyGenerator", "projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.KeyGenerator\\VolvoFlashWR.KeyGenerator.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.KeyGenerator\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj": {"projectPath": "D:\\001-Software Engineering\\S.A.H\\S.A.H.VolvoFlashWR\\VolvoFlashWR.Core\\VolvoFlashWR.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\8.0.408/PortableRuntimeIdentifierGraph.json"}}}}}