Log started at 7/4/2025 2:45:47 PM
2025-07-04 14:45:47.862 [Information] LoggingService: Logging service initialized
2025-07-04 14:45:47.887 [Information] App: Starting integrated application initialization
2025-07-04 14:45:47.888 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-04 14:45:47.891 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-04 14:45:47.894 [Information] IntegratedStartupService: Setting up application environment
2025-07-04 14:45:47.894 [Information] IntegratedStartupService: Application environment setup completed
2025-07-04 14:45:47.896 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-04 14:45:47.899 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-04 14:45:47.902 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-04 14:45:47.912 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-04 14:45:47.915 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.917 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.918 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-04 14:45:47.921 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-04 14:45:47.924 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.927 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.927 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:45:47.931 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-04 14:45:47.934 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.937 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.937 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 14:45:47.941 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\config' is denied.
2025-07-04 14:45:47.944 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.947 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\SysWOW64: Access to the path 'C:\Windows\SysWOW64\config' is denied.
2025-07-04 14:45:47.948 [Warning] VCRedistBundler: Required Visual C++ library not found in system: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 14:45:47.950 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-04 14:45:47.952 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-04 14:45:47.953 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-04 14:45:47.956 [Debug] VCRedistBundler: Successfully loaded and verified: msvcr120.dll
2025-07-04 14:45:47.956 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-04 14:45:47.958 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp120.dll
2025-07-04 14:45:47.959 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-04 14:45:47.960 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-04 14:45:47.962 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-04 14:45:47.962 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-04 14:45:47.963 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-04 14:45:47.964 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 14:45:47.964 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 14:45:47.964 [Warning] VCRedistBundler: ✗ Missing VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 14:45:47.972 [Information] VCRedistBundler: VC++ Redistributable verification: 4/8 (50.0%) required libraries found
2025-07-04 14:45:47.973 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: False
2025-07-04 14:45:47.973 [Warning] IntegratedStartupService: VC++ Redistributable bundling completed with warnings
2025-07-04 14:45:47.980 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 4 available, 4 missing
2025-07-04 14:45:47.981 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86)), api-ms-win-crt-runtime-l1-1-0.dll (Universal CRT Runtime), api-ms-win-crt-heap-l1-1-0.dll (Universal CRT Heap), api-ms-win-crt-string-l1-1-0.dll (Universal CRT String)
2025-07-04 14:45:47.983 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-04 14:45:47.985 [Information] LibraryExtractor: Starting library extraction process
2025-07-04 14:45:47.988 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-04 14:45:47.991 [Information] LibraryExtractor: Copying system libraries
2025-07-04 14:45:47.997 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-04 14:45:48.005 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-04 14:46:36.593 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
