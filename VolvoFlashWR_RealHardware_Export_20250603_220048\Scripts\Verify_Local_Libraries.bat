@echo off
echo ========================================
echo VolvoFlashWR Local Libraries Verification
echo ========================================
echo.

REM Change to application directory
cd /d "%~dp0..\Application"

echo Verifying Visual C++ Runtime Libraries...
echo.

REM Visual C++ 2013 Libraries
if exist "Libraries\msvcr120.dll" (
    echo ✓ msvcr120.dll found
) else (
    echo ✗ msvcr120.dll missing - CRITICAL
)

if exist "Libraries\msvcp120.dll" (
    echo ✓ msvcp120.dll found
) else (
    echo ✗ msvcp120.dll missing - CRITICAL
)

REM Visual C++ 2015-2022 Libraries
if exist "Libraries\vcruntime140.dll" (
    echo ✓ vcruntime140.dll found
) else (
    echo ✗ vcruntime140.dll missing - CRITICAL
)

if exist "Libraries\msvcp140.dll" (
    echo ✓ msvcp140.dll found
) else (
    echo ✗ msvcp140.dll missing - CRITICAL
)

if exist "Libraries\vcruntime140_1.dll" (
    echo ✓ vcruntime140_1.dll found
) else (
    echo ⚠ vcruntime140_1.dll missing - Optional
)

if exist "Libraries\msvcp140_1.dll" (
    echo ✓ msvcp140_1.dll found
) else (
    echo ⚠ msvcp140_1.dll missing - Optional
)

if exist "Libraries\msvcp140_2.dll" (
    echo ✓ msvcp140_2.dll found
) else (
    echo ⚠ msvcp140_2.dll missing - Optional
)

echo.
echo Verifying Critical Vocom Libraries...
echo.

if exist "Libraries\WUDFPuma.dll" (
    echo ✓ WUDFPuma.dll found
) else (
    echo ✗ WUDFPuma.dll missing - CRITICAL
)

if exist "Libraries\apci.dll" (
    echo ✓ apci.dll found
) else (
    echo ✗ apci.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlus.dll" (
    echo ✓ Volvo.ApciPlus.dll found
) else (
    echo ✗ Volvo.ApciPlus.dll missing - CRITICAL
)

if exist "Libraries\Volvo.ApciPlusData.dll" (
    echo ✓ Volvo.ApciPlusData.dll found
) else (
    echo ✗ Volvo.ApciPlusData.dll missing - CRITICAL
)

echo.
echo Verifying Configuration Files...
echo.

if exist "VolvoFlashWR.Launcher.exe.config" (
    echo ✓ Launcher configuration found
) else (
    echo ✗ Launcher configuration missing
)

if exist "VolvoFlashWR.UI.exe.config" (
    echo ✓ UI configuration found
) else (
    echo ✗ UI configuration missing
)

echo.
echo Checking Library File Sizes...
echo.

powershell -Command "Get-ChildItem 'Libraries\*.dll' | Where-Object {$_.Name -like '*msvc*' -or $_.Name -like '*vcruntime*' -or $_.Name -eq 'WUDFPuma.dll' -or $_.Name -eq 'apci.dll'} | Select-Object Name, @{Name='Size (KB)';Expression={[math]::Round($_.Length/1KB,1)}} | Format-Table -AutoSize"

echo.
echo ========================================
echo VERIFICATION SUMMARY
echo ========================================
echo.
echo LOCAL LIBRARIES APPROACH:
echo - All Visual C++ runtime libraries are included locally
echo - No system-wide installation required
echo - Application is self-contained and portable
echo.
echo BENEFITS:
echo ✓ No dependency on system Visual C++ redistributables
echo ✓ Version-specific libraries guaranteed
echo ✓ Portable across different systems
echo ✓ No conflicts with other applications
echo.
echo If all critical items show ✓, the application should run without
echo Visual C++ redistributable dependency issues.
echo.
echo The remaining requirement is a physical Vocom 1 adapter connected via USB.
echo.

pause
