Log started at 7/4/2025 1:34:59 PM
2025-07-04 13:34:59.274 [Information] LoggingService: Logging service initialized
2025-07-04 13:34:59.281 [Information] App: Starting integrated application initialization
2025-07-04 13:34:59.283 [Information] DependencyManager: Dependency manager initialized for x64 architecture
2025-07-04 13:34:59.286 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-04 13:34:59.287 [Information] IntegratedStartupService: Setting up application environment
2025-07-04 13:34:59.288 [Information] IntegratedStartupService: Application environment setup completed
2025-07-04 13:34:59.289 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-04 13:34:59.290 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-04 13:34:59.292 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-04 13:34:59.389 [Debug] VCRedistBundler: Error searching subdirectories in C:\Windows\system32: Access to the path 'C:\Windows\system32\LogFiles\WMI\RtBackup' is denied.
2025-07-04 13:34:59.480 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-04 13:34:59.483 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-04 13:34:59.486 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-04 13:34:59.487 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-04 13:34:59.489 [Debug] VCRedistBundler: Successfully loaded and verified: msvcr120.dll
2025-07-04 13:34:59.489 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-04 13:34:59.490 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp120.dll
2025-07-04 13:34:59.491 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-04 13:34:59.491 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-04 13:34:59.493 [Debug] VCRedistBundler: Successfully loaded and verified: msvcp140.dll
2025-07-04 13:34:59.493 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-04 13:34:59.494 [Debug] VCRedistBundler: Successfully loaded and verified: vcruntime140.dll
2025-07-04 13:34:59.494 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:34:59.495 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:34:59.495 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:34:59.496 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:34:59.496 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:34:59.496 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:34:59.500 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-04 13:34:59.502 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-04 13:34:59.505 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-04 13:34:59.505 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-04 13:34:59.506 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-04 13:34:59.507 [Information] LibraryExtractor: Starting library extraction process
2025-07-04 13:34:59.509 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-04 13:34:59.510 [Information] LibraryExtractor: Copying system libraries
2025-07-04 13:34:59.512 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-04 13:34:59.520 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-04 13:35:13.694 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-04 13:35:34.434 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-04 13:36:03.376 [Information] LibraryExtractor: Verifying library extraction
2025-07-04 13:36:03.377 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-04 13:36:03.377 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-04 13:36:03.378 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-04 13:36:03.378 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-04 13:36:03.378 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-04 13:36:03.381 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-04 13:36:03.383 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-04 13:36:03.385 [Information] DependencyManager: Initializing dependency manager
2025-07-04 13:36:03.385 [Information] DependencyManager: Setting up library search paths
2025-07-04 13:36:03.386 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:36:03.386 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:36:03.386 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-04 13:36:03.387 [Information] DependencyManager: Updated PATH environment variable
2025-07-04 13:36:03.388 [Information] DependencyManager: Verifying required directories
2025-07-04 13:36:03.389 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:36:03.389 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:36:03.390 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-04 13:36:03.391 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-04 13:36:03.392 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-04 13:36:03.396 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-04 13:36:03.397 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-04 13:36:03.399 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-04 13:36:03.400 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-04 13:36:03.401 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-04 13:36:03.401 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-04 13:36:03.402 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-04 13:36:03.404 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 13:36:03.405 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 13:36:03.406 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:36:03.406 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-04 13:36:03.407 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-04 13:36:03.408 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-04 13:36:03.410 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll (x64)
2025-07-04 13:36:03.486 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:36:03.486 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll
2025-07-04 13:36:03.551 [Debug] DependencyManager: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:36:03.551 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:36:03.552 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-04 13:36:03.609 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-04 13:36:03.610 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apcidb.dll
2025-07-04 13:36:03.659 [Debug] DependencyManager: Architecture mismatch: Library apcidb.dll is x86, process is x64
2025-07-04 13:36:03.659 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-04 13:36:03.660 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-04 13:36:03.871 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-04 13:36:03.872 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlus.dll
2025-07-04 13:36:04.073 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlus.dll is x86, process is x64
2025-07-04 13:36:04.073 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-04 13:36:04.074 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-04 13:36:04.164 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-04 13:36:04.164 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\Volvo.ApciPlusData.dll
2025-07-04 13:36:04.246 [Debug] DependencyManager: Architecture mismatch: Library Volvo.ApciPlusData.dll is x86, process is x64
2025-07-04 13:36:04.247 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-04 13:36:04.247 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-04 13:36:04.299 [Debug] DependencyManager: Architecture mismatch: Library PhoenixGeneral.dll is x86, process is x64
2025-07-04 13:36:04.299 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-04 13:36:04.300 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-04 13:36:04.300 [Debug] DependencyManager: Architecture mismatch: Library msvcr120.dll is x86, process is x64
2025-07-04 13:36:04.301 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcr120.dll
2025-07-04 13:36:04.301 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-04 13:36:04.302 [Debug] DependencyManager: Architecture mismatch: Library msvcp120.dll is x86, process is x64
2025-07-04 13:36:04.302 [Warning] DependencyManager: Architecture incompatible library skipped: C:\Windows\SysWOW64\msvcp120.dll
2025-07-04 13:36:04.303 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-04 13:36:04.303 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-04 13:36:04.303 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll (x64)
2025-07-04 13:36:04.304 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll (x64)
2025-07-04 13:36:04.304 [Information] DependencyManager: Setting up environment variables
2025-07-04 13:36:04.305 [Information] DependencyManager: Environment variables configured
2025-07-04 13:36:04.306 [Information] DependencyManager: Verifying library loading status
2025-07-04 13:36:06.003 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-04 13:36:06.003 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-04 13:36:06.004 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-04 13:36:06.007 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-04 13:36:06.008 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-04 13:36:06.010 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-04 13:36:06.011 [Information] IntegratedStartupService: Verifying system readiness
2025-07-04 13:36:06.011 [Information] IntegratedStartupService: System readiness verification passed
2025-07-04 13:36:06.012 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-04 13:36:06.012 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-04 13:36:06.013 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-04 13:36:06.013 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:36:06.013 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-04 13:36:06.014 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-04 13:36:06.014 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-04 13:36:06.014 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-04 13:36:06.014 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:36:06.015 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-04 13:36:06.015 [Information] App: Integrated startup completed successfully
2025-07-04 13:36:06.017 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-04 13:36:06.149 [Information] App: Initializing application services
2025-07-04 13:36:06.151 [Information] AppConfigurationService: Initializing configuration service
2025-07-04 13:36:06.151 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-04 13:36:06.187 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-04 13:36:06.900 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-04 13:36:06.901 [Information] App: Configuration service initialized successfully
2025-07-04 13:36:06.902 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-04 13:36:06.902 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-04 13:36:06.910 [Information] App: Environment variable exists: True, not 'false': False
2025-07-04 13:36:06.910 [Information] App: Final useDummyImplementations value: False
2025-07-04 13:36:06.910 [Information] App: Updating config to NOT use dummy implementations
2025-07-04 13:36:06.911 [Debug] AppConfigurationService: Configuration value set for key 'Application.UseDummyImplementations'
2025-07-04 13:36:06.923 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-04 13:36:06.923 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-04 13:36:06.924 [Information] App: usePatchedImplementation flag is: True
2025-07-04 13:36:06.924 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-04 13:36:06.924 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-04 13:36:06.924 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-04 13:36:06.924 [Information] App: verboseLogging flag is: True
2025-07-04 13:36:06.925 [Information] App: Verifying real hardware requirements...
2025-07-04 13:36:06.929 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-04 13:36:06.929 [Information] App: ✓ Found critical library: apci.dll
2025-07-04 13:36:06.929 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-04 13:36:06.929 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-04 13:36:06.929 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:36:06.930 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-04 13:36:06.930 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-04 13:36:06.930 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-04 13:36:06.941 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-04 13:36:06.942 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-04 13:36:06.943 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-04 13:36:06.943 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-04 13:36:06.944 [Information] PatchedVocomServiceFactory: Assembly location: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Communication.dll
2025-07-04 13:36:06.957 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-04 13:36:06.958 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-04 13:36:06.958 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-04 13:36:06.958 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-04 13:36:06.959 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-04 13:36:06.989 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-04 13:36:06.992 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-04 13:36:06.992 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-04 13:36:06.992 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-04 13:36:06.993 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-04 13:36:06.993 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-04 13:36:06.994 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-04 13:36:06.995 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-04 13:36:06.998 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-04 13:36:06.998 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 13:36:06.998 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-04 13:36:07.006 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-04 13:36:07.025 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-04 13:36:07.026 [Information] PhoenixVocomAdapter: Loading APCI library dynamically with architecture awareness
2025-07-04 13:36:07.027 [Information] PhoenixVocomAdapter: Current process architecture: x64
2025-07-04 13:36:07.027 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:36:07.028 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll has incompatible architecture
2025-07-04 13:36:07.028 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:36:07.028 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\apci.dll has incompatible architecture
2025-07-04 13:36:07.101 [Warning] PhoenixVocomAdapter: Architecture mismatch: Library apci.dll is x86, process is x64
2025-07-04 13:36:07.102 [Warning] PhoenixVocomAdapter: APCI library at C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll has incompatible architecture
2025-07-04 13:36:07.102 [Error] PhoenixVocomAdapter: No compatible APCI library found
2025-07-04 13:36:07.102 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-04 13:36:07.103 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-04 13:36:07.103 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-04 13:36:07.104 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-04 13:36:07.104 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-04 13:36:07.105 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-04 13:36:07.106 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-04 13:36:07.107 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:36:07.107 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-04 13:36:07.108 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-04 13:36:07.109 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-04 13:36:07.110 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-04 13:36:07.110 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-04 13:36:07.110 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-04 13:36:07.111 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-04 13:36:07.111 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-04 13:36:07.111 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-04 13:36:07.112 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-04 13:36:07.113 [Information] VocomDiagnosticTool: ✓ Available: msvcr120.dll
2025-07-04 13:36:07.114 [Information] VocomDiagnosticTool: ✓ Available: msvcp120.dll
2025-07-04 13:36:07.115 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-04 13:36:07.115 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-04 13:36:07.116 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-04 13:36:07.116 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:36:07.116 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-04 13:36:07.117 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-04 13:36:07.117 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-04 13:36:07.118 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-04 13:36:07.120 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-04 13:36:07.120 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-04 13:36:07.126 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-04 13:36:07.126 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-04 13:36:07.126 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:36:07.126 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:36:07.128 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-04 13:36:07.128 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:07.129 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.129 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-04 13:36:07.129 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:07.130 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.130 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-04 13:36:07.285 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.285 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-04 13:36:07.362 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.362 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-04 13:36:07.363 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:07.363 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.363 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-04 13:36:07.436 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:07.516 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.516 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-04 13:36:07.576 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:36:07.683 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:36:07.776 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:07.880 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:07.880 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-04 13:36:07.881 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-04 13:36:08.067 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:08.068 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-04 13:36:08.133 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:08.202 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:08.202 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-04 13:36:08.321 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-04 13:36:08.443 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-04 13:36:08.443 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-04 13:36:08.560 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:36:08.561 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:36:08.611 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-04 13:36:08.614 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-04 13:36:08.614 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-04 13:36:08.615 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-04 13:36:08.615 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-04 13:36:08.616 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-04 13:36:08.617 [Information] VocomDriver: Initializing Vocom driver
2025-07-04 13:36:08.619 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-04 13:36:08.621 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-04 13:36:08.622 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:36:08.622 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:36:08.623 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-04 13:36:08.623 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-04 13:36:08.625 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcr120.dll from msvcr120.dll
2025-07-04 13:36:08.626 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp120.dll from msvcp120.dll
2025-07-04 13:36:08.626 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-04 13:36:08.627 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-04 13:36:08.627 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-04 13:36:08.627 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-04 13:36:08.629 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-04 13:36:08.630 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-04 13:36:08.631 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-04 13:36:08.631 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-04 13:36:08.632 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-04 13:36:08.632 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-04 13:36:08.633 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-04 13:36:08.634 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-04 13:36:08.634 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-04 13:36:08.634 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-04 13:36:08.634 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-04 13:36:08.634 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-04 13:36:08.635 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-04 13:36:08.635 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-04 13:36:08.635 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-04 13:36:08.635 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-04 13:36:08.635 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-04 13:36:08.635 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-04 13:36:08.636 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-04 13:36:08.636 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-04 13:36:08.636 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-04 13:36:08.636 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-04 13:36:08.636 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-04 13:36:08.636 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-04 13:36:08.637 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-04 13:36:08.637 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-04 13:36:08.637 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-04 13:36:08.637 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-04 13:36:08.637 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-04 13:36:08.637 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-04 13:36:08.638 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-04 13:36:08.638 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-04 13:36:08.638 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-04 13:36:08.638 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-04 13:36:08.638 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-04 13:36:08.639 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-04 13:36:08.639 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-04 13:36:08.639 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-04 13:36:08.639 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-04 13:36:08.639 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-04 13:36:08.639 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-04 13:36:08.640 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-04 13:36:08.640 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-04 13:36:08.640 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-04 13:36:08.640 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-04 13:36:08.640 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-04 13:36:08.640 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-04 13:36:08.641 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-04 13:36:08.641 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-04 13:36:08.641 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-04 13:36:08.642 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-04 13:36:08.643 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-04 13:36:08.643 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-04 13:36:08.643 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-04 13:36:08.643 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-04 13:36:08.644 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-04 13:36:08.644 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-04 13:36:08.646 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-04 13:36:08.647 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-04 13:36:08.648 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-04 13:36:08.649 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-04 13:36:08.692 [Information] WiFiCommunicationService: WiFi is available
2025-07-04 13:36:08.693 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-04 13:36:08.694 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-04 13:36:08.695 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-04 13:36:08.696 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-04 13:36:08.697 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-04 13:36:08.698 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:36:08.699 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:36:08.700 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:36:08.701 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:36:08.704 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:36:08.704 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:36:08.704 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:36:08.705 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:36:08.705 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:36:08.705 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:36:08.707 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:08.717 [Information] VocomService: PTT application is not running
2025-07-04 13:36:08.718 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:36:08.719 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:36:08.720 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:36:08.720 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-04 13:36:08.721 [Information] App: Initializing Vocom service
2025-07-04 13:36:08.721 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:36:08.721 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:36:08.722 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:36:08.722 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:36:08.723 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:36:08.723 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:36:08.724 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:36:08.724 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:36:08.724 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:36:08.724 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:36:08.725 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:08.736 [Information] VocomService: PTT application is not running
2025-07-04 13:36:08.737 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:36:08.737 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:36:08.737 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:36:08.740 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:36:08.740 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:36:08.742 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:36:08.743 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:36:09.096 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:36:09.097 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:36:09.098 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:36:09.098 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:36:09.099 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:36:09.200 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:36:09.201 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:36:09.202 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:36:09.202 [Information] App: Found 1 Vocom devices, attempting to connect to the first one
2025-07-04 13:36:09.204 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:36:09.204 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:09.214 [Information] VocomService: PTT application is not running
2025-07-04 13:36:09.217 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:36:09.217 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.217 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:09.227 [Information] VocomService: PTT application is not running
2025-07-04 13:36:09.227 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.229 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.230 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:36:09.230 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:36:09.230 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.255 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:36:09.256 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.257 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:36:09.258 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:36:09.259 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:36:09.259 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:36:09.260 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:36:09.260 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:36:09.260 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:09.260 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-04 13:36:09.263 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-04 13:36:09.266 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:09.266 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-04 13:36:09.269 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:36:09.270 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:36:09.271 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:36:09.273 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:36:09.274 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:36:09.277 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:36:09.279 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:36:09.282 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:36:09.287 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:36:09.289 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:36:09.289 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:09.291 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:36:09.291 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:36:09.291 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:09.293 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:36:09.293 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:36:09.293 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:09.294 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:36:09.295 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:36:09.295 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:09.296 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:36:09.297 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:36:09.297 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:09.297 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:36:09.300 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:36:09.301 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:09.301 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:36:09.301 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:36:09.301 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:09.302 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:36:09.302 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:36:09.302 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:09.302 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:36:09.303 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:36:09.303 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:09.303 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:36:09.303 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:36:09.304 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:36:09.305 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:36:09.306 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:36:09.306 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:36:09.306 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:36:09.307 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:36:09.486 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:36:09.486 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:36:09.487 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:36:09.487 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:36:09.487 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:36:09.588 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:36:09.588 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:36:09.588 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:36:09.589 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:36:09.589 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:36:09.590 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:09.599 [Information] VocomService: PTT application is not running
2025-07-04 13:36:09.600 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:36:09.600 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.600 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:09.611 [Information] VocomService: PTT application is not running
2025-07-04 13:36:09.611 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.611 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.611 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:36:09.612 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:36:09.612 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.612 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:36:09.612 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:09.613 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:36:09.613 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:36:09.613 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:36:09.613 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:36:09.613 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:36:09.614 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:36:09.614 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:36:09.614 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:09.615 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:36:09.615 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:09.615 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:36:09.615 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:36:09.616 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:36:09.616 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 1/3
2025-07-04 13:36:10.617 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 2/3)
2025-07-04 13:36:10.618 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:36:10.618 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:36:10.618 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:36:10.618 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:36:10.619 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:36:10.620 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:36:10.620 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:36:10.621 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:36:10.621 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:36:10.621 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:36:10.621 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:10.621 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:36:10.622 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:36:10.622 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:10.622 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:36:10.622 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:36:10.623 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:10.623 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:36:10.623 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:36:10.623 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:10.624 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:36:10.624 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:36:10.624 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:10.624 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:36:10.625 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:36:10.625 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:10.625 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:36:10.625 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:36:10.626 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:10.626 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:36:10.626 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:36:10.627 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:10.627 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:36:10.627 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:36:10.628 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:10.628 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:36:10.628 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:36:10.628 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:36:10.629 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:36:10.629 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:36:10.629 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:36:10.629 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:36:10.630 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:36:10.813 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:36:10.813 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:36:10.814 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:36:10.814 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:36:10.814 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:36:10.915 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:36:10.916 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:36:10.916 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:36:10.916 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:36:10.916 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:36:10.916 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:10.928 [Information] VocomService: PTT application is not running
2025-07-04 13:36:10.929 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:36:10.929 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:10.929 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:10.944 [Information] VocomService: PTT application is not running
2025-07-04 13:36:10.945 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:10.945 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:10.945 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:36:10.945 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:36:10.946 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:10.946 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:36:10.946 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:10.946 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:36:10.947 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:36:10.947 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:36:10.947 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:36:10.947 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:36:10.947 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:36:10.947 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:36:10.948 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:36:10.948 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:10.948 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:36:10.948 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:36:10.948 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:10.949 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:36:10.949 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:36:10.949 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:36:10.949 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:36:10.950 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 2/3
2025-07-04 13:36:12.949 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 3/3)
2025-07-04 13:36:12.950 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-04 13:36:12.950 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-04 13:36:12.950 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-04 13:36:12.950 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-04 13:36:12.951 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-04 13:36:12.952 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-04 13:36:12.952 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-04 13:36:12.952 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-04 13:36:12.953 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:36:12.953 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-04 13:36:12.953 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:12.953 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:36:12.954 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-04 13:36:12.954 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:12.954 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:36:12.954 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-04 13:36:12.954 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:12.955 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:36:12.955 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-04 13:36:12.955 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:12.955 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:36:12.955 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-04 13:36:12.955 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-04 13:36:12.956 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-04 13:36:12.956 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-04 13:36:12.956 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:12.956 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-04 13:36:12.956 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-04 13:36:12.957 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:12.957 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-04 13:36:12.957 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-04 13:36:12.957 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:12.957 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-04 13:36:12.958 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-04 13:36:12.958 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-04 13:36:12.958 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-04 13:36:12.958 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-04 13:36:12.958 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-04 13:36:12.959 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-04 13:36:12.959 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:36:12.959 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:36:12.959 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:36:12.960 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:36:13.124 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:36:13.125 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:36:13.125 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:36:13.125 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:36:13.125 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:36:13.226 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:36:13.226 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:36:13.226 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:36:13.227 [Information] VocomService: Attempting to connect to Vocom device  via USB
2025-07-04 13:36:13.227 [Information] VocomService: Connecting to Vocom device  via USB
2025-07-04 13:36:13.227 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:13.238 [Information] VocomService: PTT application is not running
2025-07-04 13:36:13.239 [Information] VocomService: Connecting to Vocom device  via USB with enhanced capabilities
2025-07-04 13:36:13.239 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:13.239 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:13.251 [Information] VocomService: PTT application is not running
2025-07-04 13:36:13.251 [Information] VocomService: Attempting connection with native USB service to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:13.252 [Information] NativeVocomUSBCommunication: Attempting native USB connection to: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:13.252 [Error] NativeVocomUSBCommunication: Failed to open device \\.\USB\VID_178E&PID_0024\0000007658. Error: 3
2025-07-04 13:36:13.252 [Warning] VocomService: Native USB connection failed, trying standard USB service
2025-07-04 13:36:13.252 [Information] VocomService: Using standard USB communication service to connect to USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:13.253 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-04 13:36:13.253 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\0000007658
2025-07-04 13:36:13.253 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-04 13:36:13.254 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-04 13:36:13.254 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-04 13:36:13.254 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-04 13:36:13.254 [Error] VocomService: Standard USB connection failed for device 
2025-07-04 13:36:13.255 [Error] VocomService: All USB connection methods failed for device 
2025-07-04 13:36:13.255 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:36:13.255 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:36:13.256 [Error] ECUCommunicationService: Vocom error: All USB connection methods failed for device 
2025-07-04 13:36:13.256 [Error] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:13.256 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:36:13.256 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:36:13.257 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device  via USB
2025-07-04 13:36:13.257 [Warning] VocomService: Failed to connect to Vocom device  via USB
2025-07-04 13:36:13.257 [Error] VocomService: Failed to connect to any Vocom device
2025-07-04 13:36:13.257 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:36:13.258 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:36:13.258 [Error] ECUCommunicationService: Vocom error: Failed to connect to any Vocom device
2025-07-04 13:36:13.258 [Error] ECUCommunicationService: Failed to connect to a Vocom adapter
2025-07-04 13:36:13.258 [Warning] ECUCommunicationServiceFactory: Failed to initialize ECU communication service on attempt 3/3
2025-07-04 13:36:16.258 [Error] ECUCommunicationServiceFactory: Failed to initialize ECU communication service after 3 attempts
2025-07-04 13:36:16.259 [Warning] ECUCommunicationServiceFactory: Creating DummyECUCommunicationService as fallback
2025-07-04 13:36:16.260 [Information] DummyECUCommunicationService: DummyECUCommunicationService created
2025-07-04 13:36:16.261 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-04 13:36:16.762 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-04 13:36:16.762 [Information] ECUCommunicationServiceFactory: DummyECUCommunicationService initialized successfully as fallback
2025-07-04 13:36:16.763 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedCategories' not found, returning default value
2025-07-04 13:36:16.763 [Debug] AppConfigurationService: Configuration key 'Backup.PredefinedTags' not found, returning default value
2025-07-04 13:36:16.765 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-04 13:36:16.766 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-04 13:36:16.768 [Information] BackupService: Initializing backup service
2025-07-04 13:36:16.769 [Information] BackupService: Backup service initialized successfully
2025-07-04 13:36:16.769 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-04 13:36:16.769 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-04 13:36:16.770 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-04 13:36:16.792 [Information] BackupService: Compressing backup data
2025-07-04 13:36:16.798 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (446 bytes)
2025-07-04 13:36:16.799 [Information] BackupServiceFactory: Created template for category: Production
2025-07-04 13:36:16.799 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-04 13:36:16.800 [Information] BackupService: Compressing backup data
2025-07-04 13:36:16.801 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (450 bytes)
2025-07-04 13:36:16.801 [Information] BackupServiceFactory: Created template for category: Development
2025-07-04 13:36:16.801 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-04 13:36:16.801 [Information] BackupService: Compressing backup data
2025-07-04 13:36:16.802 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-04 13:36:16.802 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-04 13:36:16.802 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-04 13:36:16.803 [Information] BackupService: Compressing backup data
2025-07-04 13:36:16.803 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (445 bytes)
2025-07-04 13:36:16.804 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-04 13:36:16.804 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-04 13:36:16.804 [Information] BackupService: Compressing backup data
2025-07-04 13:36:16.805 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (446 bytes)
2025-07-04 13:36:16.805 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-04 13:36:16.805 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-04 13:36:16.806 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-04 13:36:16.806 [Information] BackupService: Compressing backup data
2025-07-04 13:36:16.807 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (510 bytes)
2025-07-04 13:36:16.807 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-04 13:36:16.808 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-04 13:36:16.809 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-04 13:36:16.812 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 13:36:16.813 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 13:36:16.851 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-04 13:36:16.852 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 13:36:16.853 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-04 13:36:16.853 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-04 13:36:16.853 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-04 13:36:16.854 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-04 13:36:16.854 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-04 13:36:16.857 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-04 13:36:16.858 [Information] App: Flash operation monitor service initialized successfully
2025-07-04 13:36:16.863 [Information] LicensingService: Initializing licensing service
2025-07-04 13:36:16.911 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-04 13:36:16.912 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-04 13:36:16.913 [Information] App: Licensing service initialized successfully
2025-07-04 13:36:16.913 [Information] App: License status: Trial
2025-07-04 13:36:16.913 [Information] App: Trial period: 30 days remaining
2025-07-04 13:36:16.914 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-04 13:36:17.057 [Information] VocomService: Initializing Vocom service with enhanced capabilities
2025-07-04 13:36:17.057 [Information] VocomService: Initializing enhanced Vocom services
2025-07-04 13:36:17.057 [Information] NativeVocomUSBCommunication: Initializing native Vocom USB communication
2025-07-04 13:36:17.057 [Information] NativeVocomUSBCommunication: Detecting Vocom devices using native USB enumeration
2025-07-04 13:36:17.058 [Information] NativeVocomUSBCommunication: Native USB detection found 0 Vocom devices
2025-07-04 13:36:17.058 [Information] NativeVocomUSBCommunication: Found 0 potential Vocom devices
2025-07-04 13:36:17.058 [Information] VocomService: Native USB communication service initialized
2025-07-04 13:36:17.059 [Information] VocomService: Enhanced device detection service initialized
2025-07-04 13:36:17.059 [Information] VocomService: Connection recovery service initialized
2025-07-04 13:36:17.059 [Information] VocomService: Enhanced services initialization completed
2025-07-04 13:36:17.059 [Information] VocomService: Checking if PTT application is running
2025-07-04 13:36:17.069 [Information] VocomService: PTT application is not running
2025-07-04 13:36:17.069 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:36:17.070 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:36:17.070 [Information] VocomService: Vocom service initialized successfully with enhanced capabilities
2025-07-04 13:36:17.120 [Information] DummyECUCommunicationService: Initializing DummyECUCommunicationService
2025-07-04 13:36:17.621 [Information] DummyECUCommunicationService: DummyECUCommunicationService initialized successfully
2025-07-04 13:36:17.672 [Information] BackupService: Initializing backup service
2025-07-04 13:36:17.672 [Information] BackupService: Backup service initialized successfully
2025-07-04 13:36:17.725 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-04 13:36:17.725 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-04 13:36:17.726 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\TEST RW\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-04 13:36:17.727 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-04 13:36:17.777 [Information] BackupService: Getting predefined backup categories
2025-07-04 13:36:17.829 [Information] MainViewModel: Services initialized successfully
2025-07-04 13:36:17.831 [Information] MainViewModel: Scanning for Vocom devices
2025-07-04 13:36:17.832 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:36:17.832 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:36:17.832 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:36:17.832 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:36:17.995 [Information] EnhancedVocomDeviceDetection: Found Vocom device via WMI: Vocom - 88890300 (USB\VID_178E&PID_0024\0000007658)
2025-07-04 13:36:17.995 [Information] EnhancedVocomDeviceDetection: Found 1 potential USB Vocom devices
2025-07-04 13:36:17.996 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:36:17.996 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:36:17.996 [Information] EnhancedVocomDeviceDetection: Validating device: Vocom - 88890300
2025-07-04 13:36:18.098 [Information] EnhancedVocomDeviceDetection: Device validated: Vocom - 88890300
2025-07-04 13:36:18.099 [Information] EnhancedVocomDeviceDetection: Detected 1 valid Vocom devices
2025-07-04 13:36:18.099 [Information] VocomService: Enhanced device detection found 1 Vocom devices
2025-07-04 13:36:18.100 [Information] MainViewModel: Found 1 Vocom device(s)
2025-07-04 13:37:12.839 [Information] MainViewModel: Scanning for Vocom devices
2025-07-04 13:37:12.840 [Information] VocomService: Scanning for Vocom devices with enhanced detection capabilities
2025-07-04 13:37:12.840 [Information] VocomService: Using new enhanced device detection service
2025-07-04 13:37:12.841 [Information] EnhancedVocomDeviceDetection: Starting comprehensive Vocom device detection
2025-07-04 13:37:12.841 [Information] EnhancedVocomDeviceDetection: Detecting USB Vocom devices
2025-07-04 13:37:13.010 [Information] EnhancedVocomDeviceDetection: Found 0 potential USB Vocom devices
2025-07-04 13:37:13.010 [Information] EnhancedVocomDeviceDetection: WiFi Vocom device detection not yet implemented
2025-07-04 13:37:13.010 [Information] EnhancedVocomDeviceDetection: Bluetooth Vocom device detection not yet implemented
2025-07-04 13:37:13.010 [Information] EnhancedVocomDeviceDetection: Detected 0 valid Vocom devices
2025-07-04 13:37:13.010 [Information] VocomService: Using original enhanced Vocom device detector for real hardware
2025-07-04 13:37:13.011 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-04 13:37:13.013 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-04 13:37:13.014 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-04 13:37:13.167 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-04 13:37:13.168 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-04 13:37:13.169 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-04 13:37:13.170 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-04 13:37:13.170 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-04 13:37:13.170 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-04 13:37:13.171 [Information] VocomService: Original enhanced detector found 1 real Vocom devices
2025-07-04 13:37:13.171 [Debug] VocomService: Checking if Bluetooth is enabled
2025-07-04 13:37:13.172 [Debug] VocomService: Bluetooth is enabled
2025-07-04 13:37:13.173 [Debug] VocomService: Checking if WiFi is available
2025-07-04 13:37:13.174 [Debug] VocomService: WiFi is available
2025-07-04 13:37:13.174 [Information] VocomService: Found 3 Vocom devices
2025-07-04 13:37:13.175 [Information] MainViewModel: Found 3 Vocom device(s)
