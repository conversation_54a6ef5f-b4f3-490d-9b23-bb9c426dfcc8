Log started at 7/3/2025 7:30:30 AM
2025-07-03 07:30:30.373 [Information] LoggingService: Logging service initialized
2025-07-03 07:30:30.383 [Information] App: Starting integrated application initialization
2025-07-03 07:30:30.385 [Information] IntegratedStartupService: === Starting Integrated Application Initialization ===
2025-07-03 07:30:30.386 [Information] IntegratedStartupService: Setting up application environment
2025-07-03 07:30:30.386 [Information] IntegratedStartupService: Application environment setup completed
2025-07-03 07:30:30.387 [Information] IntegratedStartupService: Bundling Visual C++ Redistributable libraries
2025-07-03 07:30:30.389 [Information] VCRedistBundler: Starting Visual C++ Redistributable bundling
2025-07-03 07:30:30.390 [Information] VCRedistBundler: Copying system Visual C++ Redistributable libraries
2025-07-03 07:30:30.666 [Warning] VCRedistBundler: Required Visual C++ library not found in system: msvcr140.dll
2025-07-03 07:30:30.717 [Information] VCRedistBundler: Copied api-ms-win-crt-runtime-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:30:30.760 [Information] VCRedistBundler: Copied api-ms-win-crt-heap-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:30:30.803 [Information] VCRedistBundler: Copied api-ms-win-crt-string-l1-1-0.dll from C:\Windows\SysWOW64\downlevel\api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:30:30.804 [Information] VCRedistBundler: Extracting embedded Visual C++ Redistributable libraries
2025-07-03 07:30:30.806 [Information] VCRedistBundler: Verifying Visual C++ Redistributable bundling
2025-07-03 07:30:30.806 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcr120.dll
2025-07-03 07:30:30.817 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp120.dll
2025-07-03 07:30:30.825 [Warning] VCRedistBundler: Library exists but failed to load: msvcp120.dll
2025-07-03 07:30:30.825 [Warning] VCRedistBundler: ✗ Missing VC++ library: msvcr140.dll
2025-07-03 07:30:30.825 [Information] VCRedistBundler: ✓ Verified VC++ library: msvcp140.dll
2025-07-03 07:30:30.832 [Warning] VCRedistBundler: Library exists but failed to load: msvcp140.dll
2025-07-03 07:30:30.833 [Information] VCRedistBundler: ✓ Verified VC++ library: vcruntime140.dll
2025-07-03 07:30:30.837 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:30:30.842 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:30:30.842 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:30:30.846 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:30:30.847 [Information] VCRedistBundler: ✓ Verified VC++ library: api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:30:30.851 [Warning] VCRedistBundler: Library exists but failed to load: api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:30:30.853 [Information] VCRedistBundler: VC++ Redistributable verification: 7/8 (87.5%) required libraries found
2025-07-03 07:30:30.854 [Information] VCRedistBundler: Visual C++ Redistributable bundling completed. Success: True
2025-07-03 07:30:30.856 [Information] IntegratedStartupService: VC++ Redistributable bundling status: 7 available, 1 missing
2025-07-03 07:30:30.856 [Warning] IntegratedStartupService: Missing VC++ libraries: msvcr140.dll (Microsoft Visual C++ 2015-2022 Runtime (x86))
2025-07-03 07:30:30.857 [Information] IntegratedStartupService: Extracting and verifying libraries
2025-07-03 07:30:30.858 [Information] LibraryExtractor: Starting library extraction process
2025-07-03 07:30:30.860 [Information] LibraryExtractor: Extracting embedded libraries
2025-07-03 07:30:30.862 [Information] LibraryExtractor: Copying system libraries
2025-07-03 07:30:30.895 [Information] LibraryExtractor: Checking for missing libraries to download
2025-07-03 07:30:30.927 [Information] LibraryExtractor: Downloading missing library: msvcr120.dll
2025-07-03 07:30:46.331 [Information] LibraryExtractor: Downloading missing library: msvcr140.dll
2025-07-03 07:31:05.894 [Information] LibraryExtractor: Downloading missing library: msvcp140.dll
2025-07-03 07:31:38.996 [Information] LibraryExtractor: Verifying library extraction
2025-07-03 07:31:38.996 [Information] LibraryExtractor: ✓ Verified: WUDFPuma.dll
2025-07-03 07:31:38.997 [Information] LibraryExtractor: ✓ Verified: apci.dll
2025-07-03 07:31:38.997 [Information] LibraryExtractor: ✓ Verified: Volvo.ApciPlus.dll
2025-07-03 07:31:38.997 [Information] LibraryExtractor: Library verification: 3/3 (100.0%) required libraries found
2025-07-03 07:31:38.998 [Information] LibraryExtractor: Library extraction completed. Success: True
2025-07-03 07:31:39.001 [Information] IntegratedStartupService: Library extraction status: 3 available, 0 missing
2025-07-03 07:31:39.003 [Information] IntegratedStartupService: Initializing dependency manager
2025-07-03 07:31:39.004 [Information] DependencyManager: Initializing dependency manager
2025-07-03 07:31:39.004 [Information] DependencyManager: Setting up library search paths
2025-07-03 07:31:39.005 [Information] DependencyManager: Added library path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:31:39.006 [Information] DependencyManager: Added driver path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-03 07:31:39.006 [Information] DependencyManager: Added application path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-03 07:31:39.006 [Information] DependencyManager: Updated PATH environment variable
2025-07-03 07:31:39.007 [Information] DependencyManager: Verifying required directories
2025-07-03 07:31:39.008 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:31:39.008 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-03 07:31:39.008 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\System
2025-07-03 07:31:39.008 [Information] DependencyManager: ✓ Directory exists: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-03 07:31:39.009 [Information] DependencyManager: Loading Visual C++ Redistributable libraries
2025-07-03 07:31:39.017 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcr120.dll: Error 193
2025-07-03 07:31:39.022 [Warning] DependencyManager: Failed to load VC++ Redistributable library msvcp120.dll: Error 193
2025-07-03 07:31:39.023 [Warning] DependencyManager: VC++ Redistributable library not found: msvcr140.dll
2025-07-03 07:31:39.036 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-03 07:31:39.037 [Information] DependencyManager: ✓ Loaded VC++ Redistributable library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-03 07:31:39.037 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:31:39.038 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-heap-l1-1-0.dll
2025-07-03 07:31:39.038 [Warning] DependencyManager: VC++ Redistributable library not found: api-ms-win-crt-string-l1-1-0.dll
2025-07-03 07:31:39.039 [Information] DependencyManager: Loading critical Vocom libraries
2025-07-03 07:31:39.044 [Information] DependencyManager: ✓ Loaded Critical library: WUDFPuma.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\WUDFPuma.dll
2025-07-03 07:31:39.110 [Warning] DependencyManager: Failed to load Critical library apci.dll: Error 193
2025-07-03 07:31:39.156 [Warning] DependencyManager: Failed to load Critical library apcidb.dll: Error 193
2025-07-03 07:31:39.382 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlus.dll: Error 193
2025-07-03 07:31:39.473 [Warning] DependencyManager: Failed to load Critical library Volvo.ApciPlusData.dll: Error 193
2025-07-03 07:31:39.522 [Information] DependencyManager: ✓ Loaded Critical library: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries\PhoenixGeneral.dll
2025-07-03 07:31:39.523 [Warning] DependencyManager: Failed to load Critical library msvcr120.dll: Error 193
2025-07-03 07:31:39.524 [Warning] DependencyManager: Failed to load Critical library msvcp120.dll: Error 193
2025-07-03 07:31:39.524 [Warning] DependencyManager: Critical library not found: msvcr140.dll
2025-07-03 07:31:39.524 [Information] DependencyManager: ✓ Loaded Critical library: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-03 07:31:39.525 [Information] DependencyManager: ✓ Loaded Critical library: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-03 07:31:39.525 [Information] DependencyManager: Setting up environment variables
2025-07-03 07:31:39.525 [Information] DependencyManager: Environment variables configured
2025-07-03 07:31:39.526 [Information] DependencyManager: Verifying library loading status
2025-07-03 07:31:39.635 [Information] DependencyManager: Library loading verification: 4/11 (36.4%) critical libraries loaded
2025-07-03 07:31:39.636 [Warning] DependencyManager: Low library loading success rate - some functionality may be limited
2025-07-03 07:31:39.636 [Information] DependencyManager: Dependency manager initialized successfully
2025-07-03 07:31:39.638 [Information] IntegratedStartupService: Dependency status: 6 found, 5 missing
2025-07-03 07:31:39.638 [Information] IntegratedStartupService: Setting up Vocom-specific environment
2025-07-03 07:31:39.641 [Information] IntegratedStartupService: Vocom environment setup completed
2025-07-03 07:31:39.642 [Information] IntegratedStartupService: Verifying system readiness
2025-07-03 07:31:39.643 [Information] IntegratedStartupService: System readiness verification passed
2025-07-03 07:31:39.643 [Information] IntegratedStartupService: === Integrated Application Initialization Complete ===
2025-07-03 07:31:39.644 [Information] IntegratedStartupService: === System Status Summary ===
2025-07-03 07:31:39.644 [Information] IntegratedStartupService: Application Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application
2025-07-03 07:31:39.644 [Information] IntegratedStartupService: Libraries Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:31:39.644 [Information] IntegratedStartupService: Drivers Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom
2025-07-03 07:31:39.645 [Information] IntegratedStartupService: Environment Variable USE_PATCHED_IMPLEMENTATION: true
2025-07-03 07:31:39.645 [Information] IntegratedStartupService: Environment Variable PHOENIX_VOCOM_ENABLED: true
2025-07-03 07:31:39.645 [Information] IntegratedStartupService: Environment Variable VERBOSE_LOGGING: true
2025-07-03 07:31:39.645 [Information] IntegratedStartupService: Environment Variable APCI_LIBRARY_PATH: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:31:39.646 [Information] IntegratedStartupService: === End System Status Summary ===
2025-07-03 07:31:39.646 [Information] App: Integrated startup completed successfully
2025-07-03 07:31:39.647 [Information] App: System Status - Libraries: 3 available, Dependencies: 6 loaded
2025-07-03 07:31:39.657 [Information] App: Initializing application services
2025-07-03 07:31:39.659 [Information] AppConfigurationService: Initializing configuration service
2025-07-03 07:31:39.660 [Information] AppConfigurationService: Created configuration directory: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config
2025-07-03 07:31:39.697 [Information] AppConfigurationService: Configuration loaded from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-03 07:31:39.698 [Information] AppConfigurationService: Configuration service initialized successfully
2025-07-03 07:31:39.698 [Information] App: Configuration service initialized successfully
2025-07-03 07:31:39.700 [Information] App: Configuration value for Application.UseDummyImplementations: False
2025-07-03 07:31:39.700 [Information] App: Environment variable USE_DUMMY_IMPLEMENTATIONS: 'false'
2025-07-03 07:31:39.704 [Information] App: Environment variable exists: True, not 'false': False
2025-07-03 07:31:39.704 [Information] App: Final useDummyImplementations value: False
2025-07-03 07:31:39.705 [Information] App: Updating config to NOT use dummy implementations
2025-07-03 07:31:39.715 [Information] AppConfigurationService: Configuration saved to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Config\app_config.json
2025-07-03 07:31:39.715 [Information] App: USE_PATCHED_IMPLEMENTATION environment variable is set to: 'true'
2025-07-03 07:31:39.716 [Information] App: usePatchedImplementation flag is: True
2025-07-03 07:31:39.716 [Information] App: PHOENIX_VOCOM_ENABLED environment variable is set to: 'true'
2025-07-03 07:31:39.716 [Information] App: APCI_LIBRARY_PATH environment variable is set to: 'C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries'
2025-07-03 07:31:39.716 [Information] App: VERBOSE_LOGGING environment variable is set to: 'true'
2025-07-03 07:31:39.716 [Information] App: verboseLogging flag is: True
2025-07-03 07:31:39.718 [Information] App: Verifying real hardware requirements...
2025-07-03 07:31:39.719 [Information] App: ✓ Found critical library: WUDFPuma.dll
2025-07-03 07:31:39.719 [Information] App: ✓ Found critical library: apci.dll
2025-07-03 07:31:39.720 [Information] App: ✓ Found critical library: Volvo.ApciPlus.dll
2025-07-03 07:31:39.720 [Information] App: ✓ Found critical library: Volvo.ApciPlusData.dll
2025-07-03 07:31:39.720 [Information] App: ✓ Found system Vocom driver: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:31:39.721 [Information] App: ✓ Found Phoenix Diag installation: C:\Program Files (x86)\Phoenix Diag\Flash Editor Plus 2021
2025-07-03 07:31:39.721 [Information] App: ✓ Found Vocom driver config: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Vocom\config.json
2025-07-03 07:31:39.721 [Information] App: ✓ All critical real hardware requirements verified successfully
2025-07-03 07:31:39.733 [Information] App: *** ATTEMPTING TO CREATE PATCHED VOCOM SERVICE FACTORY ***
2025-07-03 07:31:39.734 [Information] App: Found PatchedVocomServiceFactory type: VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
2025-07-03 07:31:39.735 [Information] PatchedVocomServiceFactory: *** PatchedVocomServiceFactory initialized ***
2025-07-03 07:31:39.736 [Information] PatchedVocomServiceFactory: Assembly: VolvoFlashWR.Communication, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null
2025-07-03 07:31:39.737 [Information] PatchedVocomServiceFactory: Assembly location: 
2025-07-03 07:31:39.757 [Information] PatchedVocomServiceFactory: Patched types in assembly:
- VolvoFlashWR.Communication.Vocom.PatchedVocomDeviceDriver
- VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory
- VolvoFlashWR.Communication.Vocom.VocomNativeInterop_Patch

2025-07-03 07:31:39.757 [Information] PatchedVocomServiceFactory: Created marker file at C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\patched_factory_created.txt
2025-07-03 07:31:39.757 [Information] App: Successfully created PatchedVocomServiceFactory instance using reflection
2025-07-03 07:31:39.758 [Information] App: Using VolvoFlashWR.Communication.Vocom.PatchedVocomServiceFactory Vocom service factory
2025-07-03 07:31:39.758 [Information] App: Checking if PTT application is running before creating Vocom service
2025-07-03 07:31:39.789 [Information] App: Creating Vocom service (attempt 1/3)
2025-07-03 07:31:39.791 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: Creating patched Vocom service with default settings
2025-07-03 07:31:39.792 [Information] PatchedVocomServiceFactory: PATCHED IMPLEMENTATION: This log message confirms the patched implementation is being used
2025-07-03 07:31:39.792 [Information] PatchedVocomServiceFactory: PHOENIX_VOCOM_ENABLED environment variable: 'true'
2025-07-03 07:31:39.792 [Information] PatchedVocomServiceFactory: usePhoenixAdapter flag: True
2025-07-03 07:31:39.793 [Information] PatchedVocomServiceFactory: Phoenix Vocom adapter enabled, attempting to create it
2025-07-03 07:31:39.794 [Information] PhoenixVocomAdapter: Initializing Phoenix Vocom adapter
2025-07-03 07:31:39.795 [Information] PhoenixVocomAdapter: Checking for required Phoenix libraries
2025-07-03 07:31:39.798 [Information] PhoenixVocomAdapter: Found 3 Vodia libraries
2025-07-03 07:31:39.799 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 07:31:39.800 [Information] PhoenixVocomAdapter: Required libraries check completed. Found 4/4 critical libraries, 3 Vodia libraries, 102 System libraries
2025-07-03 07:31:39.806 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apci.dll
2025-07-03 07:31:39.808 [Information] PhoenixVocomAdapter: Copied apci.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:31:39.810 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\apcidb.dll
2025-07-03 07:31:39.812 [Information] PhoenixVocomAdapter: Copied apcidb.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apcidb.dll
2025-07-03 07:31:39.818 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlus.dll
2025-07-03 07:31:39.821 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll
2025-07-03 07:31:39.823 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusData.dll
2025-07-03 07:31:39.825 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll
2025-07-03 07:31:39.826 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.ApciPlusTea2Data.dll
2025-07-03 07:31:39.827 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusTea2Data.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusTea2Data.dll
2025-07-03 07:31:39.829 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interface.dll
2025-07-03 07:31:39.830 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interface.dll
2025-07-03 07:31:39.832 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-03 07:31:39.833 [Information] PhoenixVocomAdapter: Copied Volvo.NAMS.AC.Services.Interfaces.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NAMS.AC.Services.Interfaces.dll
2025-07-03 07:31:39.835 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Core.dll
2025-07-03 07:31:39.836 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Core.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Core.dll
2025-07-03 07:31:39.837 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Logging.dll
2025-07-03 07:31:39.838 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Logging.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Logging.dll
2025-07-03 07:31:39.840 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.dll
2025-07-03 07:31:39.841 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.dll
2025-07-03 07:31:39.842 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Volvo.NVS.Persistence.NHibernate.dll
2025-07-03 07:31:39.843 [Information] PhoenixVocomAdapter: Copied Volvo.NVS.Persistence.NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.NVS.Persistence.NHibernate.dll
2025-07-03 07:31:39.845 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Baf.Utility.dll
2025-07-03 07:31:39.846 [Information] PhoenixVocomAdapter: Copied VolvoIt.Baf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Baf.Utility.dll
2025-07-03 07:31:39.847 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-03 07:31:39.848 [Information] PhoenixVocomAdapter: Copied VolvoIt.Fido.Agent.Gateway.Contract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Fido.Agent.Gateway.Contract.dll
2025-07-03 07:31:39.849 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.ServiceContract.dll
2025-07-03 07:31:39.851 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.ServiceContract.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.ServiceContract.dll
2025-07-03 07:31:39.852 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\VolvoIt.Waf.Utility.dll
2025-07-03 07:31:39.853 [Information] PhoenixVocomAdapter: Copied VolvoIt.Waf.Utility.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoIt.Waf.Utility.dll
2025-07-03 07:31:39.854 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlus.dll.config
2025-07-03 07:31:39.855 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlus.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlus.dll.config
2025-07-03 07:31:39.857 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Config\Volvo.ApciPlusData.dll.config
2025-07-03 07:31:39.858 [Information] PhoenixVocomAdapter: Copied Volvo.ApciPlusData.dll.config to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Volvo.ApciPlusData.dll.config
2025-07-03 07:31:39.863 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.dll
2025-07-03 07:31:39.866 [Information] PhoenixVocomAdapter: Copied NHibernate.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.dll
2025-07-03 07:31:39.867 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\NHibernate.Caches.SysCache2.dll
2025-07-03 07:31:39.868 [Information] PhoenixVocomAdapter: Copied NHibernate.Caches.SysCache2.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\NHibernate.Caches.SysCache2.dll
2025-07-03 07:31:39.870 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Iesi.Collections.dll
2025-07-03 07:31:39.871 [Information] PhoenixVocomAdapter: Copied Iesi.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Iesi.Collections.dll
2025-07-03 07:31:39.872 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Ionic.Zip.Reduced.dll
2025-07-03 07:31:39.873 [Information] PhoenixVocomAdapter: Copied Ionic.Zip.Reduced.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Ionic.Zip.Reduced.dll
2025-07-03 07:31:39.875 [Information] PhoenixVocomAdapter: Copied SharpCompress.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\SharpCompress.dll
2025-07-03 07:31:39.878 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\DotNetZip.dll
2025-07-03 07:31:39.879 [Information] PhoenixVocomAdapter: Copied DotNetZip.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\DotNetZip.dll
2025-07-03 07:31:39.881 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\ICSharpCode.SharpZipLib.dll
2025-07-03 07:31:39.882 [Information] PhoenixVocomAdapter: Copied ICSharpCode.SharpZipLib.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\ICSharpCode.SharpZipLib.dll
2025-07-03 07:31:39.883 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.CommonDomain.Model.dll
2025-07-03 07:31:39.884 [Information] PhoenixVocomAdapter: Copied Vodia.CommonDomain.Model.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.CommonDomain.Model.dll
2025-07-03 07:31:39.886 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.Contracts.Common.dll
2025-07-03 07:31:39.888 [Information] PhoenixVocomAdapter: Copied Vodia.Contracts.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.Contracts.Common.dll
2025-07-03 07:31:39.889 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Vodia.UtilityComponent.dll
2025-07-03 07:31:39.890 [Information] PhoenixVocomAdapter: Copied Vodia.UtilityComponent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Vodia.UtilityComponent.dll
2025-07-03 07:31:39.892 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\log4net.dll
2025-07-03 07:31:39.893 [Information] PhoenixVocomAdapter: Copied log4net.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\log4net.dll
2025-07-03 07:31:39.895 [Information] PhoenixVocomAdapter: Copied Newtonsoft.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\Newtonsoft.Json.dll
2025-07-03 07:31:39.897 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\Dependencies\AutoMapper.dll
2025-07-03 07:31:39.898 [Information] PhoenixVocomAdapter: Copied AutoMapper.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\AutoMapper.dll
2025-07-03 07:31:39.899 [Information] PhoenixVocomAdapter: Found 102 System libraries
2025-07-03 07:31:39.901 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.AppContext.dll
2025-07-03 07:31:39.903 [Information] PhoenixVocomAdapter: Copied System.AppContext.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.AppContext.dll
2025-07-03 07:31:39.904 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Buffers.dll
2025-07-03 07:31:39.905 [Information] PhoenixVocomAdapter: Copied System.Buffers.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Buffers.dll
2025-07-03 07:31:39.906 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Concurrent.dll
2025-07-03 07:31:39.907 [Information] PhoenixVocomAdapter: Copied System.Collections.Concurrent.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Concurrent.dll
2025-07-03 07:31:39.908 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.dll
2025-07-03 07:31:39.909 [Information] PhoenixVocomAdapter: Copied System.Collections.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.dll
2025-07-03 07:31:39.910 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.NonGeneric.dll
2025-07-03 07:31:39.911 [Information] PhoenixVocomAdapter: Copied System.Collections.NonGeneric.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.NonGeneric.dll
2025-07-03 07:31:39.913 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Collections.Specialized.dll
2025-07-03 07:31:39.913 [Information] PhoenixVocomAdapter: Copied System.Collections.Specialized.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Collections.Specialized.dll
2025-07-03 07:31:39.915 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.dll
2025-07-03 07:31:39.915 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.dll
2025-07-03 07:31:39.917 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.EventBasedAsync.dll
2025-07-03 07:31:39.917 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.EventBasedAsync.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.EventBasedAsync.dll
2025-07-03 07:31:39.919 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.Primitives.dll
2025-07-03 07:31:39.920 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.Primitives.dll
2025-07-03 07:31:39.921 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ComponentModel.TypeConverter.dll
2025-07-03 07:31:39.922 [Information] PhoenixVocomAdapter: Copied System.ComponentModel.TypeConverter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ComponentModel.TypeConverter.dll
2025-07-03 07:31:39.925 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Console.dll
2025-07-03 07:31:39.926 [Information] PhoenixVocomAdapter: Copied System.Console.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Console.dll
2025-07-03 07:31:39.928 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.Common.dll
2025-07-03 07:31:39.929 [Information] PhoenixVocomAdapter: Copied System.Data.Common.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.Common.dll
2025-07-03 07:31:39.931 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SQLite.dll
2025-07-03 07:31:39.932 [Information] PhoenixVocomAdapter: Copied System.Data.SQLite.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SQLite.dll
2025-07-03 07:31:39.933 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Data.SqlServerCe.dll
2025-07-03 07:31:39.935 [Information] PhoenixVocomAdapter: Copied System.Data.SqlServerCe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Data.SqlServerCe.dll
2025-07-03 07:31:39.936 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Contracts.dll
2025-07-03 07:31:39.937 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Contracts.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Contracts.dll
2025-07-03 07:31:39.938 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Debug.dll
2025-07-03 07:31:39.938 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Debug.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Debug.dll
2025-07-03 07:31:39.940 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.FileVersionInfo.dll
2025-07-03 07:31:39.941 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.FileVersionInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.FileVersionInfo.dll
2025-07-03 07:31:39.943 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Process.dll
2025-07-03 07:31:39.943 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Process.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Process.dll
2025-07-03 07:31:39.945 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.StackTrace.dll
2025-07-03 07:31:39.946 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.StackTrace.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.StackTrace.dll
2025-07-03 07:31:39.947 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TextWriterTraceListener.dll
2025-07-03 07:31:39.947 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TextWriterTraceListener.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TextWriterTraceListener.dll
2025-07-03 07:31:39.948 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tools.dll
2025-07-03 07:31:39.949 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tools.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tools.dll
2025-07-03 07:31:39.950 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.TraceSource.dll
2025-07-03 07:31:39.951 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.TraceSource.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.TraceSource.dll
2025-07-03 07:31:39.953 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Diagnostics.Tracing.dll
2025-07-03 07:31:39.954 [Information] PhoenixVocomAdapter: Copied System.Diagnostics.Tracing.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Diagnostics.Tracing.dll
2025-07-03 07:31:39.955 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Drawing.Primitives.dll
2025-07-03 07:31:39.957 [Information] PhoenixVocomAdapter: Copied System.Drawing.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Drawing.Primitives.dll
2025-07-03 07:31:39.958 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Dynamic.Runtime.dll
2025-07-03 07:31:39.959 [Information] PhoenixVocomAdapter: Copied System.Dynamic.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Dynamic.Runtime.dll
2025-07-03 07:31:39.961 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Calendars.dll
2025-07-03 07:31:39.961 [Information] PhoenixVocomAdapter: Copied System.Globalization.Calendars.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Calendars.dll
2025-07-03 07:31:39.962 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.dll
2025-07-03 07:31:39.963 [Information] PhoenixVocomAdapter: Copied System.Globalization.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.dll
2025-07-03 07:31:39.964 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Globalization.Extensions.dll
2025-07-03 07:31:39.965 [Information] PhoenixVocomAdapter: Copied System.Globalization.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Globalization.Extensions.dll
2025-07-03 07:31:39.966 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.dll
2025-07-03 07:31:39.967 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.dll
2025-07-03 07:31:39.969 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Compression.ZipFile.dll
2025-07-03 07:31:39.970 [Information] PhoenixVocomAdapter: Copied System.IO.Compression.ZipFile.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Compression.ZipFile.dll
2025-07-03 07:31:39.971 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.dll
2025-07-03 07:31:39.972 [Information] PhoenixVocomAdapter: Copied System.IO.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.dll
2025-07-03 07:31:39.973 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.dll
2025-07-03 07:31:39.974 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.dll
2025-07-03 07:31:39.975 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.DriveInfo.dll
2025-07-03 07:31:39.976 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.DriveInfo.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.DriveInfo.dll
2025-07-03 07:31:39.977 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Primitives.dll
2025-07-03 07:31:39.977 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Primitives.dll
2025-07-03 07:31:39.978 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.FileSystem.Watcher.dll
2025-07-03 07:31:39.979 [Information] PhoenixVocomAdapter: Copied System.IO.FileSystem.Watcher.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.FileSystem.Watcher.dll
2025-07-03 07:31:39.980 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.IsolatedStorage.dll
2025-07-03 07:31:39.980 [Information] PhoenixVocomAdapter: Copied System.IO.IsolatedStorage.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.IsolatedStorage.dll
2025-07-03 07:31:39.981 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.MemoryMappedFiles.dll
2025-07-03 07:31:39.982 [Information] PhoenixVocomAdapter: Copied System.IO.MemoryMappedFiles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.MemoryMappedFiles.dll
2025-07-03 07:31:39.983 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.Pipes.dll
2025-07-03 07:31:39.983 [Information] PhoenixVocomAdapter: Copied System.IO.Pipes.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.Pipes.dll
2025-07-03 07:31:39.985 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.IO.UnmanagedMemoryStream.dll
2025-07-03 07:31:39.985 [Information] PhoenixVocomAdapter: Copied System.IO.UnmanagedMemoryStream.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.IO.UnmanagedMemoryStream.dll
2025-07-03 07:31:39.987 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.dll
2025-07-03 07:31:39.988 [Information] PhoenixVocomAdapter: Copied System.Linq.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.dll
2025-07-03 07:31:39.990 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Expressions.dll
2025-07-03 07:31:39.991 [Information] PhoenixVocomAdapter: Copied System.Linq.Expressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Expressions.dll
2025-07-03 07:31:39.993 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Parallel.dll
2025-07-03 07:31:39.994 [Information] PhoenixVocomAdapter: Copied System.Linq.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Parallel.dll
2025-07-03 07:31:39.995 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Linq.Queryable.dll
2025-07-03 07:31:39.996 [Information] PhoenixVocomAdapter: Copied System.Linq.Queryable.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Linq.Queryable.dll
2025-07-03 07:31:39.997 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Memory.dll
2025-07-03 07:31:39.998 [Information] PhoenixVocomAdapter: Copied System.Memory.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Memory.dll
2025-07-03 07:31:40.000 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Http.dll
2025-07-03 07:31:40.001 [Information] PhoenixVocomAdapter: Copied System.Net.Http.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Http.dll
2025-07-03 07:31:40.003 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NameResolution.dll
2025-07-03 07:31:40.004 [Information] PhoenixVocomAdapter: Copied System.Net.NameResolution.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NameResolution.dll
2025-07-03 07:31:40.005 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.NetworkInformation.dll
2025-07-03 07:31:40.006 [Information] PhoenixVocomAdapter: Copied System.Net.NetworkInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.NetworkInformation.dll
2025-07-03 07:31:40.007 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Ping.dll
2025-07-03 07:31:40.008 [Information] PhoenixVocomAdapter: Copied System.Net.Ping.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Ping.dll
2025-07-03 07:31:40.009 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Primitives.dll
2025-07-03 07:31:40.010 [Information] PhoenixVocomAdapter: Copied System.Net.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Primitives.dll
2025-07-03 07:31:40.011 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Requests.dll
2025-07-03 07:31:40.012 [Information] PhoenixVocomAdapter: Copied System.Net.Requests.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Requests.dll
2025-07-03 07:31:40.013 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Security.dll
2025-07-03 07:31:40.014 [Information] PhoenixVocomAdapter: Copied System.Net.Security.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Security.dll
2025-07-03 07:31:40.015 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.Sockets.dll
2025-07-03 07:31:40.016 [Information] PhoenixVocomAdapter: Copied System.Net.Sockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.Sockets.dll
2025-07-03 07:31:40.017 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebHeaderCollection.dll
2025-07-03 07:31:40.018 [Information] PhoenixVocomAdapter: Copied System.Net.WebHeaderCollection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebHeaderCollection.dll
2025-07-03 07:31:40.019 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.Client.dll
2025-07-03 07:31:40.020 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.Client.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.Client.dll
2025-07-03 07:31:40.021 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Net.WebSockets.dll
2025-07-03 07:31:40.022 [Information] PhoenixVocomAdapter: Copied System.Net.WebSockets.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Net.WebSockets.dll
2025-07-03 07:31:40.023 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Numerics.Vectors.dll
2025-07-03 07:31:40.024 [Information] PhoenixVocomAdapter: Copied System.Numerics.Vectors.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Numerics.Vectors.dll
2025-07-03 07:31:40.026 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ObjectModel.dll
2025-07-03 07:31:40.027 [Information] PhoenixVocomAdapter: Copied System.ObjectModel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ObjectModel.dll
2025-07-03 07:31:40.028 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.dll
2025-07-03 07:31:40.029 [Information] PhoenixVocomAdapter: Copied System.Reflection.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.dll
2025-07-03 07:31:40.030 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Extensions.dll
2025-07-03 07:31:40.031 [Information] PhoenixVocomAdapter: Copied System.Reflection.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Extensions.dll
2025-07-03 07:31:40.033 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Reflection.Primitives.dll
2025-07-03 07:31:40.034 [Information] PhoenixVocomAdapter: Copied System.Reflection.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Reflection.Primitives.dll
2025-07-03 07:31:40.035 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Reader.dll
2025-07-03 07:31:40.036 [Information] PhoenixVocomAdapter: Copied System.Resources.Reader.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Reader.dll
2025-07-03 07:31:40.037 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.ResourceManager.dll
2025-07-03 07:31:40.038 [Information] PhoenixVocomAdapter: Copied System.Resources.ResourceManager.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.ResourceManager.dll
2025-07-03 07:31:40.039 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Resources.Writer.dll
2025-07-03 07:31:40.040 [Information] PhoenixVocomAdapter: Copied System.Resources.Writer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Resources.Writer.dll
2025-07-03 07:31:40.042 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.Unsafe.dll
2025-07-03 07:31:40.043 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.Unsafe.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.Unsafe.dll
2025-07-03 07:31:40.045 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.CompilerServices.VisualC.dll
2025-07-03 07:31:40.046 [Information] PhoenixVocomAdapter: Copied System.Runtime.CompilerServices.VisualC.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.CompilerServices.VisualC.dll
2025-07-03 07:31:40.047 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.dll
2025-07-03 07:31:40.048 [Information] PhoenixVocomAdapter: Copied System.Runtime.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.dll
2025-07-03 07:31:40.049 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Extensions.dll
2025-07-03 07:31:40.051 [Information] PhoenixVocomAdapter: Copied System.Runtime.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Extensions.dll
2025-07-03 07:31:40.052 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Handles.dll
2025-07-03 07:31:40.053 [Information] PhoenixVocomAdapter: Copied System.Runtime.Handles.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Handles.dll
2025-07-03 07:31:40.054 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.dll
2025-07-03 07:31:40.055 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.dll
2025-07-03 07:31:40.056 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-03 07:31:40.056 [Information] PhoenixVocomAdapter: Copied System.Runtime.InteropServices.RuntimeInformation.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.InteropServices.RuntimeInformation.dll
2025-07-03 07:31:40.057 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Numerics.dll
2025-07-03 07:31:40.058 [Information] PhoenixVocomAdapter: Copied System.Runtime.Numerics.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Numerics.dll
2025-07-03 07:31:40.059 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Formatters.dll
2025-07-03 07:31:40.060 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Formatters.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Formatters.dll
2025-07-03 07:31:40.061 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Json.dll
2025-07-03 07:31:40.061 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Json.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Json.dll
2025-07-03 07:31:40.062 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Primitives.dll
2025-07-03 07:31:40.063 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Primitives.dll
2025-07-03 07:31:40.064 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Runtime.Serialization.Xml.dll
2025-07-03 07:31:40.065 [Information] PhoenixVocomAdapter: Copied System.Runtime.Serialization.Xml.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Runtime.Serialization.Xml.dll
2025-07-03 07:31:40.066 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Claims.dll
2025-07-03 07:31:40.067 [Information] PhoenixVocomAdapter: Copied System.Security.Claims.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Claims.dll
2025-07-03 07:31:40.068 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Algorithms.dll
2025-07-03 07:31:40.069 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Algorithms.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Algorithms.dll
2025-07-03 07:31:40.070 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Csp.dll
2025-07-03 07:31:40.071 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Csp.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Csp.dll
2025-07-03 07:31:40.073 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Encoding.dll
2025-07-03 07:31:40.075 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Encoding.dll
2025-07-03 07:31:40.077 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.Primitives.dll
2025-07-03 07:31:40.078 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.Primitives.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.Primitives.dll
2025-07-03 07:31:40.080 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Cryptography.X509Certificates.dll
2025-07-03 07:31:40.081 [Information] PhoenixVocomAdapter: Copied System.Security.Cryptography.X509Certificates.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Cryptography.X509Certificates.dll
2025-07-03 07:31:40.083 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.Principal.dll
2025-07-03 07:31:40.084 [Information] PhoenixVocomAdapter: Copied System.Security.Principal.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.Principal.dll
2025-07-03 07:31:40.086 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Security.SecureString.dll
2025-07-03 07:31:40.087 [Information] PhoenixVocomAdapter: Copied System.Security.SecureString.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Security.SecureString.dll
2025-07-03 07:31:40.089 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.dll
2025-07-03 07:31:40.090 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.dll
2025-07-03 07:31:40.091 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.Encoding.Extensions.dll
2025-07-03 07:31:40.093 [Information] PhoenixVocomAdapter: Copied System.Text.Encoding.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.Encoding.Extensions.dll
2025-07-03 07:31:40.094 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Text.RegularExpressions.dll
2025-07-03 07:31:40.095 [Information] PhoenixVocomAdapter: Copied System.Text.RegularExpressions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Text.RegularExpressions.dll
2025-07-03 07:31:40.097 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.dll
2025-07-03 07:31:40.098 [Information] PhoenixVocomAdapter: Copied System.Threading.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.dll
2025-07-03 07:31:40.099 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Overlapped.dll
2025-07-03 07:31:40.100 [Information] PhoenixVocomAdapter: Copied System.Threading.Overlapped.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Overlapped.dll
2025-07-03 07:31:40.102 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.dll
2025-07-03 07:31:40.103 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.dll
2025-07-03 07:31:40.107 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Extensions.dll
2025-07-03 07:31:40.108 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Extensions.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Extensions.dll
2025-07-03 07:31:40.111 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Tasks.Parallel.dll
2025-07-03 07:31:40.112 [Information] PhoenixVocomAdapter: Copied System.Threading.Tasks.Parallel.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Tasks.Parallel.dll
2025-07-03 07:31:40.114 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Thread.dll
2025-07-03 07:31:40.115 [Information] PhoenixVocomAdapter: Copied System.Threading.Thread.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Thread.dll
2025-07-03 07:31:40.117 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.ThreadPool.dll
2025-07-03 07:31:40.118 [Information] PhoenixVocomAdapter: Copied System.Threading.ThreadPool.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.ThreadPool.dll
2025-07-03 07:31:40.120 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Threading.Timer.dll
2025-07-03 07:31:40.121 [Information] PhoenixVocomAdapter: Copied System.Threading.Timer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Threading.Timer.dll
2025-07-03 07:31:40.123 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.ValueTuple.dll
2025-07-03 07:31:40.124 [Information] PhoenixVocomAdapter: Copied System.ValueTuple.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.ValueTuple.dll
2025-07-03 07:31:40.125 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.ReaderWriter.dll
2025-07-03 07:31:40.126 [Information] PhoenixVocomAdapter: Copied System.Xml.ReaderWriter.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.ReaderWriter.dll
2025-07-03 07:31:40.127 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XDocument.dll
2025-07-03 07:31:40.128 [Information] PhoenixVocomAdapter: Copied System.Xml.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XDocument.dll
2025-07-03 07:31:40.132 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlDocument.dll
2025-07-03 07:31:40.133 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlDocument.dll
2025-07-03 07:31:40.135 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XmlSerializer.dll
2025-07-03 07:31:40.136 [Information] PhoenixVocomAdapter: Copied System.Xml.XmlSerializer.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XmlSerializer.dll
2025-07-03 07:31:40.138 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.dll
2025-07-03 07:31:40.139 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.dll
2025-07-03 07:31:40.140 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\System.Xml.XPath.XDocument.dll
2025-07-03 07:31:40.141 [Information] PhoenixVocomAdapter: Copied System.Xml.XPath.XDocument.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\System.Xml.XPath.XDocument.dll
2025-07-03 07:31:40.143 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Drivers\Phoenix\System\SystemInterface.dll
2025-07-03 07:31:40.144 [Information] PhoenixVocomAdapter: Copied SystemInterface.dll to C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\SystemInterface.dll
2025-07-03 07:31:40.144 [Information] PhoenixVocomAdapter: Copied 132 files, 0 files were missing
2025-07-03 07:31:40.145 [Information] PhoenixVocomAdapter: Loading APCI library dynamically
2025-07-03 07:31:40.250 [Error] PhoenixVocomAdapter: Failed to load APCI library. Error code: 193
2025-07-03 07:31:40.251 [Error] PhoenixVocomAdapter: Failed to load APCI library dynamically
2025-07-03 07:31:40.252 [Information] VocomDiagnosticTool: === Starting Vocom DLL Diagnostics ===
2025-07-03 07:31:40.253 [Information] VocomDiagnosticTool: --- Diagnosing APCI Library ---
2025-07-03 07:31:40.253 [Information] VocomDiagnosticTool: APCI file size: 1165312 bytes
2025-07-03 07:31:40.253 [Information] VocomDiagnosticTool: APCI last modified: 2/19/2019 5:58:52 AM
2025-07-03 07:31:40.255 [Error] VocomDiagnosticTool: Failed to load apci.dll. Error: 0 (0x0)
2025-07-03 07:31:40.256 [Information] VocomDiagnosticTool: --- Diagnosing WUDFPuma Library ---
2025-07-03 07:31:40.257 [Information] VocomDiagnosticTool: Found WUDFPuma.dll at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:31:40.257 [Information] VocomDiagnosticTool: WUDFPuma file size: 36344 bytes
2025-07-03 07:31:40.257 [Information] VocomDiagnosticTool: WUDFPuma last modified: 5/3/2017 1:34:26 PM
2025-07-03 07:31:40.258 [Information] VocomDiagnosticTool: Successfully loaded WUDFPuma.dll
2025-07-03 07:31:40.259 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Initialize
2025-07-03 07:31:40.260 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_Shutdown
2025-07-03 07:31:40.260 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DetectDevices
2025-07-03 07:31:40.260 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_ConnectDevice
2025-07-03 07:31:40.260 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_DisconnectDevice
2025-07-03 07:31:40.261 [Warning] VocomDiagnosticTool: ✗ Missing function: Vocom_SendCANFrame
2025-07-03 07:31:40.261 [Information] VocomDiagnosticTool: --- Checking System Dependencies ---
2025-07-03 07:31:40.262 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr120.dll
2025-07-03 07:31:40.263 [Warning] VocomDiagnosticTool: ✗ Missing: msvcp120.dll
2025-07-03 07:31:40.263 [Warning] VocomDiagnosticTool: ✗ Missing: msvcr140.dll
2025-07-03 07:31:40.264 [Information] VocomDiagnosticTool: ✓ Available: msvcp140.dll
2025-07-03 07:31:40.264 [Information] VocomDiagnosticTool: ✓ Available: vcruntime140.dll
2025-07-03 07:31:40.264 [Information] VocomDiagnosticTool: ✓ Available: api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:31:40.264 [Information] VocomDiagnosticTool: === Vocom DLL Diagnostics Complete ===
2025-07-03 07:31:40.265 [Warning] PatchedVocomServiceFactory: Phoenix adapter initialization failed, falling back to patched driver
2025-07-03 07:31:40.265 [Information] PatchedVocomServiceFactory: Attempting to create patched Vocom device driver
2025-07-03 07:31:40.268 [Information] PatchedVocomDeviceDriver: Initializing patched Vocom device driver
2025-07-03 07:31:40.270 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: Initializing Vocom driver (Patch)
2025-07-03 07:31:40.271 [Information] VocomNativeInterop_Patch: PATCHED IMPLEMENTATION: This log message confirms the patched VocomNativeInterop is being used
2025-07-03 07:31:40.275 [Information] VocomNativeInterop_Patch: Searching for apci.dll...
2025-07-03 07:31:40.276 [Information] VocomNativeInterop_Patch: Searching for apci.dll in 9 locations
2025-07-03 07:31:40.276 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:31:40.276 [Information] VocomNativeInterop_Patch: Found Vocom driver DLL at: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:31:40.278 [Information] VocomNativeInterop_Patch: Attempting to load common dependencies
2025-07-03 07:31:40.279 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:40.280 [Warning] VocomNativeInterop_Patch: Failed to load dependency apci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:40.280 [Warning] VocomNativeInterop_Patch: Dependency apci.dll not found in any search path
2025-07-03 07:31:40.355 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:40.356 [Warning] VocomNativeInterop_Patch: Failed to load dependency apcidb.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:40.356 [Warning] VocomNativeInterop_Patch: Dependency apcidb.dll not found in any search path
2025-07-03 07:31:40.539 [Warning] VocomNativeInterop_Patch: Failed to load dependency Rpci.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:40.540 [Warning] VocomNativeInterop_Patch: Dependency Rpci.dll not found in any search path
2025-07-03 07:31:40.623 [Warning] VocomNativeInterop_Patch: Failed to load dependency Pc2.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:40.623 [Warning] VocomNativeInterop_Patch: Dependency Pc2.dll not found in any search path
2025-07-03 07:31:40.725 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:40.725 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusData.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:40.725 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusData.dll not found in any search path
2025-07-03 07:31:40.836 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:40.912 [Warning] VocomNativeInterop_Patch: Failed to load dependency Volvo.ApciPlusTea2Data.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:40.913 [Warning] VocomNativeInterop_Patch: Dependency Volvo.ApciPlusTea2Data.dll not found in any search path
2025-07-03 07:31:40.977 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Core.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:31:41.100 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Volvo.NVS.Logging.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:31:41.197 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:41.291 [Warning] VocomNativeInterop_Patch: Failed to load dependency VolvoIt.Waf.Utility.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:41.292 [Warning] VocomNativeInterop_Patch: Dependency VolvoIt.Waf.Utility.dll not found in any search path
2025-07-03 07:31:41.292 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: PhoenixGeneral.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries
2025-07-03 07:31:41.488 [Warning] VocomNativeInterop_Patch: Failed to load dependency PhoenixESW.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:41.488 [Warning] VocomNativeInterop_Patch: Dependency PhoenixESW.dll not found in any search path
2025-07-03 07:31:41.559 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:41.625 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.CommonDomain.Model.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:41.625 [Warning] VocomNativeInterop_Patch: Dependency Vodia.CommonDomain.Model.dll not found in any search path
2025-07-03 07:31:41.740 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\, error: 0
2025-07-03 07:31:41.854 [Warning] VocomNativeInterop_Patch: Failed to load dependency Vodia.UtilityComponent.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Libraries, error: 0
2025-07-03 07:31:42.298 [Warning] VocomNativeInterop_Patch: Dependency Vodia.UtilityComponent.dll not found in any search path
2025-07-03 07:31:42.403 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: log4net.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:31:42.410 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: Newtonsoft.Json.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:31:42.457 [Information] VocomNativeInterop_Patch: Successfully loaded dependency: SystemInterface.dll from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\
2025-07-03 07:31:42.459 [Error] VocomNativeInterop_Patch: Failed to load Vocom driver DLL. Error code: 0, Message: The operation completed successfully.
2025-07-03 07:31:42.460 [Error] VocomNativeInterop_Patch: DLL Path: C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\apci.dll
2025-07-03 07:31:42.460 [Information] VocomNativeInterop_Patch: DLL file size: 1165312 bytes, Last modified: 2/19/2019 5:58:52 AM
2025-07-03 07:31:42.461 [Error] PatchedVocomDeviceDriver: Failed to initialize patched Vocom native interop layer
2025-07-03 07:31:42.461 [Warning] PatchedVocomServiceFactory: Patched driver initialization failed, falling back to standard driver
2025-07-03 07:31:42.462 [Information] VocomDriver: Initializing Vocom driver
2025-07-03 07:31:42.463 [Information] VocomNativeInterop: Initializing Vocom driver
2025-07-03 07:31:42.464 [Information] VocomNativeInterop: Searching for Vocom driver DLL in 7 locations
2025-07-03 07:31:42.465 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:31:42.465 [Information] VocomNativeInterop: Found Vocom driver DLL at: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:31:42.466 [Information] WUDFPumaDependencyResolver: Attempting to load WUDFPuma.dll from: C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFPuma.dll
2025-07-03 07:31:42.466 [Information] WUDFPumaDependencyResolver: Set DLL directory to: C:\Program Files (x86)\88890020 Adapter\UMDF
2025-07-03 07:31:42.467 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr120.dll
2025-07-03 07:31:42.468 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcp120.dll
2025-07-03 07:31:42.468 [Warning] WUDFPumaDependencyResolver: Could not load dependency: msvcr140.dll
2025-07-03 07:31:42.469 [Information] WUDFPumaDependencyResolver: Loaded dependency: msvcp140.dll from C:\Windows\system32\msvcp140.dll
2025-07-03 07:31:42.469 [Information] WUDFPumaDependencyResolver: Loaded dependency: vcruntime140.dll from C:\Windows\system32\vcruntime140.dll
2025-07-03 07:31:42.470 [Information] WUDFPumaDependencyResolver: Loaded dependency: api-ms-win-crt-runtime-l1-1-0.dll from api-ms-win-crt-runtime-l1-1-0.dll
2025-07-03 07:31:42.472 [Information] WUDFPumaDependencyResolver: Loaded dependency: WdfCoInstaller01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WdfCoInstaller01009.dll
2025-07-03 07:31:42.475 [Information] WUDFPumaDependencyResolver: Loaded dependency: WUDFUpdate_01009.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\WUDFUpdate_01009.dll
2025-07-03 07:31:42.476 [Information] WUDFPumaDependencyResolver: Loaded dependency: winusbcoinstaller2.dll from C:\Program Files (x86)\88890020 Adapter\UMDF\winusbcoinstaller2.dll
2025-07-03 07:31:42.477 [Information] WUDFPumaDependencyResolver: Successfully loaded WUDFPuma.dll
2025-07-03 07:31:42.477 [Information] VocomNativeInterop: Successfully loaded WUDFPuma.dll with dependencies
2025-07-03 07:31:42.477 [Information] VocomNativeInterop: Loading WUDFPuma function pointers
2025-07-03 07:31:42.478 [Information] VocomNativeInterop: Attempting to enumerate WUDFPuma.dll functions
2025-07-03 07:31:42.478 [Warning] WUDFPumaDependencyResolver: Function DllMain not found in WUDFPuma.dll
2025-07-03 07:31:42.479 [Warning] WUDFPumaDependencyResolver: Function DllCanUnloadNow not found in WUDFPuma.dll
2025-07-03 07:31:42.479 [Warning] WUDFPumaDependencyResolver: Function DllGetVersion not found in WUDFPuma.dll
2025-07-03 07:31:42.479 [Warning] WUDFPumaDependencyResolver: Function DllRegisterServer not found in WUDFPuma.dll
2025-07-03 07:31:42.479 [Warning] WUDFPumaDependencyResolver: Function DllUnregisterServer not found in WUDFPuma.dll
2025-07-03 07:31:42.479 [Warning] WUDFPumaDependencyResolver: Function DriverEntry not found in WUDFPuma.dll
2025-07-03 07:31:42.480 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverEntry not found in WUDFPuma.dll
2025-07-03 07:31:42.480 [Warning] WUDFPumaDependencyResolver: Function WUDFDriverUnload not found in WUDFPuma.dll
2025-07-03 07:31:42.480 [Warning] WUDFPumaDependencyResolver: Function WUDFObjectContextGetObject not found in WUDFPuma.dll
2025-07-03 07:31:42.480 [Warning] WUDFPumaDependencyResolver: Function Initialize not found in WUDFPuma.dll
2025-07-03 07:31:42.481 [Warning] WUDFPumaDependencyResolver: Function Cleanup not found in WUDFPuma.dll
2025-07-03 07:31:42.481 [Warning] WUDFPumaDependencyResolver: Function Open not found in WUDFPuma.dll
2025-07-03 07:31:42.481 [Warning] WUDFPumaDependencyResolver: Function Close not found in WUDFPuma.dll
2025-07-03 07:31:42.481 [Warning] WUDFPumaDependencyResolver: Function Read not found in WUDFPuma.dll
2025-07-03 07:31:42.481 [Warning] WUDFPumaDependencyResolver: Function Write not found in WUDFPuma.dll
2025-07-03 07:31:42.481 [Warning] WUDFPumaDependencyResolver: Function Control not found in WUDFPuma.dll
2025-07-03 07:31:42.482 [Warning] WUDFPumaDependencyResolver: Function IoControl not found in WUDFPuma.dll
2025-07-03 07:31:42.482 [Warning] WUDFPumaDependencyResolver: Function DeviceIoControl not found in WUDFPuma.dll
2025-07-03 07:31:42.482 [Warning] WUDFPumaDependencyResolver: Function CreateFile not found in WUDFPuma.dll
2025-07-03 07:31:42.482 [Warning] WUDFPumaDependencyResolver: Function ReadFile not found in WUDFPuma.dll
2025-07-03 07:31:42.482 [Warning] WUDFPumaDependencyResolver: Function WriteFile not found in WUDFPuma.dll
2025-07-03 07:31:42.483 [Warning] WUDFPumaDependencyResolver: Function CloseHandle not found in WUDFPuma.dll
2025-07-03 07:31:42.483 [Warning] WUDFPumaDependencyResolver: Function GetDeviceList not found in WUDFPuma.dll
2025-07-03 07:31:42.483 [Warning] WUDFPumaDependencyResolver: Function OpenDevice not found in WUDFPuma.dll
2025-07-03 07:31:42.483 [Warning] WUDFPumaDependencyResolver: Function CloseDevice not found in WUDFPuma.dll
2025-07-03 07:31:42.483 [Warning] WUDFPumaDependencyResolver: Function SendCommand not found in WUDFPuma.dll
2025-07-03 07:31:42.483 [Warning] WUDFPumaDependencyResolver: Function ReceiveData not found in WUDFPuma.dll
2025-07-03 07:31:42.484 [Warning] WUDFPumaDependencyResolver: Function Connect not found in WUDFPuma.dll
2025-07-03 07:31:42.484 [Warning] WUDFPumaDependencyResolver: Function Disconnect not found in WUDFPuma.dll
2025-07-03 07:31:42.484 [Warning] WUDFPumaDependencyResolver: Function Send not found in WUDFPuma.dll
2025-07-03 07:31:42.484 [Warning] WUDFPumaDependencyResolver: Function Receive not found in WUDFPuma.dll
2025-07-03 07:31:42.484 [Warning] WUDFPumaDependencyResolver: Function Transmit not found in WUDFPuma.dll
2025-07-03 07:31:42.484 [Warning] WUDFPumaDependencyResolver: Function Transfer not found in WUDFPuma.dll
2025-07-03 07:31:42.485 [Warning] WUDFPumaDependencyResolver: Function GetStatus not found in WUDFPuma.dll
2025-07-03 07:31:42.485 [Warning] WUDFPumaDependencyResolver: Function GetInfo not found in WUDFPuma.dll
2025-07-03 07:31:42.485 [Warning] WUDFPumaDependencyResolver: Function SetConfig not found in WUDFPuma.dll
2025-07-03 07:31:42.485 [Warning] WUDFPumaDependencyResolver: Function GetConfig not found in WUDFPuma.dll
2025-07-03 07:31:42.485 [Warning] WUDFPumaDependencyResolver: Function Reset not found in WUDFPuma.dll
2025-07-03 07:31:42.486 [Warning] WUDFPumaDependencyResolver: Function Start not found in WUDFPuma.dll
2025-07-03 07:31:42.486 [Warning] WUDFPumaDependencyResolver: Function Stop not found in WUDFPuma.dll
2025-07-03 07:31:42.486 [Information] VocomNativeInterop: Found 0 functions in WUDFPuma.dll
2025-07-03 07:31:42.486 [Information] VocomNativeInterop: WUDFPuma.dll is a WUDF driver - using Windows Device API approach
2025-07-03 07:31:42.487 [Information] VocomNativeInterop: Initializing Windows Device API for Vocom WUDF driver
2025-07-03 07:31:42.487 [Information] VocomNativeInterop: Enumerating Vocom devices using Windows Device APIs
2025-07-03 07:31:42.488 [Information] VocomNativeInterop: Device enumeration complete, found 0 devices
2025-07-03 07:31:42.488 [Information] VocomNativeInterop: No Vocom devices found via Windows Device API - this is normal when no physical device is connected
2025-07-03 07:31:42.488 [Information] VocomNativeInterop: Windows Device API initialization completed - will use when real device is connected
2025-07-03 07:31:42.489 [Information] VocomNativeInterop: Successfully loaded WUDFPuma function pointers
2025-07-03 07:31:42.489 [Information] VocomNativeInterop: Vocom driver initialized successfully
2025-07-03 07:31:42.489 [Information] VocomDriver: Vocom driver initialized successfully
2025-07-03 07:31:42.491 [Information] VocomService: Initializing Vocom service with dependencies
2025-07-03 07:31:42.492 [Information] ModernUSBCommunicationService: Initializing modern USB communication service
2025-07-03 07:31:42.493 [Information] WiFiCommunicationService: Initializing WiFi communication service
2025-07-03 07:31:42.494 [Information] WiFiCommunicationService: Checking if WiFi is available
2025-07-03 07:31:42.529 [Information] WiFiCommunicationService: WiFi is available
2025-07-03 07:31:42.530 [Information] WiFiCommunicationService: WiFi communication service initialized successfully
2025-07-03 07:31:42.531 [Information] BluetoothCommunicationService: Initializing Bluetooth communication service
2025-07-03 07:31:42.532 [Information] BluetoothCommunicationService: Checking if Bluetooth is available
2025-07-03 07:31:42.533 [Information] BluetoothCommunicationService: Bluetooth is available
2025-07-03 07:31:42.534 [Information] BluetoothCommunicationService: Bluetooth communication service initialized successfully
2025-07-03 07:31:42.535 [Information] VocomService: Initializing Vocom service
2025-07-03 07:31:42.536 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:42.546 [Information] VocomService: PTT application is not running
2025-07-03 07:31:42.548 [Information] VocomService: Vocom service initialized successfully
2025-07-03 07:31:42.549 [Information] PatchedVocomServiceFactory: Vocom service created and initialized successfully with real driver
2025-07-03 07:31:42.549 [Information] App: Initializing Vocom service
2025-07-03 07:31:42.549 [Information] VocomService: Initializing Vocom service
2025-07-03 07:31:42.549 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:42.559 [Information] VocomService: PTT application is not running
2025-07-03 07:31:42.560 [Information] VocomService: Vocom service initialized successfully
2025-07-03 07:31:42.562 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:31:42.562 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:31:42.563 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:31:42.564 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:31:42.567 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:31:42.937 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:31:42.940 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:31:42.943 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:31:42.944 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:31:42.945 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:31:42.945 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:31:42.945 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:31:42.946 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:31:42.948 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:31:42.949 [Information] App: Found 4 Vocom devices, attempting to connect to the first one
2025-07-03 07:31:42.951 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:31:42.951 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:42.960 [Information] VocomService: PTT application is not running
2025-07-03 07:31:42.962 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:31:42.962 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:31:42.963 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:42.973 [Information] VocomService: PTT application is not running
2025-07-03 07:31:42.973 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:31:42.995 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:31:42.997 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:31:42.997 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:31:42.999 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:31:42.999 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:31:43.000 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:31:43.000 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:31:43.000 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.000 [Warning] App: Failed to connect to Vocom device, continuing without a connected device
2025-07-03 07:31:43.002 [Information] ECUCommunicationServiceFactory: Creating ECU communication service
2025-07-03 07:31:43.005 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:43.006 [Information] ECUCommunicationServiceFactory: Initializing ECU communication service (attempt 1/3)
2025-07-03 07:31:43.009 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 07:31:43.011 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 07:31:43.011 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 07:31:43.013 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 07:31:43.015 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 07:31:43.021 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 07:31:43.023 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 07:31:43.026 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 07:31:43.032 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:31:43.035 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:31:43.035 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:31:43.037 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:31:43.038 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:31:43.038 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:31:43.039 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:31:43.039 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:31:43.040 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:31:43.041 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:31:43.042 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:31:43.042 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:31:43.044 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:31:43.044 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:31:43.044 [Error] BaseECUProtocolHandler: Vocom device is not connected
2025-07-03 07:31:43.045 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 07:31:43.049 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 07:31:43.050 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:43.051 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 07:31:43.051 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 07:31:43.052 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:43.052 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 07:31:43.052 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 07:31:43.052 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:43.053 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 07:31:43.053 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 07:31:43.053 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:43.054 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 07:31:43.054 [Warning] ECUCommunicationService: Vocom adapter is not connected or not ready
2025-07-03 07:31:43.054 [Information] ECUCommunicationService: Attempting to find and connect to a Vocom adapter
2025-07-03 07:31:43.056 [Information] VocomService: Attempting to connect to the first available Vocom device
2025-07-03 07:31:43.057 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:31:43.057 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:31:43.057 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:31:43.057 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:31:43.058 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:31:43.305 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:31:43.306 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:31:43.306 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:31:43.306 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:31:43.307 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:31:43.307 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:31:43.307 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:31:43.307 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:31:43.308 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:31:43.308 [Information] VocomService: Attempting to connect to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.309 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.309 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:43.322 [Information] VocomService: PTT application is not running
2025-07-03 07:31:43.322 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.322 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:31:43.323 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:43.335 [Information] VocomService: PTT application is not running
2025-07-03 07:31:43.336 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:31:43.336 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:31:43.336 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:31:43.336 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:31:43.337 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:31:43.337 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:31:43.337 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:31:43.337 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:31:43.338 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:31:43.338 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.338 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.338 [Warning] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:31:43.339 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-DRIVER via USB
2025-07-03 07:31:43.339 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-03 07:31:43.339 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:43.354 [Information] VocomService: PTT application is not running
2025-07-03 07:31:43.354 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-03 07:31:43.355 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-03 07:31:43.355 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:43.367 [Information] VocomService: PTT application is not running
2025-07-03 07:31:43.367 [Information] VocomService: Using USB communication service to connect to WUDFPuma Driver
2025-07-03 07:31:43.367 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:31:43.368 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-03 07:31:43.368 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:31:43.368 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:31:43.368 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:31:43.369 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:31:43.369 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:31:43.369 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:31:43.369 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:31:43.370 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:31:43.370 [Warning] VocomService: Failed to connect to alternative Vocom device 88890300-DRIVER via USB
2025-07-03 07:31:43.370 [Information] VocomService: Attempting to connect to alternative Vocom device 88890300-BT via Bluetooth
2025-07-03 07:31:43.370 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:31:43.371 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:43.383 [Information] VocomService: PTT application is not running
2025-07-03 07:31:43.385 [Information] VocomService: Connecting to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:31:43.387 [Information] VocomService: Using Bluetooth address: 00:11:22:33:44:55
2025-07-03 07:31:44.189 [Information] VocomService: Successfully connected to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:31:44.190 [Information] VocomService: Connected to Vocom device 88890300-BT via Bluetooth
2025-07-03 07:31:44.190 [Information] ECUCommunicationService: Vocom device connected: 88890300-BT
2025-07-03 07:31:44.190 [Information] VocomService: Successfully connected to alternative Vocom device 88890300-BT via Bluetooth
2025-07-03 07:31:44.191 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 07:31:44.191 [Information] ECUCommunicationServiceFactory: ECU communication service created and initialized successfully
2025-07-03 07:31:44.195 [Information] BackupServiceFactory: Creating backup service with predefined categories and tags
2025-07-03 07:31:44.196 [Information] BackupServiceFactory: Creating backup service with custom settings
2025-07-03 07:31:44.198 [Information] BackupService: Initializing backup service
2025-07-03 07:31:44.198 [Information] BackupService: Backup service initialized successfully
2025-07-03 07:31:44.199 [Information] BackupServiceFactory: Backup service created with custom settings (Directory: Backups, Compression: True, Encryption: False) and initialized successfully
2025-07-03 07:31:44.199 [Information] BackupServiceFactory: Setting up 5 predefined categories
2025-07-03 07:31:44.200 [Information] BackupService: Saving backup to file Backups\Templates\template_Production.backup
2025-07-03 07:31:44.221 [Information] BackupService: Compressing backup data
2025-07-03 07:31:44.232 [Information] BackupService: Backup saved to file Backups\Templates\template_Production.backup (449 bytes)
2025-07-03 07:31:44.233 [Information] BackupServiceFactory: Created template for category: Production
2025-07-03 07:31:44.233 [Information] BackupService: Saving backup to file Backups\Templates\template_Development.backup
2025-07-03 07:31:44.233 [Information] BackupService: Compressing backup data
2025-07-03 07:31:44.234 [Information] BackupService: Backup saved to file Backups\Templates\template_Development.backup (451 bytes)
2025-07-03 07:31:44.235 [Information] BackupServiceFactory: Created template for category: Development
2025-07-03 07:31:44.235 [Information] BackupService: Saving backup to file Backups\Templates\template_Testing.backup
2025-07-03 07:31:44.236 [Information] BackupService: Compressing backup data
2025-07-03 07:31:44.236 [Information] BackupService: Backup saved to file Backups\Templates\template_Testing.backup (447 bytes)
2025-07-03 07:31:44.237 [Information] BackupServiceFactory: Created template for category: Testing
2025-07-03 07:31:44.237 [Information] BackupService: Saving backup to file Backups\Templates\template_Archived.backup
2025-07-03 07:31:44.237 [Information] BackupService: Compressing backup data
2025-07-03 07:31:44.238 [Information] BackupService: Backup saved to file Backups\Templates\template_Archived.backup (447 bytes)
2025-07-03 07:31:44.238 [Information] BackupServiceFactory: Created template for category: Archived
2025-07-03 07:31:44.239 [Information] BackupService: Saving backup to file Backups\Templates\template_Critical.backup
2025-07-03 07:31:44.239 [Information] BackupService: Compressing backup data
2025-07-03 07:31:44.240 [Information] BackupService: Backup saved to file Backups\Templates\template_Critical.backup (449 bytes)
2025-07-03 07:31:44.240 [Information] BackupServiceFactory: Created template for category: Critical
2025-07-03 07:31:44.240 [Information] BackupServiceFactory: Setting up 7 predefined tags
2025-07-03 07:31:44.241 [Information] BackupService: Saving backup to file Backups\Templates\template_tags.backup
2025-07-03 07:31:44.241 [Information] BackupService: Compressing backup data
2025-07-03 07:31:44.242 [Information] BackupService: Backup saved to file Backups\Templates\template_tags.backup (510 bytes)
2025-07-03 07:31:44.242 [Information] BackupServiceFactory: Created template with predefined tags
2025-07-03 07:31:44.242 [Information] BackupServiceFactory: Backup service with predefined categories and tags created successfully
2025-07-03 07:31:44.243 [Information] BackupSchedulerServiceFactory: Creating backup scheduler service with default settings
2025-07-03 07:31:44.246 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 07:31:44.247 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 07:31:44.291 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-03 07:31:44.292 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 07:31:44.293 [Information] BackupSchedulerService: Starting backup scheduler
2025-07-03 07:31:44.293 [Information] BackupSchedulerService: Backup scheduler started successfully
2025-07-03 07:31:44.293 [Information] BackupSchedulerServiceFactory: Backup scheduler service created, initialized, and started successfully
2025-07-03 07:31:44.294 [Information] FlashOperationMonitorService: Initializing FlashOperationMonitorService
2025-07-03 07:31:44.294 [Information] FlashOperationMonitor: Flash operation monitoring started with interval 1000ms
2025-07-03 07:31:44.297 [Information] FlashOperationMonitorService: FlashOperationMonitorService initialized successfully
2025-07-03 07:31:44.297 [Information] App: Flash operation monitor service initialized successfully
2025-07-03 07:31:44.303 [Information] LicensingService: Initializing licensing service
2025-07-03 07:31:44.361 [Error] LicensingService: Error loading license information Exception: Padding is invalid and cannot be removed. StackTrace:    at System.Security.Cryptography.SymmetricPadding.GetPaddingLength(ReadOnlySpan`1 block, PaddingMode paddingMode, Int32 blockSize)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(ReadOnlySpan`1 inputBuffer, Span`1 outputBuffer)
   at System.Security.Cryptography.UniversalCryptoDecryptor.UncheckedTransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.UniversalCryptoTransform.TransformFinalBlock(Byte[] inputBuffer, Int32 inputOffset, Int32 inputCount)
   at System.Security.Cryptography.CryptoStream.ReadAsyncCore(Memory`1 buffer, CancellationToken cancellationToken, Boolean useAsync)
   at System.Security.Cryptography.CryptoStream.Read(Byte[] buffer, Int32 offset, Int32 count)
   at System.IO.StreamReader.ReadBuffer()
   at System.IO.StreamReader.ReadToEnd()
   at VolvoFlashWR.Core.Services.LicensingService.DecryptData(Byte[] encryptedData) in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 424
   at VolvoFlashWR.Core.Services.LicensingService.LoadLicenseInfoAsync() in D:\001-Software Engineering\S.A.H\S.A.H.VolvoFlashWR\VolvoFlashWR.Core\Services\LicensingService.cs:line 333
2025-07-03 07:31:44.363 [Information] LicensingService: Licensing service initialized. Status: Trial
2025-07-03 07:31:44.363 [Information] App: Licensing service initialized successfully
2025-07-03 07:31:44.364 [Information] App: License status: Trial
2025-07-03 07:31:44.364 [Information] App: Trial period: 30 days remaining
2025-07-03 07:31:44.365 [Information] BackupSchedulerService: Getting all backup schedules
2025-07-03 07:31:44.518 [Information] VocomService: Initializing Vocom service
2025-07-03 07:31:44.519 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:31:44.528 [Information] VocomService: PTT application is not running
2025-07-03 07:31:44.528 [Information] VocomService: Vocom service initialized successfully
2025-07-03 07:31:44.579 [Information] ECUCommunicationService: Initializing ECU communication service
2025-07-03 07:31:44.579 [Information] ECUCommunicationService: Loading MC9S12XEP100 configuration
2025-07-03 07:31:44.579 [Information] ECUCommunicationService: MC9S12XEP100 configuration loaded successfully from configuration class
2025-07-03 07:31:44.580 [Information] ProtocolHandlerFactory: Initializing protocol handler factory
2025-07-03 07:31:44.580 [Information] ProtocolHandlerFactory: Loading MC9S12XEP100 configuration
2025-07-03 07:31:44.581 [Information] ProtocolHandlerFactory: MC9S12XEP100 configuration loaded successfully
2025-07-03 07:31:44.581 [Information] ProtocolHandlerFactory: Loading Vocom configuration
2025-07-03 07:31:44.582 [Information] ProtocolHandlerFactory: Vocom configuration loaded successfully
2025-07-03 07:31:44.582 [Information] CANProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:31:44.583 [Information] BaseECUProtocolHandler: Initializing CAN protocol handler
2025-07-03 07:31:44.594 [Information] BaseECUProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 07:31:44.595 [Information] CANProtocolHandler: Configuring CAN controller registers for MC9S12XEP100
2025-07-03 07:31:44.596 [Information] CANProtocolHandler: Using default CAN configuration with 13 parameters
2025-07-03 07:31:44.596 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL0 = 0x01
2025-07-03 07:31:44.596 [Information] CANProtocolHandler: CAN config: Register_CAN0_CTL1 = 0x80
2025-07-03 07:31:44.596 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR0 = 0x03
2025-07-03 07:31:44.597 [Information] CANProtocolHandler: CAN config: Register_CAN0_BTR1 = 0x2C
2025-07-03 07:31:44.597 [Information] CANProtocolHandler: CAN config: BaudRate = 500000
2025-07-03 07:31:44.597 [Information] CANProtocolHandler: CAN config: LowSpeedBaudRate = 125000
2025-07-03 07:31:44.598 [Information] CANProtocolHandler: CAN config: SamplePoint = 0.75
2025-07-03 07:31:44.598 [Information] CANProtocolHandler: CAN config: SJW = 1
2025-07-03 07:31:44.599 [Information] CANProtocolHandler: CAN config: TSEG1 = 12
2025-07-03 07:31:44.599 [Information] CANProtocolHandler: CAN config: TSEG2 = 2
2025-07-03 07:31:44.599 [Information] CANProtocolHandler: CAN config: Prescaler = 4
2025-07-03 07:31:44.599 [Information] CANProtocolHandler: CAN config: MaxRetries = 3
2025-07-03 07:31:44.600 [Information] CANProtocolHandler: CAN config: TimeoutMs = 1000
2025-07-03 07:31:44.600 [Information] CANProtocolHandler: Requesting CAN initialization mode
2025-07-03 07:31:44.603 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0140
2025-07-03 07:31:44.608 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0140 (simulated)
2025-07-03 07:31:44.609 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be active
2025-07-03 07:31:44.611 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be set
2025-07-03 07:31:44.612 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:31:44.620 [Information] CANRegisterAccess: Read value 0x0F from register 0x0141 (simulated)
2025-07-03 07:31:44.621 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now set
2025-07-03 07:31:44.622 [Information] CANProtocolHandler: Configuring CAN bus timing registers for high-speed communication (500000 bps)
2025-07-03 07:31:44.622 [Information] CANRegisterAccess: Writing value 0x01 to register 0x0142
2025-07-03 07:31:44.629 [Information] CANRegisterAccess: Wrote value 0x01 to register 0x0142 (simulated)
2025-07-03 07:31:44.629 [Information] CANRegisterAccess: Writing value 0x1C to register 0x0143
2025-07-03 07:31:44.635 [Information] CANRegisterAccess: Wrote value 0x1C to register 0x0143 (simulated)
2025-07-03 07:31:44.636 [Information] CANProtocolHandler: Configuring CAN control register 1
2025-07-03 07:31:44.637 [Information] CANRegisterAccess: Writing value 0x80 to register 0x0141
2025-07-03 07:31:44.642 [Information] CANRegisterAccess: Wrote value 0x80 to register 0x0141 (simulated)
2025-07-03 07:31:44.642 [Information] CANProtocolHandler: Configuring CAN identifier acceptance registers
2025-07-03 07:31:44.642 [Information] CANRegisterAccess: Writing value 0x00 to register 0x014B
2025-07-03 07:31:44.648 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x014B (simulated)
2025-07-03 07:31:44.648 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0010
2025-07-03 07:31:44.654 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0010 (simulated)
2025-07-03 07:31:44.654 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0014
2025-07-03 07:31:44.660 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0014 (simulated)
2025-07-03 07:31:44.660 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0011
2025-07-03 07:31:44.667 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0011 (simulated)
2025-07-03 07:31:44.667 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0015
2025-07-03 07:31:44.672 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0015 (simulated)
2025-07-03 07:31:44.672 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0012
2025-07-03 07:31:44.678 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0012 (simulated)
2025-07-03 07:31:44.678 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0016
2025-07-03 07:31:44.683 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0016 (simulated)
2025-07-03 07:31:44.684 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0013
2025-07-03 07:31:44.691 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0013 (simulated)
2025-07-03 07:31:44.691 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0017
2025-07-03 07:31:44.697 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0017 (simulated)
2025-07-03 07:31:44.697 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0014
2025-07-03 07:31:44.702 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0014 (simulated)
2025-07-03 07:31:44.703 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0018
2025-07-03 07:31:44.710 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0018 (simulated)
2025-07-03 07:31:44.710 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0015
2025-07-03 07:31:44.714 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0015 (simulated)
2025-07-03 07:31:44.715 [Information] CANRegisterAccess: Writing value 0xFF to register 0x0019
2025-07-03 07:31:44.721 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x0019 (simulated)
2025-07-03 07:31:44.721 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0016
2025-07-03 07:31:44.726 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0016 (simulated)
2025-07-03 07:31:44.727 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001A
2025-07-03 07:31:44.731 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001A (simulated)
2025-07-03 07:31:44.732 [Information] CANRegisterAccess: Writing value 0x00 to register 0x0017
2025-07-03 07:31:44.737 [Information] CANRegisterAccess: Wrote value 0x00 to register 0x0017 (simulated)
2025-07-03 07:31:44.738 [Information] CANRegisterAccess: Writing value 0xFF to register 0x001B
2025-07-03 07:31:44.743 [Information] CANRegisterAccess: Wrote value 0xFF to register 0x001B (simulated)
2025-07-03 07:31:44.744 [Information] CANProtocolHandler: Exiting CAN initialization mode
2025-07-03 07:31:44.744 [Information] CANRegisterAccess: Writing value 0x0C to register 0x0140
2025-07-03 07:31:44.749 [Information] CANRegisterAccess: Wrote value 0x0C to register 0x0140 (simulated)
2025-07-03 07:31:44.750 [Information] CANProtocolHandler: Waiting for CAN initialization mode to be inactive
2025-07-03 07:31:44.750 [Information] CANRegisterAccess: Waiting for bit 0x01 in register 0x0141 to be clear
2025-07-03 07:31:44.750 [Information] CANRegisterAccess: Reading byte from register 0x0141
2025-07-03 07:31:44.755 [Information] CANRegisterAccess: Read value 0x28 from register 0x0141 (simulated)
2025-07-03 07:31:44.756 [Information] CANRegisterAccess: Bit 0x01 in register 0x0141 is now clear
2025-07-03 07:31:44.756 [Information] CANProtocolHandler: Waiting for CAN synchronization
2025-07-03 07:31:44.756 [Information] CANRegisterAccess: Waiting for bit 0x10 in register 0x0140 to be set
2025-07-03 07:31:44.756 [Information] CANRegisterAccess: Reading byte from register 0x0140
2025-07-03 07:31:44.763 [Information] CANRegisterAccess: Read value 0x19 from register 0x0140 (simulated)
2025-07-03 07:31:44.763 [Information] CANRegisterAccess: Bit 0x10 in register 0x0140 is now set
2025-07-03 07:31:44.763 [Information] CANProtocolHandler: CAN protocol handler initialized successfully
2025-07-03 07:31:44.764 [Information] SPIProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:31:44.764 [Information] BaseECUProtocolHandler: Initializing SPI protocol handler
2025-07-03 07:31:44.774 [Information] BaseECUProtocolHandler: SPI protocol handler initialized successfully
2025-07-03 07:31:44.775 [Information] SPIProtocolHandler: Configuring SPI controller registers for MC9S12XEP100
2025-07-03 07:31:44.775 [Information] SPIProtocolHandler: Writing 0x50 to SPI Control Register 1 (0x00F8)
2025-07-03 07:31:44.778 [Information] VocomService: Sending data and waiting for response
2025-07-03 07:31:44.779 [Information] VocomService: Using dummy implementation for SendAndReceiveDataAsync
2025-07-03 07:31:44.830 [Information] VocomService: Sent 4 bytes and received 6 bytes response (simulated)
2025-07-03 07:31:44.831 [Error] SPIProtocolHandler: Failed to write value 0x50 to register 0x00F8
2025-07-03 07:31:44.831 [Error] SPIProtocolHandler: Failed to write to SPI Control Register 1
2025-07-03 07:31:44.831 [Information] SCIProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:31:44.832 [Information] BaseECUProtocolHandler: Initializing SCI protocol handler
2025-07-03 07:31:44.842 [Information] BaseECUProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 07:31:44.843 [Information] SCIProtocolHandler: Configuring SCI controller registers for MC9S12XEP100
2025-07-03 07:31:44.843 [Information] SCIProtocolHandler: Configuring SCI baud rate for 9600 baud
2025-07-03 07:31:44.853 [Information] SCIProtocolHandler: Configuring SCI control register 1
2025-07-03 07:31:44.864 [Information] SCIProtocolHandler: Configuring SCI control register 2
2025-07-03 07:31:44.875 [Information] SCIProtocolHandler: Verifying SCI status registers
2025-07-03 07:31:44.886 [Information] SCIProtocolHandler: Flushing SCI data registers
2025-07-03 07:31:44.896 [Information] SCIProtocolHandler: SCI protocol handler initialized successfully
2025-07-03 07:31:44.897 [Information] IICProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:31:44.897 [Information] BaseECUProtocolHandler: Initializing IIC protocol handler
2025-07-03 07:31:44.908 [Information] BaseECUProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 07:31:44.909 [Information] IICProtocolHandler: Configuring IIC controller registers for MC9S12XEP100
2025-07-03 07:31:44.909 [Information] IICProtocolHandler: Checking IIC bus status
2025-07-03 07:31:44.921 [Information] IICProtocolHandler: Setting IIC bus address for slave mode
2025-07-03 07:31:44.932 [Information] IICProtocolHandler: Configuring IIC bus frequency divider for 100 kHz operation
2025-07-03 07:31:44.943 [Information] IICProtocolHandler: Enabling IIC module
2025-07-03 07:31:44.954 [Information] IICProtocolHandler: Clearing any pending IIC interrupts
2025-07-03 07:31:44.965 [Information] IICProtocolHandler: Verifying IIC module initialization
2025-07-03 07:31:44.976 [Information] IICProtocolHandler: IIC protocol handler initialized successfully
2025-07-03 07:31:44.976 [Information] J1939ProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:31:44.977 [Information] BaseECUProtocolHandler: Initializing J1939 protocol handler
2025-07-03 07:31:44.988 [Information] BaseECUProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 07:31:44.989 [Information] J1939ProtocolHandler: Using default J1939 configuration with 15 parameters
2025-07-03 07:31:44.989 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL0 = 0x01
2025-07-03 07:31:44.989 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_CTL1 = 0x80
2025-07-03 07:31:44.989 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR0 = 0x07
2025-07-03 07:31:44.989 [Information] J1939ProtocolHandler: J1939 config: Register_CAN0_BTR1 = 0x2C
2025-07-03 07:31:44.990 [Information] J1939ProtocolHandler: J1939 config: BaudRate = 250000
2025-07-03 07:31:44.990 [Information] J1939ProtocolHandler: J1939 config: SamplePoint = 0.75
2025-07-03 07:31:44.990 [Information] J1939ProtocolHandler: J1939 config: SJW = 1
2025-07-03 07:31:44.990 [Information] J1939ProtocolHandler: J1939 config: TSEG1 = 12
2025-07-03 07:31:44.990 [Information] J1939ProtocolHandler: J1939 config: TSEG2 = 2
2025-07-03 07:31:44.990 [Information] J1939ProtocolHandler: J1939 config: Prescaler = 8
2025-07-03 07:31:44.991 [Information] J1939ProtocolHandler: J1939 config: MaxRetries = 3
2025-07-03 07:31:44.991 [Information] J1939ProtocolHandler: J1939 config: TimeoutMs = 2000
2025-07-03 07:31:44.991 [Information] J1939ProtocolHandler: J1939 config: ExtendedFrames = True
2025-07-03 07:31:44.991 [Information] J1939ProtocolHandler: J1939 config: SourceAddress = 249
2025-07-03 07:31:44.991 [Information] J1939ProtocolHandler: J1939 config: PGN = 65226
2025-07-03 07:31:45.092 [Information] J1939ProtocolHandler: J1939 protocol handler initialized successfully
2025-07-03 07:31:45.092 [Information] ProtocolHandlerFactory: Protocol handler factory initialized successfully
2025-07-03 07:31:45.092 [Information] ECUCommunicationService: CAN protocol handler obtained from factory
2025-07-03 07:31:45.093 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:45.093 [Information] ECUCommunicationService: MC9S12XEP100 integration for CAN protocol initialized successfully
2025-07-03 07:31:45.093 [Information] ECUCommunicationService: SPI protocol handler obtained from factory
2025-07-03 07:31:45.093 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:45.093 [Information] ECUCommunicationService: MC9S12XEP100 integration for SPI protocol initialized successfully
2025-07-03 07:31:45.094 [Information] ECUCommunicationService: SCI protocol handler obtained from factory
2025-07-03 07:31:45.094 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:45.094 [Information] ECUCommunicationService: MC9S12XEP100 integration for SCI protocol initialized successfully
2025-07-03 07:31:45.094 [Information] ECUCommunicationService: IIC protocol handler obtained from factory
2025-07-03 07:31:45.094 [Information] MC9S12XEP100Helper: No flash health status file found, using default values
2025-07-03 07:31:45.094 [Information] ECUCommunicationService: MC9S12XEP100 integration for IIC protocol initialized successfully
2025-07-03 07:31:45.095 [Information] ECUCommunicationService: ECU communication service initialized successfully
2025-07-03 07:31:45.145 [Information] BackupService: Initializing backup service
2025-07-03 07:31:45.146 [Information] BackupService: Backup service initialized successfully
2025-07-03 07:31:45.200 [Information] BackupSchedulerService: Initializing backup scheduler service
2025-07-03 07:31:45.200 [Information] BackupSchedulerService: Loading backup schedules from disk
2025-07-03 07:31:45.201 [Information] BackupSchedulerService: Loaded 2 backup schedules from C:\Users\<USER>\Desktop\VolvoFlashWR_Complete_Integrated_Build\VolvoFlashWR_Complete_Integrated_Build\Application\Schedules\backup_schedules.json
2025-07-03 07:31:45.202 [Information] BackupSchedulerService: Backup scheduler service initialized successfully
2025-07-03 07:31:45.254 [Information] BackupService: Getting predefined backup categories
2025-07-03 07:31:45.305 [Information] MainViewModel: Services initialized successfully
2025-07-03 07:31:45.307 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 07:31:45.308 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:31:45.308 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:31:45.308 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:31:45.309 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:31:45.309 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:31:45.493 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:31:45.494 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:31:45.494 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:31:45.494 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:31:45.494 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:31:45.494 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:31:45.495 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:31:45.495 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:31:45.496 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:31:45.497 [Information] MainViewModel: Found 4 Vocom device(s)
2025-07-03 07:32:03.095 [Information] MainViewModel: Connecting to Vocom device 88890300-USB
2025-07-03 07:32:03.096 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.097 [Information] VocomService: Disconnecting from Vocom device 88890300-BT
2025-07-03 07:32:03.098 [Information] VocomService: Disconnecting from Vocom device 88890300-BT via Bluetooth
2025-07-03 07:32:03.514 [Information] VocomService: Successfully disconnected from Vocom device 88890300-BT via Bluetooth
2025-07-03 07:32:03.515 [Information] VocomService: Disconnected from Vocom device 88890300-BT
2025-07-03 07:32:03.515 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-03 07:32:03.517 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-03 07:32:03.518 [Information] ECUCommunicationService: No ECUs are connected
2025-07-03 07:32:03.518 [Information] MainViewModel: Vocom device 88890300-BT disconnected
2025-07-03 07:32:03.518 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-BT
2025-07-03 07:32:03.519 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-03 07:32:03.519 [Information] ECUCommunicationService: No ECUs are connected
2025-07-03 07:32:03.519 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:03.528 [Information] VocomService: PTT application is not running
2025-07-03 07:32:03.528 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.529 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.529 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:03.537 [Information] VocomService: PTT application is not running
2025-07-03 07:32:03.537 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.538 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:32:03.538 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.538 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:32:03.538 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:32:03.538 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:32:03.539 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:32:03.539 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.539 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.540 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.540 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.540 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.541 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:03.541 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.541 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.541 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.542 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.542 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.542 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:03.543 [Error] MainViewModel: Failed to connect to Vocom device 88890300-USB
2025-07-03 07:32:10.921 [Information] MainViewModel: Connecting to Vocom device 88890300-DRIVER
2025-07-03 07:32:10.922 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.922 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:10.930 [Information] VocomService: PTT application is not running
2025-07-03 07:32:10.931 [Information] VocomService: Connecting to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.931 [Information] VocomService: Using USB port: WUDFPuma Driver
2025-07-03 07:32:10.931 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:10.940 [Information] VocomService: PTT application is not running
2025-07-03 07:32:10.940 [Information] VocomService: Using USB communication service to connect to WUDFPuma Driver
2025-07-03 07:32:10.941 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:32:10.941 [Information] ModernUSBCommunicationService: Connecting to device: WUDFPuma Driver
2025-07-03 07:32:10.941 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:32:10.941 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:32:10.942 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:32:10.942 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:32:10.942 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:32:10.942 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:32:10.942 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:32:10.943 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:32:10.943 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:32:10.943 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB port WUDFPuma Driver
2025-07-03 07:32:10.943 [Error] VocomService: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.944 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.944 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.944 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.944 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.944 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-DRIVER via USB
2025-07-03 07:32:10.945 [Error] MainViewModel: Failed to connect to Vocom device 88890300-DRIVER
2025-07-03 07:32:15.346 [Information] MainViewModel: Connecting to Vocom device 88890300-WiFi
2025-07-03 07:32:15.347 [Information] VocomService: Connecting to Vocom device 88890300-WiFi via WiFi
2025-07-03 07:32:15.347 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:15.355 [Information] VocomService: PTT application is not running
2025-07-03 07:32:15.357 [Information] VocomService: Connecting to Vocom device 88890300-WiFi via WiFi
2025-07-03 07:32:15.358 [Information] VocomService: Using WiFi IP address: *************
2025-07-03 07:32:16.353 [Information] VocomService: Successfully connected to Vocom device 88890300-WiFi via WiFi
2025-07-03 07:32:16.354 [Information] VocomService: Connected to Vocom device 88890300-WiFi via WiFi
2025-07-03 07:32:16.354 [Information] ECUCommunicationService: Vocom device connected: 88890300-WiFi
2025-07-03 07:32:16.354 [Information] MainViewModel: Vocom device 88890300-WiFi connected
2025-07-03 07:32:16.355 [Information] ECUCommunicationService: Vocom device connected: 88890300-WiFi
2025-07-03 07:32:16.355 [Information] MainViewModel: Connected to Vocom device 88890300-WiFi
2025-07-03 07:32:16.357 [Information] MainViewModel: Scanning for ECUs
2025-07-03 07:32:16.362 [Information] ECUCommunicationService: Scanning for ECUs
2025-07-03 07:32:16.363 [Information] ECUCommunicationService: Scanning for ECUs on the CAN bus...
2025-07-03 07:32:16.863 [Information] ECUCommunicationService: Scanning for ECUs using SPI protocol...
2025-07-03 07:32:17.172 [Information] ECUCommunicationService: Scanning for ECUs using SCI protocol...
2025-07-03 07:32:17.493 [Information] ECUCommunicationService: Scanning for ECUs using IIC protocol...
2025-07-03 07:32:17.813 [Information] ECUCommunicationService: Found 10 ECUs
2025-07-03 07:32:17.814 [Information] MainViewModel: Found 10 ECU(s)
2025-07-03 07:32:23.405 [Information] MainViewModel: Connecting to Vocom device 88890300-USB
2025-07-03 07:32:23.406 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.406 [Information] VocomService: Disconnecting from Vocom device 88890300-WiFi
2025-07-03 07:32:23.407 [Information] VocomService: Disconnecting from Vocom device 88890300-WiFi via WiFi
2025-07-03 07:32:23.712 [Information] VocomService: Successfully disconnected from Vocom device 88890300-WiFi via WiFi
2025-07-03 07:32:23.713 [Information] VocomService: Disconnected from Vocom device 88890300-WiFi
2025-07-03 07:32:23.713 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-WiFi
2025-07-03 07:32:23.713 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-03 07:32:23.714 [Information] ECUCommunicationService: No ECUs are connected
2025-07-03 07:32:23.714 [Information] MainViewModel: Vocom device 88890300-WiFi disconnected
2025-07-03 07:32:23.714 [Information] ECUCommunicationService: Vocom device disconnected: 88890300-WiFi
2025-07-03 07:32:23.714 [Information] ECUCommunicationService: Disconnecting from all ECUs
2025-07-03 07:32:23.715 [Information] ECUCommunicationService: No ECUs are connected
2025-07-03 07:32:23.715 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:23.724 [Information] VocomService: PTT application is not running
2025-07-03 07:32:23.724 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.724 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.725 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:32:23.735 [Information] VocomService: PTT application is not running
2025-07-03 07:32:23.736 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.736 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:32:23.736 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.736 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:32:23.737 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:32:23.737 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:32:23.737 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:32:23.737 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.738 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.738 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.738 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.739 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.739 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:32:23.739 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.740 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.740 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.740 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.740 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.741 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:32:23.741 [Error] MainViewModel: Failed to connect to Vocom device 88890300-USB
2025-07-03 07:32:38.174 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 07:32:38.176 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:32:38.176 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:32:38.176 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:32:38.177 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:32:38.177 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:32:38.353 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:32:38.354 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:32:38.354 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:32:38.354 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:32:38.354 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:32:38.355 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-03 07:32:38.355 [Information] VocomService: Enhanced detector found 1 real Vocom devices
2025-07-03 07:32:38.358 [Information] VocomService: Found 3 Vocom devices
2025-07-03 07:32:38.359 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-03 07:32:48.125 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 07:32:48.126 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:32:48.126 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:32:48.126 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:32:48.126 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:32:48.127 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:32:48.291 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:32:48.292 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:32:48.292 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:32:48.292 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:32:48.292 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:32:48.293 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 1 Vocom devices
2025-07-03 07:32:48.293 [Information] VocomService: Enhanced detector found 1 real Vocom devices
2025-07-03 07:32:48.295 [Information] VocomService: Found 3 Vocom devices
2025-07-03 07:32:48.296 [Information] MainViewModel: Found 3 Vocom device(s)
2025-07-03 07:33:06.279 [Information] MainViewModel: Scanning for Vocom devices
2025-07-03 07:33:06.280 [Information] VocomService: Scanning for Vocom devices
2025-07-03 07:33:06.281 [Information] VocomService: Using enhanced Vocom device detector for real hardware
2025-07-03 07:33:06.281 [Information] EnhancedVocomDeviceDetector: Starting enhanced Vocom device detection
2025-07-03 07:33:06.281 [Information] EnhancedVocomDeviceDetector: Detecting USB Vocom devices
2025-07-03 07:33:06.281 [Information] EnhancedVocomDeviceDetector: Detecting WMI Vocom devices
2025-07-03 07:33:06.448 [Information] EnhancedVocomDeviceDetector: Found WMI Vocom device: Vocom - 88890300 (USB\VID_178E&PID_0024\**********)
2025-07-03 07:33:06.449 [Information] EnhancedVocomDeviceDetector: Detecting Serial Port Vocom devices
2025-07-03 07:33:06.449 [Information] EnhancedVocomDeviceDetector: Detecting Registry-based Vocom devices
2025-07-03 07:33:06.450 [Information] EnhancedVocomDeviceDetector: Detecting Driver-based Vocom devices
2025-07-03 07:33:06.450 [Information] EnhancedVocomDeviceDetector: Vocom driver found, checking for connected devices
2025-07-03 07:33:06.450 [Information] EnhancedVocomDeviceDetector: Added Vocom driver-based device entry
2025-07-03 07:33:06.450 [Information] EnhancedVocomDeviceDetector: Enhanced detection found 2 Vocom devices
2025-07-03 07:33:06.450 [Information] VocomService: Enhanced detector found 2 real Vocom devices
2025-07-03 07:33:06.452 [Information] VocomService: Found 4 Vocom devices
2025-07-03 07:33:06.453 [Information] MainViewModel: Found 4 Vocom device(s)
2025-07-03 07:33:22.353 [Information] MainViewModel: Connecting to Vocom device 88890300-USB
2025-07-03 07:33:22.354 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.354 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:33:22.364 [Information] VocomService: PTT application is not running
2025-07-03 07:33:22.365 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.365 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.365 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:33:22.374 [Information] VocomService: PTT application is not running
2025-07-03 07:33:22.374 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.375 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:33:22.375 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.375 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:33:22.375 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:33:22.376 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:33:22.376 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:33:22.376 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.376 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.377 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.377 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.377 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.378 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:33:22.378 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.378 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.379 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.379 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.379 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.380 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:33:22.380 [Error] MainViewModel: Failed to connect to Vocom device 88890300-USB
2025-07-03 07:34:01.519 [Information] MainViewModel: Connecting to Vocom device 88890300-USB
2025-07-03 07:34:01.520 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.520 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:34:01.528 [Information] VocomService: PTT application is not running
2025-07-03 07:34:01.528 [Information] VocomService: Connecting to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.528 [Information] VocomService: Using USB port: USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.529 [Information] VocomService: Checking if PTT application is running
2025-07-03 07:34:01.537 [Information] VocomService: PTT application is not running
2025-07-03 07:34:01.538 [Information] VocomService: Using USB communication service to connect to USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.538 [Information] ModernUSBCommunicationService: USB availability check: True
2025-07-03 07:34:01.538 [Information] ModernUSBCommunicationService: Connecting to device: USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.538 [Information] ModernUSBCommunicationService: Attempting modern USB connection to Vocom adapter
2025-07-03 07:34:01.539 [Information] ModernUSBCommunicationService: Searching for Vocom adapter using HidSharp
2025-07-03 07:34:01.539 [Information] ModernUSBCommunicationService: No compatible Vocom HID device found
2025-07-03 07:34:01.539 [Warning] ModernUSBCommunicationService: No Vocom adapter found via USB
2025-07-03 07:34:01.539 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.539 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.540 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.540 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.540 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.540 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB port USB\VID_178E&PID_0024\**********
2025-07-03 07:34:01.541 [Error] VocomService: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.541 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.541 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.541 [Error] MainViewModel: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.541 [Error] ECUCommunicationService: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.542 [Error] MainViewModel: ECU error: Vocom error: Failed to connect to Vocom device 88890300-USB via USB
2025-07-03 07:34:01.542 [Error] MainViewModel: Failed to connect to Vocom device 88890300-USB
