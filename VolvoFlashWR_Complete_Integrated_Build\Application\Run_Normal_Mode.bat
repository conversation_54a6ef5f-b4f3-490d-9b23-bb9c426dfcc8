@echo off
title VolvoFlashWR - Normal Mode (Export Build)
echo === VolvoFlashWR Normal Mode (Export Build) ===
echo.

echo Setting up environment for normal mode...
echo - Adding Libraries to PATH
echo - Adding Vocom drivers to PATH
echo - Configuring for real hardware support
echo.

REM Set environment variables for library loading
set PATH=%~dp0Libraries;%~dp0Drivers\Vocom;%~dp0Libraries\VCRedist;%PATH%
set USE_PATCHED_IMPLEMENTATION=true
set VERBOSE_LOGGING=true
set PHOENIX_VOCOM_ENABLED=true

echo Environment configured successfully
echo.

echo Starting VolvoFlashWR application...
echo - Application will auto-detect Vocom adapters
echo - Check application logs in Logs\ folder for diagnostics
echo - Press Ctrl+C to stop if needed
echo.

"%~dp0VolvoFlashWR.Launcher.exe"

echo.
echo Application closed.
echo Check Logs\ folder for detailed execution logs.
pause
