$dllPath = ".\VolvoFlashWR_Complete_Integrated_Build\Application\VolvoFlashWR.Core.dll"

try {
    $assembly = [System.Reflection.Assembly]::LoadFrom($dllPath)
    Write-Host "Checking VolvoFlashWR.Core.dll for new integrated services..." -ForegroundColor Green
    
    $types = $assembly.GetTypes()
    $integratedTypes = $types | Where-Object { 
        $_.Name -match "IntegratedStartup|DependencyManager|LibraryExtractor|VCRedistBundler" 
    }
    
    if ($integratedTypes.Count -gt 0) {
        Write-Host "`nFound integrated services:" -ForegroundColor Yellow
        foreach ($type in $integratedTypes) {
            Write-Host "  - $($type.Name) ($($type.Namespace))" -ForegroundColor White
        }
    } else {
        Write-Host "`nNo integrated services found in the DLL" -ForegroundColor Red
    }
    
    Write-Host "`nTotal types in assembly: $($types.Count)" -ForegroundColor Cyan
    
} catch {
    Write-Host "Error loading assembly: $($_.Exception.Message)" -ForegroundColor Red
}
